import { z } from "zod";

export const typeEnum = ["month", "quarter", "semiyear", "year"] as const;
export const typeSchema = z.enum(typeEnum);
export type Type = z.infer<typeof typeSchema>;

export const typeEntries: [Type, string][] = [
  ["month", "月报"],
  ["quarter", "季报"],
  ["semiyear", "半年报"],
  ["year", "年报"],
];

export const typeTransform: Record<Type, string> = {
  month: "月报",
  quarter: "季报",
  semiyear: "半年报",
  year: "年报",
};
