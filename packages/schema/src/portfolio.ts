import { z } from "zod";

export const categoryEnum = ["all", "own", "sub"] as const;
export const categorySchema = z.enum(categoryEnum);
export type Category = z.infer<typeof categorySchema>;

export const categoryEntries: [Category, string][] = [
  ["all", "全部产品"],
  ["own", "我的产品"],
  ["sub", "我的关注"],
];

export const categoryTransform: Record<Category, string> = {
  all: "全部产品",
  own: "我的产品",
  sub: "我的关注",
};
