import { z } from "zod";

export const documentTypeEnum = [
  "交易确认单",
  "电子合同",
  "风险匹配告知书",
  "风险不匹配告知书",
  "交易申请书",
  "分红确认函",
  "投资者基本信息表",
  "合格投资者承诺",
  "税收声明",
  "风险测评问卷",
  "风险承受能力评估结果",
  "投资者类型告知函",
  "专业投资者风险告知函",
  "资产证明",
  "身份证",
  "身份证正面",
  "身份证反面",
  "生物识别",
  "银行卡材料",
  "投资经历证明材料",
  "专业投资者告知及确认书",
  "其他材料",
  "补充协议",
] as const;

export const documentTypeSchema = z.enum(documentTypeEnum);

export type DocumentType = z.infer<typeof documentTypeSchema>;
