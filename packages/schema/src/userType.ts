import { z } from "zod";

export const classificationEnum = ["普通投资者", "专业投资者"] as const;
export const classificationSchema = z.enum(classificationEnum);
export type Classification = z.infer<typeof classificationSchema>;

export const userTypeEnum = ["个人", "机构", "产品"] as const;
export const userTypeSchema = z.enum(userTypeEnum);
export type UserType = z.infer<typeof userTypeSchema>;

export const identityTypeEnum = [
  "下属机构",
  "中国护照",
  "产品备案编码",
  "其他",
  "军官证",
  "军队",
  "台胞证",
  "士兵证",
  "外国人永久居留身份证",
  "外国护照",
  "户口本",
  "批文",
  "文职证",
  "武警",
  "港澳台居民居住证",
  "港澳居民来往内地通行证",
  "登记证书",
  "社会团体",
  "组织机构代码",
  "统一社会信用代码",
  "营业执照",
  "行政机关",
  "警官证",
  "身份证",
  "注册号",
] as const;

export const identityTypeSchema = z.enum(identityTypeEnum);
export type IdentityType = z.infer<typeof identityTypeSchema>;

export const individualIdentityTypeEnum = [
  "身份证",
  "中国护照",
  "警官证",
  "士兵证",
  "军官证",
  "台胞证",
  "港澳居民来往内地通行证",
  "外国人永久居留身份证",
  "外国护照",
  "其他",
] as const satisfies IdentityType[];

export const individualIdentityTypeSchema = z.enum(individualIdentityTypeEnum);
export type IndividualIdentityType = z.infer<
  typeof individualIdentityTypeSchema
>;

export const organizationIdentityTypeEnum = [
  "营业执照",
  "统一社会信用代码",
  "组织机构代码",
  "下属机构",
  "军队",
  "武警",
  "行政机关",
  "社会团体",
  "其他",
] as const satisfies IdentityType[];

export const organizationIdentityTypeSchema = z.enum(
  organizationIdentityTypeEnum
);
export type OrganizationIdentityType = z.infer<
  typeof organizationIdentityTypeSchema
>;
