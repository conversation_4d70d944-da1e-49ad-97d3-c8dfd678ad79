import { z } from "zod";

export const sortByEnum = ["currentEarnings", "holding", "portfolioName"] as const;
export const sortBySchema = z.enum(sortByEnum);
export type SortBy = z.infer<typeof sortBySchema>;

export const sortByEntries: [SortBy, string][] = [
  ["currentEarnings", "按持有收益排序"],
  ["holding", "按持仓市值排序"],
  ["portfolioName", "按产品名称排序"],
];

export const sortByTransform: Record<SortBy, string> = {
  currentEarnings: "按持有收益排序",
  holding: "按持仓市值排序",
  portfolioName: "按产品名称排序",
};
