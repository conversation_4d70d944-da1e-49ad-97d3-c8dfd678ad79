import { z } from "zod";

export const businessTypeEnum = [
  "认购",
  "基金成立",
  "申购",
  "强制调增",
  "赎回",
  "强制赎回",
  "基金清盘",
  "强制调减",
  "修改分红方式",
  "基金转换出",
  "非交易过户",
  "基金转换入",
  "非交易过户出",
  "非交易过户入",
  "内部转托管出",
  "内部转托管入",
  "内部转托管",
  "红利发放",
  "发行失败",
  "基金转换确认",
  "基金份额冻结确认",
  "转销售商",
  "转销售商入",
  "转销售商出",
  "撤单确认",
] as const;
export const businessTypeSchema = z.enum(businessTypeEnum);
export type BusinessType = z.infer<typeof businessTypeSchema>;
