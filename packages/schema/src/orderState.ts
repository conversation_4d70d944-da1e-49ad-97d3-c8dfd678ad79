import { z } from "zod";

export const orderStateEnum = [
  "10001",
  "10002",
  "10003",
  "10007",
  "10014",
  "10022",
  "10023",
  "11001",
  "11002",
  "10011",
  "10024",
  "10026",
] as const;

export const orderStateSchema = z.enum(orderStateEnum);
export type OrderState = z.infer<typeof orderStateSchema>;

export const orderStateTransform: Record<OrderState, string> = {
  10001: "预约审核中",
  10002: "订单信息待确认",
  10003: "合同待投资者签署",
  10007: "签约成功",
  10014: "订单待双录",
  10022: "风险揭示书待投资者签署",
  10023: "合同待托管人签署",
  11001: "补充协议待投资者签署",
  11002: "终止签署",
  10011: "订单取消",
  10024: "风险揭示书待理财经理签署",
  10026: "未生效",
};
