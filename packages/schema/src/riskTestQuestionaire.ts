import { z } from "zod";

export const optionSchema = z.object({
  id: z.string(),
  text: z.string(),
  score: z.number(),
});

export const questionSchema = z.object({
  id: z.number().int(),
  question: z.string(),
  options: z.array(optionSchema),
});

export const configSchema = z.array(questionSchema);

export const answerSchema = z.object({
  questionId: z.number(),
  optionId: z.string(),
});

export const answersSchema = z.array(answerSchema);

export const levelEnum = ["C1", "C2", "C3", "C4", "C5"] as const;
export const levelSchema = z.enum(levelEnum);

export const typeEnum = ["个人", "机构"] as const;
export const typeSchema = z.enum(typeEnum);

export type Option = z.infer<typeof optionSchema>;
export type Question = z.infer<typeof questionSchema>;
export type Config = z.infer<typeof configSchema>;
export type Answer = z.infer<typeof answerSchema>;
export type Answers = z.infer<typeof answersSchema>;
export type Level = z.infer<typeof levelSchema>;
export type Type = z.infer<typeof typeSchema>;
