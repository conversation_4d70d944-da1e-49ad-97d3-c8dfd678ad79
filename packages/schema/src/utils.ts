import dayjs from "dayjs";
import { z } from "zod";

export const stringToDate = z.string().transform((dateString, ctx) => {
  const date = new Date(dateString);
  if (!z.date().safeParse(date).success) {
    ctx.addIssue({ code: z.ZodIssueCode.invalid_date });
  }
  return date;
});

/** MN: mid night */
export const stringToDateMN = z.string().transform((dateString, ctx) => {
  const date = dayjs(dayjs(dateString).format("YYYY-MM-DD")).toDate();
  if (!z.date().safeParse(date).success) {
    ctx.addIssue({ code: z.ZodIssueCode.invalid_date });
  }
  return date;
});

export const phone = z.string().regex(/^1[0-9]{10}$/);

export const datePattern = "YYYY-MM-DD";
export const timePattern = "YYYY-MM-DD HH:mm:ss";
