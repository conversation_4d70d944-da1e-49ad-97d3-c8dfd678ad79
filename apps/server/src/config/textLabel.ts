export default {
  姓名: "investorName",
  仅为中国税收居民: "chineseTaxResident",
  仅为非居民: "nonResidents",
  中国和其他国家税收居民: "chinaAndOtherTax",
  姓: "surname",
  名: "name",
  出生日期: "birthday",
  现居地址中文: "placeOfResidenceChinese",
  现居地址英文: "placeOfResidenceEnglish",
  出生地中文: "placeOfBirthChinese",
  出生地英文: "placeOfBirthEnglish",
  纳税人识别号A: "taxAreaAndTaxpayerIdA",
  纳税人识别号B: "taxAreaAndTaxpayerIdB",
  纳税人识别号C: "taxAreaAndTaxpayerIdC",
  居民国不发放纳税号: "canNotProvideTaxpayerIdNotIssued",
  持有人未取得纳税号: "canNotProvideTaxpayerIdNotAcquired",
  未取得纳税号解释原因: "canNotProvideTaxpayerIdNotAcquiredRemark",
  日期: "signData",
  签名人本人: "signUserIdentityI",
  签名人代理人: "signUserIdentityAgent",
  机构名称: "orgName",
  消极非金融机构: "orgTypeNegativeNonFinancial",
  其他非金融机构: "orgTypeNonFinancial",
  机构名称英文: "orgEnglishName",
  机构地址英文: "orgAddressEnglish",
  机构地址中文: "orgAddressChiness",
  投资者姓名: "userName",
  证件类型: "catType",
  证件号码: "idCardNo",
  性别: "userSex",
  年龄: "userAge",
  国籍: "userCountry",
  职业: "userOccupation",
  职务: "userPosition",
  座机: "userLandline",
  移动电话: "userMobile",
  邮编: "zip",
  电子邮箱: "userEmail",
  住址: "userAddress",
  满足条件是: "userInvestmentExperience",
  满足条件否: "userInvestmentExperienceNo",
  资产规模是: "userAssetsScale",
  资产规模否: "userAssetsScaleNo",
  控制关系是: "isControllingShareholderNo",
  控制关系否: "isControllingShareholder",
  控制关系原因: "isControllingShareholderDesc",
  交易人本人是: "transactionActualBeneficiary",
  交易人他人: "transactionActualBeneficiaryNo",
  交易人说明原因: "transactionActualBeneficiaryDesc",
  不良诚信是: "isBadFaithNo",
  不良诚信否: "isBadFaith",
  不良诚信原因: "isBadFaithDesc",
  机构类型: "orgType",
  机构证件类型: "orgDocumentsType",
  机构证件编号: "orgDocumentsNo",
  有效期: "orgDocumentsValidity",
  机构资质证明: "orgQualificationProve",
  资质证明编号: "orgQualificationNo",
  经营范围: "orgBusinessScope",
  注册地址: "orgRegisterAddress",
  办公地址: "orgAddress",
  注册资本: "orgRegisteredCapital",
  股东或者实际控制人: "orgController",
  法人姓名: "representativeName",
  法人性别: "representativeSex",
  法人年龄: "representativeAge",
  法人证件类型: "representativeDocumentsType",
  法人证件号码: "representativeIdCardNo",
  法人职务: "representativePosition",
  法人电子邮箱: "representativeEmail",
  法人证件有效期: "representativeDocumentsValidity",
  法人座机: "representativeLanline",
  法人移动电话: "representativeMobile",
  法人邮编: "representativeZip",
  法人地址: "representativeAddress",
  经办人姓名: "managersName",
  经办人性别: "managersSex",
  经办人年龄: "managersAge",
  经办人证件类型: "managersDocumentsType",
  经办人证件号码: "managersIdCardNo",
  经办人职务: "managersPosition",
  经办人邮箱: "managersEmail",
  经办人证件有效期: "managersDocumentsValidity",
  经办人座机: "managersLanline",
  经办人移动电话: "managersMobile",
  经办人办公邮编: "managersZip",
  经办人办公地址: "managersAddress",
  经办人与机构关系: "relationshipWithOrg",
  产品投资者名称: "productName",
  产品类型: "productType",
  产品备案机构: "productRecordCode",
  成立时间: "productFoundingDateStr",
  备案时间: "productRecordDateStr",
  产品备案编号: "productRecordNo",
  产品存续期: "productSurvivalPeriod",
  产品类别: "productCategory",
  产品规模: "productScale",
  产品托管人: "productDeposit",
  管理人名称: "managerName",
  基金账号: "jjzh",
  销售商: "xss",
  基金名称: "jjmc",
  基金代码: "jjdm",
  分红方式: "fhfs",
  红利登记日: "hldjr",
  红利发放日: "hlffr",
  每单位分红: "mdwfh",
  参加分红份额: "cjfhfe",
  现金红利: "xjhl",
  红利再投资份额: "hlztzfe",
  再投资单位净值: "ztzdwjz",
  再投资红利金额: "ztzhlje",
  红利总额: "hlze",
  业绩报酬: "yjbc",
  银行户名: "accountName",
  银行账号: "accountNo",
  开户银行: "accountOpen",
  投资者全称: "userNameAll",
  投资者类型: "tzzlx",
  产品投资者证件类型: "productCertType",
  联系地址: "address",
  开放日: "openDay",
  赎回份额: "shfe",
  赎回费用: "shfy",
  认申购费用: "rsgfy",
  认申购金额: "rsgje",
  投资者风险等级: "userRisk",
  产品风险等级: "productRisk",
  交易类型: "jylx",
  确认日期: "qrrq",
  申请金额: "sqje",
  申请份额: "sqfe",
  确认金额: "qrje",
  确认份额: "qrfe",
  单位净值: "dwjz",
  交易费用: "jyfy",
  ans1: "ans1",
  ans2: "ans2",
  ans3: "ans3",
  ans4: "ans4",
  ans5: "ans5",
  ans6: "ans6",
  ans7: "ans7",
  ans8: "ans8",
  ans9: "ans9",
  ans10: "ans10",
  ans11: "ans11",
  ans12: "ans12",
  ans13: "ans13",
  ans14: "ans14",
  ans15: "ans15",
  ans16: "ans16",
  C1: "C1",
  C2: "C2",
  C3: "C3",
  C4: "C4",
  C5: "C5",
  R1: "R1",
  R2: "R2",
  R3: "R3",
  R4: "R4",
  R5: "R5",
  总分: "total",
};
