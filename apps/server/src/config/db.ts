import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import env from "./env";
import { isDevEnv } from "../utils/dev";

const poolConnection = mysql.createPool({
  host: env.DB_HOST,
  port: env.DB_PORT ? parseInt(env.DB_PORT) : undefined,
  database: env.DB_NAME,
  user: env.DB_USER,
  password: env.DB_PASSWORD,
});

export const db = drizzle(poolConnection, {
  logger: isDevEnv(),
});

export const disconnect = () => {
  poolConnection.end();
}
