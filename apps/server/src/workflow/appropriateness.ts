import { Classification } from "@portfolio-service/schema/userType";
import { assertEvent, assign, setup } from "xstate";
import service from "../service/appropriatenessProcess";

type Context = {
  classification?: Classification;
  id?: string;
};

type Input = {
  id?: string;
};

export const appropriatenessMachine = setup({
  types: {
    context: {} as Context,
    input: {} as Input,
    events: {} as
      | { type: "event.next" }
      | { type: "event.reject" }
      | { type: "event.terminate" }
      | { type: "event.redo" }
      | { type: "event.classificationReviewPass"; value: Classification },
  },
  actions: {
    "action.classificationReview": assign({
      classification: ({ event }): Classification => {
        assertEvent(event, "event.classificationReviewPass");
        return event.value;
      },
    }),
    "action.complete": async ({ context }) => {
      await service.sendNotification(context.id, "complete");
    },
    "action.reject": async ({ context }) => {
      await service.sendNotification(context.id, "reject");
    },
    "action.terminate": async ({ context }) => {
      await service.sendNotification(context.id, "terminate");
    },
  },
  guards: {
    isValid: ({ event }) => {
      assertEvent(event, "event.classificationReviewPass");
      return event.value === "普通投资者";
    },
    isProfessional: ({ event }) => {
      assertEvent(event, "event.classificationReviewPass");
      return event.value === "专业投资者";
    },
  },
}).createMachine({
  /** @xstate-layout N4IgpgJg5mDOIC5QEMAOqBOB7TBLZALmAHZywDEYAbiQQHQZgBWYAxgQNoAMAuoqKiyxcBXFmL8QAD0QAWAEwAaEAE9E8gIwB2OvICsATgAcAZj3yAbFYtG9WgL73laTDgz4ipWBWq06RDABbXGJCMG4+JBBBYVFxSRkEBWU1BHktI11DI1k9DQt9I01HZ3RsPDCvWDpWABtkb1wAM1xWQjFiShpielIpTl5JGJEOhPV5Ll0uEw08hX080xTEExNMvS5jWSKFLj0TLgcnEBdy90qyGvrGlra44gAlSCwuvz6ByIEhEfioxIMTPI6HtZBZVlxZDMjAYDMsEAdJnpslokRoTFoNHs9CUTmU3B4SJc6g1hLd2uInlRcGAAO6vHpXEnNVrkx7Uak0gAKJIiQ2+9zGCAsGiBOXRYI0pgBJlkcKMGjoshhxlM0y0Vj0shxp3xF28jJuLPulI59PoxMNdw6Jtp3O8HA0n2i-NGf0QwtFkPVUJM0tlqnUXCB8iVxgskPMRkOJm1eIqniJ11JRut7NpZoYz15UWGArdQpFdDF3slvtW-tSZgVe2MGK06q0awBsdc8cJ+vcsAA1gAVOAEDPvbNfWKu0CJEWGOh2aabfZFaYVxCY2SK9Wa+RRwF7Iwts4Eqp0CBYVgAV0CtFgAGVcFBOr4GUPBjmXb9x8uLBC6OGuMLJSH5E0JQAzSbQ6AMddZFmOxNSMCw911BN9WPM8Lx6a9bzZY9BzAfph2dUc32kD8vx-P8dkAkU4XyHRZEg8wDFyCZ5AQttDxQ89LxtOkH16XCPj5QiJHzDRpToLRQyROxTD0eVqIsAxwPo+RGPMLhJVY84kOqDi0IIWBuIzRhj3w3Mx2IhB8lI2Rfw0f95iokC7IsJSLE1Axpg0JVS0cY5iCwCA4EkHU2LIQSfmE98EAAWgsOFYuBLgkuSlLkqOUpWy09tqgtZMrSIsyiMScNwN-ENZAquypI0OFVkUpLjEOLyPJamNjhCrLD1y5l8qwrBwrzKLMS0IFtEleVjH0cMl3hXJXP2DyrHkGUWPauNOsTJkyWNNMaQG8zEjWPQ6AOOjZg8xdplq31EsajElU2TY2oy-c9WqTte37faiuXYbxMlCThTyCwJOA1IV3mhQtwmWTNIPS5dMvG872+yKLK8zETo8wxGPUtzjDhTRMjotyoKRFFtmFOG3qPE9OPQ5G+tRwU0UY78jAk9UVPyfY4qcqdoUBSccnUzVqe02nUK43bmZEiTjoXdIF3raYTGorRJhJzUGKYoNxeymosECVBajAIhZaiiZGyLEGRWWqEuCWJyFN0Sw7AMOxMV9Xc1sy+GO2YNgCAtizAMmCYZjmQDUSd1JNBcjZJpyDZAPg33XolgJglCc2XyEwUw6mSOvOjxY1ZA9IdET0xTFBVn9l8+wgA */
  context: ({ input }) => input,
  id: "appropriateness",
  initial: "classification",
  states: {
    classification: { on: { "event.next": "classificationReview" } },
    classificationRedo: { on: { "event.next": "classificationReview" } },
    classificationReview: {
      on: {
        "event.classificationReviewPass": [
          {
            target: "riskTest",
            actions: "action.classificationReview",
            guard: "isValid",
          },
          {
            target: "documentsSign",
            actions: "action.classificationReview",
            guard: "isProfessional",
          },
        ],
        "event.redo": "classificationRedo",
      },
    },

    riskTest: { on: { "event.next": "riskTestReview" } },
    riskTestRedo: { on: { "event.next": "riskTestReview" } },
    riskTestReview: {
      on: {
        "event.next": "documentsSign",
        "event.redo": "riskTestRedo",
      },
    },

    documentsSign: { on: { "event.next": "documentsReview" } },
    documentsSignRedo: { on: { "event.next": "documentsReview" } },
    documentsReview: {
      on: {
        "event.next": "complete",
        "event.redo": "documentsSignRedo",
      },
    },

    complete: { entry: "action.complete", type: "final" },
    reject: { entry: "action.reject", type: "final" },
    terminate: { entry: "action.terminate", type: "final" },
  },
  on: {
    "event.reject": { target: ".reject" },
    "event.terminate": { target: ".terminate" },
  },
});
