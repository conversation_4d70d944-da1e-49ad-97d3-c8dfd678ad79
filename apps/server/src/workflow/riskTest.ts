import { setup } from "xstate";

type Context = {
  id?: string;
  isManager?: boolean;
};

type Input = Context;

export const riskTestMachine = setup({
  types: {
    context: {} as Context,
    input: {} as Input,
    events: {} as { type: "event.next" } | { type: "event.terminate" },
  },
  guards: {
    "guard.manager": ({ context }) => !!context.isManager,
    "guard.user": ({ context }) => !context.isManager,
  },
}).createMachine({
  id: "riskTest",
  initial: "entrance",
  context: ({ input }) => input,
  states: {
    entrance: {
      always: [
        { target: "riskTest", guard: "guard.manager" },
        { target: "documentsSign", guard: "guard.user" },
      ],
    },
    riskTest: { on: { "event.next": "documentsSign" } },
    documentsSign: { on: { "event.next": "complete" } },

    complete: { type: "final" },
    terminate: { type: "final" },
  },
  on: {
    "event.terminate": { target: ".terminate" },
  },
});
