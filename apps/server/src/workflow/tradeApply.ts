import { setup } from "xstate";

export const tradeApplyMachine = setup({
  types: {
    events: {} as
      | { type: "event.next" }
      | { type: "event.reject" }
      | { type: "event.terminate" }
      | { type: "event.redo" },
  },
}).createMachine({
  /** @xstate-layout N4IgpgJg5mDOIC5SwK4CNYGMBOBLNYAxGAG5gB2ALgHTZgBWYmlA2gAwC6ioADgPaxclXH3LcQAD0QAWAEwAaEAE9EsgGzTqa2QFY2ADjVsDATgCM0ndIC+1xagw58RUhRqUw2ALa5yAQw92LiQQfkFhUXEpBDlFFQQLAGZqM10zRMT9AHYTNVyLW3t0LDwCahJcCDA+ACUmPmwIYjIqanIwCVZOcTChETEQ6NlZNmoRxPT9aX0dfVkc6TjVNmTErL0jMxn9Nh1CkAcS53LK6rrMBog6iD5mtzaOruDeAT7IwcQrLK1E2X1EnRZNSJNQ6EwKZSqaTJYxsMxmPJqLJmNigvZ2A7FJxlCpVWqkXBgADud1a7U6QR6rwiA1A0SsamogJWcj0sxM4KWCXBTNhWWk0JRmVR+0O2LAJzxdQqxNJNDoN0pIV6NKin2kjOZiVZu30HIh8VkJmSejhYL0E3WbBsGLFpQlN0wKC8blgAGVcFByHKHhTusrqf01Qg9bJqFkVvDpGwTJZZGYdFyzDzTWx+YKVjs1KKsfbqI7na6PV7rrdXGTHkqXuEgx8QzyIxMLDG4wmk79qLDI3rpLkTICc448wWXVRYNLCSTyzRyU8qTX3nTEKl49R9Kk9OsNXNFpCEtrecYJj2+wPbbnjiPXRPZdPaJA+FXQoHF5JEKHw5Hm7GdPHE3v12oXsOT1AE9ThAxbAxcg+CqeAQjtZx5zeWk3wQABaNQuUwwcjhxU58QuRpkNVOs8jXDd0iBPRpFSLC93GNd1hRDkBUSXZtVw8VJTOepGlLEjayXBA1GycNTXjftfmmLk0iZf5e12bR4TmLi81xXiZSJQTX2iTIdGodjpGRSwdDUYE1lklYmJ0FjY2hDibSKIdLz4J1R0od1PVQlUhLQxs12tPJ2NmP4zCTY1O2MVJaL1HRMkyNTXPcotvIEgMF1Q6JRLDK02Ek+LZBkgDvlmX5ciMcFTKczEXLKK8xxvbSMpQ4MhWSfQ5hyAFYsbdtNA5ED4xBDVwSyJKyguLweAAGzADwdKy5cQTMahwT7NYRvSdtkn5ViTBjGN5k6iaJToRhmEW4NhlGcZJmmUKFlk4EbJReZ42yAFElO6gPG8XwAjAK66xusZI0yB6utjJM5EM9ZdAg3IdEBWQoOsIA */
  id: "tradeApply",
  initial: "tradeApplySign",
  states: {
    tradeApplySign: { on: { "event.next": "tradeApplyReview" } },
    tradeApplySignRedo: { on: { "event.next": "tradeApplyReview" } },
    tradeApplyReview: {
      on: { "event.next": "complete", "event.redo": "tradeApplySignRedo" },
    },

    complete: { type: "final" },
    reject: { type: "final" },
    terminate: { type: "final" },
  },
  on: {
    "event.reject": { target: ".reject" },
    "event.terminate": { target: ".terminate" },
  },
});
