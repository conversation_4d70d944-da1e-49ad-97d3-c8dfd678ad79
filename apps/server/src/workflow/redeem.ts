import { setup } from "xstate";
import service from "../service/redeemProcess";
import { State } from "../utils/xstate";

type Context = {
  id?: string;
};

type Input = Context;

export const redeemMachine = setup({
  types: {
    context: {} as Context,
    input: {} as Input,
    events: {} as
      | { type: "event.next" }
      | { type: "event.reject" }
      | { type: "event.terminate" }
      | { type: "event.redo" },
  },
  actions: {
    "action.complete": async ({ context }) => {
      await service.sendNotification(context.id, "complete");
    },
    "action.reject": async ({ context }) => {
      await service.sendNotification(context.id, "reject");
    },
    "action.terminate": async ({ context }) => {
      await service.sendNotification(context.id, "terminate");
    },
  },
}).createMachine({
  /** @xstate-layout N4IgpgJg5mDOIC5SwK4CNYGMBOBLNYAxGAG5gB2ALgHTZgBWYmlA2gAwC6ioADgPaxclXH3LcQAD0QAWAEwAaEAE9EsgGzTqa2QFY2ADjVsDATgCM0ndIC+1xagw58RUhRqUw2ALa5yAQw92LiQQfkFhUXEpBDlFFQQLAGZqM10zRMT9AHYTNVyLW3t0LDwCahJcCDA+ACUmPmwIYjIqanIwCVZOcTChETEQ6NlZNmoRxPT9aX0dfVkc6TjVNmTErL0jMxn9Nh1CkAcS53LK6rrMBog6iD5mtzaOruDeAT7IwcQrLK1E2X1EnRZNSJNQ6EwKZSqaTJYxsMxmPJqLJmNigvZ2A7FJxlCpVWqkXBgADud1a7U6QR6rwiA1A0SsamogJWcj0sxM4KWCXBTNhWWk0JRmVR+0O2LAJzxdQqxNJNDoN0pIV6NKin2kjOZiVZu30HIh8VkJmSejhYL0E3WbBsGLFpQlN0wKC8blgAGVcFByHKHhTusrqf01Qg9bJqFkVvDpGwTJZZGYdFyzDzTWx+YKVjs1KKsfbqI7na6PV7rrdXGTHkqXuEgx8QzyIxMLDG4wmk79qLDI3rpLkTICc448wWXVRYNLCSTyzRyU8qTX3nTEKl49R9Kk9OsNXNFpCEtrecYJj2+wPbbnjiPXRPZdPaJA+FXQoHF5JEKHw5Hm7GdPHE3v12oXsOT1AE9ThAxbAxcg+CqeAQjtZx5zeWk3wQABaNQuUwwcjhxU58QuRpkNVOs8jXDd0iBPRpFSLC93GNd1hRDkBUSXZtVw8VJTOepGlLEjayXBA1GycNTXjftfmmLk0iZf5e12bR4TmLi81xXiZSJQTX2iTIdGodjpGRSwdDUYE1lklYmJ0FjY2hDibSKIdLz4J1R0od1PVQlUhLQxs12tPJ2NmP4zCTY1O2MVJaL1HRMkyNTXPcotvIEgMF1Q6JRLDK02Ek+LZBkgDvlmX5ciMcFTKczEXLKK8xxvbSMpQ4MhWSfQ5hyAFYsbdtNA5ED4xBDVwSyJKyguLweAAGzADwdKy5cQTMahwT7NYRvSdtkn5ViTBjGN5k6iaJToRhmEW4NhlGcZJmmUKFlk4EbJReZ42yAFElO6gPG8XwAjAK66xusZI0yB6utjJM5EM9ZdAg3IdEBWQoOsIA */
  id: "redeem",
  initial: "documentsSign",
  context: ({ input }) => input,
  states: {
    documentsSign: { on: { "event.next": "documentsReview" } },
    documentsSignRedo: { on: { "event.next": "documentsReview" } },
    documentsReview: {
      on: {
        "event.next": "complete",
        "event.redo": "documentsSignRedo",
      },
    },

    complete: { entry: "action.complete", type: "final" },
    reject: { entry: "action.reject", type: "final" },
    terminate: { entry: "action.terminate", type: "final" },
  },
  on: {
    "event.reject": { target: ".reject" },
    "event.terminate": { target: ".terminate" },
  },
});

export const redeemActiveStates: State<typeof redeemMachine>[] = [
  "documentsSign",
  "documentsSignRedo",
  "documentsReview",
];
