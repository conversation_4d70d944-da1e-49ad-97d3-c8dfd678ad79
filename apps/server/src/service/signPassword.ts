import { Service } from "../utils/service";
import userService from "./user";
import ttdService from "./ttd";
import { z } from "zod";
import { setSignPasswordInputSchema } from "../model/ttd";
import { TRPCError } from "@trpc/server";

class SignPasswordService extends Service {
  async exists(userId: number) {
    const userNo = await userService.getTtdUserNoByIdSafe(userId);
    if (!userNo) return { exists: false };

    const { signPassword } = await ttdService.queryUserInfo(userNo);
    return { exists: !!signPassword };
  }

  async create(userId: number, password: string) {
    const user = await userService.getById(userId);
    const userNo = await userService.getTtdUserNoById(userId);

    const input: z.infer<typeof setSignPasswordInputSchema> = {
      userNo,
      password,
    };

    if (user.type === "个人") {
      input.name = user.name;
      input.identityNumber = user.identityNumber;
    } else if (user.type === "机构" || user.type === "产品") {
      const { representativeName } = user;
      if (!representativeName) {
        throw new TRPCError({ code: "FORBIDDEN", message: "请先完成实名认证" });
      }

      input.organizationName = user.name;
      input.organizationIdentityNumber = user.identityNumber;
      input.representativeName = representativeName;
    }

    await ttdService.setSignPassword(input);
  }
}

export default new SignPasswordService();
