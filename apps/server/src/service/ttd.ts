import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import { v4 as uuid } from "uuid";
import { z } from "zod";
import {
  CreateIndividualInvestorBody,
  CreateOrderBody,
  CreateOrganizationInvestorBody,
  CreateVideoConfigBody,
  CreateVideoConfigResponse,
  DataStr,
  DocumentForSignResponse,
  OrderResponse,
  ProductResponse,
  UserInfoResponse,
  VerifyIdentityBody,
  VerifyIdentityResponse,
  VerifyIdentityType,
  Video,
  VideoConfigModel,
  productListInputSchema,
  setSignPasswordInputSchema,
  verifyIdentityTypeTransform,
  verifySignPasswordInputSchema,
} from "../model/ttd";
import { isDevEnv } from "../utils/dev";
import { identityTypeTtdTransform, query } from "../utils/ttd";

class TtdService {
  async createIndividualInvestor(data: CreateIndividualInvestorBody) {
    const response = await query<{ userNoStr: string }>(
      { url: "ADD_INVESTOR_PERSONAL" },
      {
        userName: data.name,
        catType: identityTypeTtdTransform[data.identityType],
        idCardNo: data.identityNumber,
        authorResult: data.valid ? "是" : "否",
      }
    );
    return response.data.data.bean.userNoStr;
  }

  async createOrganizationInvestor(data: CreateOrganizationInvestorBody) {
    const response = await query<{ userNoStr: string }>(
      { url: "ADD_INVESTOR_ORG" },
      {
        orgName: data.name,
        orgDocumentsType: identityTypeTtdTransform[data.identityType],
        orgDocumentsNo: data.identityNumber,
        representativeName: data.representativeName,
        representativeIdCardNo: data.representativeIdentityNumber,
        representativeDocumentsType:
          identityTypeTtdTransform[data.representativeIdentityType],
        authorResult: data.valid ? "是" : "否",
      }
    );
    return response.data.data.bean.userNoStr;
  }

  async verifyIdentity(
    type: VerifyIdentityType,
    data: VerifyIdentityBody
  ): Promise<VerifyIdentityResponse> {
    if (isDevEnv()) {
      return {
        name: data.name,
        idCard: data.identityNumber,
        authorResult: true,
        respRemark: "",
      };
    }

    const response = await query<VerifyIdentityResponse>(
      { url: verifyIdentityTypeTransform[type] },
      {
        name: data.name,
        idCard: data.identityNumber,
        idNumberType: data.identityType,
        bankNo: data.bankNumber,
        mobile: data.phone,
      }
    );

    return response.data.data.bean;
  }

  async createVideoConfig(model: VideoConfigModel, words: string) {
    const targetCode = uuid();

    const data: CreateVideoConfigBody = {
      recodeModel: model,
      targetCode,
      words,
      miniAppId: "",
      backUrl: "",
      intervalTime: 5,
      playSpeed: 50,
      envVersion: isDevEnv() ? "trial" : undefined,
    };

    const response = await query<CreateVideoConfigResponse>(
      { url: "CREATE_VIDEO_CONFIG" },
      data
    );

    return response.data.data.bean;
  }

  async queryVideo(targetCode: string) {
    const response = await query<Video>(
      { url: "SELECT_VIDEO" },
      { targetCode }
    );

    return response.data.data.list;
  }

  async createDocumentForSign(
    typeCode: string,
    orderNumber?: string,
    data?: DataStr
  ) {
    try {
      const response = await query<DocumentForSignResponse>(
        { url: "CUSTOM_CREATE_FILE" },
        {
          typeCode,
          orderNo: orderNumber,
          dataStr: data ? JSON.stringify(data) : undefined,
        }
      );

      return response.data.data.bean;
    } catch (e: any) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建妥妥递签署文件失败\n" + e?.message,
      });
    }
  }

  async querySignedDocumentSafe(fileId: string | undefined | null) {
    if (!fileId) {
      return;
    }

    const response = await query<DocumentForSignResponse>(
      { url: "CUSTOM_SELECT_FILE" },
      { fileId }
    );

    return response.data.data.list?.[0];
  }

  async querySignedDocument(fileId: string | undefined | null) {
    const result = await this.querySignedDocumentSafe(fileId);
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async createOrder(data: CreateOrderBody) {
    const result = await query<OrderResponse>(
      { url: "ADD_ORDER_INFO" },
      {
        productNo: data.productNumber,
        amount: Math.round(data.amount * 100).toString(),
        userNo: data.userNo,
        isConfirm: "1",
        bookDate: dayjs(data.time).format("YYYY-MM-DD HH:mm:ss"),
        productDealId: data.productDealId,
        isOffline: data.isOffline ? 1 : undefined,
      }
    );

    return result.data.data.bean;
  }

  async cancelOrder(orderNo: string) {
    const result = await query<OrderResponse>(
      { url: "CANCEL_ORDER" },
      { orderNo }
    );

    return result.data.data.bean;
  }

  async queryOrder(orderNumber: string, stateOnly?: boolean) {
    const result = await query<OrderResponse>(
      { url: "INQUIRE_ORDER" },
      { orderNo: orderNumber, selectOrderState: stateOnly ? 1 : undefined }
    );

    return result.data.data.list[0];
  }

  async updateOrderAmount(orderNumber: string, amount: number) {
    await query(
      { url: "UPDATE_ORDER_AMOUNT" },
      { orderNo: orderNumber, amount: Math.round(amount * 100).toString() }
    );
  }

  async invalidateOrder(orderNumber: string) {
    await query({ url: "INVALID_ORDER" }, { orderNo: orderNumber });
  }

  async queryProductList(input: z.infer<typeof productListInputSchema>) {
    const result = await query<ProductResponse>(
      { url: "INQUIRE_PRODUCT" },
      { productName: input?.name, pageSize: 10, pageNum: 1 }
    );
    return result.data.data.list;
  }

  async queryProduct(productNumber: string) {
    const result = await query<ProductResponse>(
      { url: "INQUIRE_PRODUCT" },
      { productSerialNo: productNumber }
    );

    return result.data.data.list[0];
  }

  async querySupplements(orderNumber: string) {
    const result = await query<OrderResponse | null>(
      { url: "INQUIRE_ORDER_SUPPLEMENT" },
      { orderNo: orderNumber }
    );

    return result.data.data.bean;
  }

  async setSignPassword(input: z.infer<typeof setSignPasswordInputSchema>) {
    await query(
      { url: "CREATE_SIGN_PASSWORD" },
      {
        userNo: input.userNo,
        signPassword: input.password,
        userName: input.name,
        idCardNo: input.identityNumber,
        orgName: input.organizationName,
        orgDocumentsNo: input.organizationIdentityNumber,
        representativeName: input.representativeName,
      }
    );
  }

  async verifySignPassword(
    input: z.infer<typeof verifySignPasswordInputSchema>
  ) {
    try {
      await query(
        { url: "VERIFY_SIGN_PASSWORD" },
        { userNo: input.userNo, signPassword: input.password }
      );

      return { success: true, message: undefined };
    } catch (e) {
      const message = e instanceof Error ? e.message : "请求失败";

      return { success: false, message };
    }
  }

  async queryUserInfo(userNo: string) {
    const result = await query<UserInfoResponse>(
      { url: "SELECT_USER_INFO" },
      { userNo }
    );

    return result.data.data.bean;
  }
}

export default new TtdService();
