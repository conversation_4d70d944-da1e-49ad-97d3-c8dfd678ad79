import { statusSchema } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import AdmZip from "adm-zip";
import dayjs from "dayjs";
import { and, count, desc, eq, inArray, ne, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first, uniqBy } from "lodash";
import { MD5 } from "object-hash";
import path from "path";
import { z } from "zod";
import {
  createAltInputSchema,
  createInputSchema,
  forceCreateInputSchema,
  listInputSchema,
  nextInputSchema,
  NotificationType,
  ownListInputSchema,
  portfolioIdSchema,
  reviewInputSchema,
  terminateInputSchema,
  userIdSchema,
} from "../model/supplementProcess";
import * as portfolio from "../schema/portfolio";
import * as log from "../schema/supplementCheckLog";
import {
  model,
  select,
  supplementProcessDocument,
} from "../schema/supplementProcess";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import { timestampEqDate } from "../utils/drizzle";
import { extractErrorMessage } from "../utils/error";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import {
  supplementMachine as machine,
  supplementActiveStates,
} from "../workflow/supplement";
import documentService from "./document";
import fileUploadService from "./fileUpload";
import notificationService from "./notification";
import subscribeProcessService from "./subscribeProcess";
import supplementToCheckService from "./supplementToCheck";
import ttdService from "./ttd";
import userService from "./user";
import workflowService from "./workflow";
import env from "../config/env";

class SupplementProcessService extends Service {
  private select = {
    ...select,
    portfolioName: portfolio.select.name,
    portfolioNo: portfolio.select.no,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
    username: user.model.name,
    identityNumber: user.model.identityNumber,
    identityType: user.model.identityType,
    classification: user.model.classification,
  };

  async getList(
    input: z.infer<typeof listInputSchema>,
    ignoreTerminated?: boolean
  ) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input, ignoreTerminated);
    query = this.withOrder(query);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getOwnList(userId: number, input: z.infer<typeof ownListInputSchema>) {
    return await this.getList({ ...input, userId }, true);
  }

  async getByIdSafe(id: number, userId?: number) {
    const predicate: SQL[] = [eq(model.id, id)];
    if (userId != null) predicate.push(eq(model.userId, userId));

    let query = this.db
      .select(this.select)
      .from(model)
      .where(and(...predicate))
      .$dynamic();
    query = this.withJoin(query);

    return first(await query);
  }

  async getById(id: number, userId?: number) {
    const record = await this.getByIdSafe(id, userId);
    if (!record) throw new TRPCError({ code: "NOT_FOUND" });

    return record;
  }

  async getByWorkflowId(workflowId: string) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.workflowId, workflowId))
      .$dynamic();

    query = this.withJoin(query);

    const [record] = await query;

    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByIds(ids: number[]) {
    if (!ids.length) {
      return [];
    }

    let query = this.db
      .select(this.select)
      .from(model)
      .where(inArray(model.id, ids))
      .$dynamic();

    query = this.withJoin(query);
    return await query;
  }

  async getByParentProcessIdSafe(processId: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.parentProcessId, processId))
      .$dynamic();

    query = this.withJoin(query);
    return await query;
  }

  async getByOrderNumber(orderNumber: string) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.orderNumber, orderNumber))
      .$dynamic();

    query = this.withJoin(query);
    return await query;
  }

  async getByUserIdAndPortfolioId(input: {
    userId: number;
    portfolioId: number;
  }) {
    const { userId, portfolioId } = input;
    let query = this.db
      .select(this.select)
      .from(model)
      .where(and(eq(model.userId, userId), eq(model.portfolioId, portfolioId)))
      .$dynamic();

    query = this.withJoin(query);
    return await query;
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(supplementProcessDocument)
      .where(eq(supplementProcessDocument.supplementProcessId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async create(input: z.infer<typeof createInputSchema>) {
    const { operatorId, parentProcessId } = input;

    const parent = await subscribeProcessService.getById(parentProcessId);

    if (parent.status !== "done") {
      throw new TRPCError({ code: "FORBIDDEN", message: "流程正在进行中" });
    }

    const { userId, portfolioId, orderNumber } = parent;
    if (!orderNumber) {
      throw new TRPCError({ code: "FORBIDDEN", message: "无订单号" });
    }

    await transaction.run(async (tx) => {
      const actor = await workflowService
        .createActor(machine)
        .start(operatorId, userId);

      await tx.insert(model).values({
        workflowId: actor.id,
        userId,
        portfolioId,
        orderNumber,
        parentProcessId,
      });

      await this.sendNotification(actor.id, "supplementSignEntry");
    });
  }

  async forceCreate(input: z.infer<typeof forceCreateInputSchema>) {
    const { operatorId, orderNumber, userId, portfolioId } = input;
    const order = await ttdService.querySupplements(orderNumber);
    if (!order?.supplements?.length) {
      return;
    }

    const records = await Promise.all([
      this.getByOrderNumber(orderNumber),
      this.getByUserIdAndPortfolioId({ userId, portfolioId }),
    ]);

    if (records.flat().some((it) => it.status === "active")) {
      return;
    }

    await transaction.run(async (tx) => {
      const actor = await workflowService
        .createActor(machine)
        .start(operatorId, userId);

      await tx.insert(model).values({
        workflowId: actor.id,
        userId,
        portfolioId,
        orderNumber,
      });

      await this.sendNotification(actor.id, "supplementSignEntry");
    });
  }

  async createAlt(input: z.infer<typeof createAltInputSchema>) {
    const { operatorId, userId, portfolioId, productNumber } = input;

    const existing = await this.getByUserIdAndPortfolioId(input);
    if (existing.some((it) => it.status === "active")) {
      throw new TRPCError({
        code: "CONFLICT",
        message: "该客户与产品组合存在进行中的补充协议流程",
      });
    }

    const user = await userService.getById(input.userId);
    const userNo = await userService.getTtdUserNoById(user.id);

    const { orderNo } = await ttdService.createOrder({
      amount: 0,
      productNumber,
      userNo,
      time: dayjs().toDate(),
      isOffline: true,
    });

    const { supplements } = await ttdService.queryOrder(orderNo);
    if (!supplements?.length) {
      try {
        await ttdService.invalidateOrder(orderNo);
      } catch (e) {
        console.error(e);
      }

      throw new TRPCError({
        code: "NOT_FOUND",
        message: "该客户与产品组合没有需要签署的补充协议",
      });
    }

    await transaction.run(async (tx) => {
      const actor = await workflowService
        .createActor(machine)
        .start(operatorId, userId);

      await tx.insert(model).values({
        workflowId: actor.id,
        userId,
        portfolioId,
        orderNumber: orderNo,
      });

      await this.sendNotification(actor.id, "supplementSignEntry");
    });
  }

  async getActiveAmount(userId: number) {
    const [result] = await this.db
      .select({ amount: count() })
      .from(model)
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .where(
        and(eq(model.userId, userId), eq(workflowTask.model.status, "active"))
      );

    return result;
  }

  async getSupplementLinksById(id: number) {
    const { orderNumber } = await this.getById(id);
    if (!orderNumber) {
      return { urls: [] };
    }

    const ttdOrder = await ttdService.queryOrder(orderNumber);

    return { urls: ttdOrder.supplements?.map(({ fileUrl }) => fileUrl) || [] };
  }

  async supplementSign(userId: number, input: z.infer<typeof nextInputSchema>) {
    await withMutex(
      ["supplementProcessService.supplementSign", userId, input],
      async () => {
        const record = await this.getById(input.id, userId);
        if (!record.orderNumber) {
          throw new TRPCError({ code: "FORBIDDEN", message: "无订单号" });
        }

        await this.validateSupplementSign(record.orderNumber);

        await transaction.run(async () => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["supplementSign", "supplementSignRedo"],
          });

          await actor.send({ type: "event.next" }, userId, null);
        });
      }
    );
  }

  async supplementReview(input: z.infer<typeof reviewInputSchema>) {
    const { id, operatorId, pass } = input;
    const record = await this.getById(id);

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["supplementReview"],
      });

      await actor.send(
        { type: pass ? "event.next" : "event.redo" },
        operatorId,
        record.userId
      );
    });
  }

  async createFromToCheck() {
    await withMutex("supplementProcessService.createFromToCheck", async () => {
      const result = await supplementToCheckService.getList();
      if (!result.length) {
        return;
      }

      const uniqueResult = uniqBy(result.reverse(), (it) => it.processId);

      for (const { processId, operatorId } of uniqueResult) {
        try {
          const { orderNumber } =
            await subscribeProcessService.getById(processId);
          if (!orderNumber) {
            throw new TRPCError({ code: "FORBIDDEN", message: "无订单号" });
          }

          const order = await ttdService.querySupplements(orderNumber);
          if (!order?.supplements?.length) {
            continue;
          }

          await transaction.run(
            async () => {
              const records = await this.getByParentProcessIdSafe(processId);
              if (!records.filter((it) => it.status === "active").length) {
                await this.create({
                  operatorId,
                  parentProcessId: processId,
                });
              }

              await supplementToCheckService.deleteByProcessId(processId);
            },
            { new: true }
          );
        } catch (e) {
          console.error(e);
        }
      }
    });
  }

  async createFromToCheckAlt() {
    await withMutex(
      "supplementProcessService.createFromToCheckAlt",
      async () => {
        const result = await supplementToCheckService.getAltList();
        if (!result.length) {
          return;
        }

        const uniqueResult = uniqBy(result.reverse(), (it) =>
          MD5({ userId: it.userId, portfolioId: it.portfolioId })
        );

        for (const item of uniqueResult) {
          try {
            await transaction.run(
              async (tx) => {
                try {
                  await this.createAlt(item);
                  await tx.insert(log.model).values({
                    userId: item.userId,
                    portfolioId: item.portfolioId,
                    success: true,
                  });
                } catch (e) {
                  await tx.insert(log.model).values({
                    userId: item.userId,
                    portfolioId: item.portfolioId,
                    success: false,
                    log: extractErrorMessage(e),
                  });
                } finally {
                  await supplementToCheckService.deleteAltById(item);
                }
              },
              { new: true }
            );
          } catch (e) {
            console.error(e);
          }
        }
      }
    );
  }

  async sendNotification(
    workflowId: string | undefined,
    type: NotificationType
  ) {
    if (!workflowId) return;

    const record = await this.getByWorkflowId(workflowId);
    await notificationService.create(this.getNotificationEntity(record, type));
  }

  async getDocumentsZipById(id: number) {
    const record = await this.getById(id);
    const zip = new AdmZip();

    const documents = await this.getDocumentsById(id);
    for (const document of documents) {
      if (document.obsolete) continue;

      if (document.fileUploadId) {
        const file = await fileUploadService.getByIdSafe(document.fileUploadId);
        zip.addLocalFile(
          path.resolve(env.FILE_UPLOAD_DIR, document.fileUploadId),
          undefined,
          `${document.type}_${file?.name}`
        );
        continue;
      }

      if (document.ttdFileId) {
        const { url } = await ttdService.querySignedDocument(
          document.ttdFileId
        );
        const arrayBuffer = await fetch(url).then((response) =>
          response.arrayBuffer()
        );
        zip.addFile(`${document.type}.pdf`, Buffer.from(arrayBuffer));
      }
    }

    return {
      filename: `补充协议_${record.portfolioName}_${record.identityNumber}_${record.username}.zip`,
      data: zip.toBuffer(),
    };
  }

  async terminate(input: z.infer<typeof terminateInputSchema>) {
    await withMutex(["supplementProcessService.terminate", input], async () => {
      const { id, operatorId } = input;

      const record = await this.getById(id);

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: supplementActiveStates,
        });

        if (!!record.orderNumber) {
          try {
            await ttdService.invalidateOrder(record.orderNumber);
          } catch (e) {
            console.error(e);
          }
        }

        await actor.stop(operatorId);
      });

      this.sendNotification(record.workflowId, "terminate");
    });
  }

  private async validateSupplementSign(orderNumber: string) {
    const { supplements } = await ttdService.queryOrder(orderNumber);

    if (!supplements?.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const order = await ttdService.querySupplements(orderNumber);
    if (!!order?.supplements?.length) {
      throw new TRPCError({ code: "FORBIDDEN", message: "补充协议未签署" });
    }
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id))
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>,
    ignoreTerminated?: boolean
  ) {
    if (!filter) return query;

    const { userId, portfolioId, status, state, date } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      conditions.push(eq(workflowTask.model.status, statusParsed.data));
    }

    const stateParsed = z.string().safeParse(state);
    if (stateParsed.success) {
      conditions.push(eq(workflowTask.model.state, stateParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      const predicate = timestampEqDate(model.createTime, dateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!ignoreTerminated) {
      conditions.push(ne(workflowTask.model.status, "stopped"));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }

  private withOrder<T extends MySqlSelect>(query: T) {
    query.orderBy(desc(model.createTime));
    return query;
  }

  private getNotificationEntity(
    record: Awaited<ReturnType<typeof this.getByWorkflowId>>,
    type: NotificationType
  ): Parameters<typeof notificationService.create>[0] {
    const { portfolioName, userId } = record;

    switch (type) {
      case "supplementSignEntry":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}补充协议签署通知`,
            content: `您持有的《${portfolioName}》有新的补充协议待签署。`,
            key: "交易提醒",
          },
        ];
      case "terminate":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}补充协议签署中止`,
            content: `您持有的《${portfolioName}》补充协议签署已中止，如有疑问请联系相关负责人。`,
            key: "交易提醒",
          },
        ];
    }
  }
}

export default new SupplementProcessService();
