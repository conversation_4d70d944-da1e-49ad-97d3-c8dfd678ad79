import { Status } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { v4 as uuid } from "uuid";
import {
  createActor as $createActor,
  Actor,
  ActorOptions,
  AnyStateMachine,
  EventFromLogic,
  Observer,
  SnapshotFrom,
} from "xstate";
import * as history from "../schema/workflowHistory";
import { model, select } from "../schema/workflowSnapshot";
import * as task from "../schema/workflowTask";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import { State } from "../utils/xstate";

class PersistedActor<T extends AnyStateMachine = any> {
  id: string;
  instance: Actor<T>;

  constructor(id: string, instance: Actor<T>) {
    this.id = id;
    this.instance = instance;
  }

  async start(operatorId: number, assigneeId: number, data?: unknown) {
    this.instance.start();
    await this.persist();
    await this.recordHistory(operatorId);
    await this.generateTask(assigneeId, data);

    return this;
  }

  async send(
    event: EventFromLogic<T>,
    operatorId: number,
    assigneeId: number | null,
    data?: unknown
  ) {
    this.instance.send(event);
    await this.persist();
    await this.recordHistory(operatorId);
    await this.generateTask(assigneeId, data);

    this.instance.stop();
  }

  async stop(operatorId: number) {
    this.instance.stop();
    await this.persist();
    await this.recordHistory(operatorId);
    await this.generateTask(null);
  }

  subscribe(observer: Observer<SnapshotFrom<Actor<T>>>) {
    return this.instance.subscribe(observer);
  }

  private async persist() {
    const { value, status } = this.instance.getSnapshot();
    const machineId = this.instance.logic.id;

    const snapshot = JSON.stringify(this.instance.getPersistedSnapshot());

    const data = { state: value, status, snapshot };

    await this.tx
      .insert(model)
      .values({ id: this.id, machineId, ...data })
      .onDuplicateKeyUpdate({ set: data });
  }

  private async recordHistory(operatorId: number) {
    const { value, status } = this.instance.getSnapshot();
    const machineId = this.instance.logic.id;

    await this.tx.insert(history.model).values({
      machineId,
      workflowId: this.id,
      state: value,
      status,
      operatorId,
    });
  }

  private async generateTask(assigneeId: number | null, data?: unknown) {
    const { value, status } = this.instance.getSnapshot();
    const machineId = this.instance.logic.id;

    const update = {
      machineId,
      state: value,
      status,
      assigneeId,
      data: data == null ? undefined : JSON.stringify(data),
    };

    const create = { workflowId: this.id, ...update };

    await this.tx
      .insert(task.model)
      .values(create)
      .onDuplicateKeyUpdate({ set: update });
  }

  private get tx() {
    const active = transaction.getActive();
    if (!active) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "No transaction available",
      });
    }

    return active;
  }
}

type GetSnapshotOptions<T extends AnyStateMachine> = {
  id: string;
  state: State<T>[];
  status?: Status[];
};

class WorkflowService extends Service {
  createActor<T extends AnyStateMachine>(
    logic: T,
    options?: ActorOptions<T>
  ): PersistedActor<T> {
    const id = uuid();

    const optionsWithInput = {
      ...(options || {}),
      input: { id, ...(options?.input || {}) },
    } as ActorOptions<T>;

    const instance = $createActor(logic, optionsWithInput);

    return new PersistedActor(id, instance);
  }

  async getSnapshot<T extends AnyStateMachine>(options: GetSnapshotOptions<T>) {
    const { id, state } = options;
    const status = options.status || ["active"];

    const [snapshot] = await this.db
      .select(select)
      .from(model)
      .where(eq(model.id, id));
    if (!snapshot) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    if (
      !status.includes(snapshot.status) ||
      !state.includes(snapshot.state as any)
    ) {
      throw new TRPCError({ code: "BAD_REQUEST" });
    }

    return snapshot;
  }

  async getActor<T extends AnyStateMachine>(
    machine: T,
    options: GetSnapshotOptions<T>
  ) {
    const { snapshot } = await this.getSnapshot(options);
    const actorOptions: ActorOptions<T> = { snapshot };
    const instance = $createActor(machine, actorOptions).start();

    return new PersistedActor(options.id, instance);
  }

  async getHistory(workflowId: string) {
    return this.db
      .select()
      .from(history.model)
      .where(eq(history.model.workflowId, workflowId));
  }
}

export default new WorkflowService();
