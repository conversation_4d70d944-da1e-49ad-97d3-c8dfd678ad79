import { TRPCError } from "@trpc/server";
import { compare, hash } from "bcrypt";
import { and, count, eq, inArray, like, ne, sql, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first, isEqual } from "lodash";
import { z } from "zod";
import {
  identityNumberSchema,
  listInputSchema,
  nameSchema,
  updateLoginInputSchema,
} from "../model/user";
import * as user from "../schema/user";
import * as userTag from "../schema/userTag";
import ttdService from "../service/ttd";
import { isDevEnv } from "../utils/dev";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import emailService from "./email";
import identityVerificationService from "./identityVerification";
import smsService from "./sms";
import * as trade from "../schema/trade";
import * as share from "../schema/share";
import * as netValue from "../schema/netValue";
import * as netValueDisclosureShare from "../schema/netValueDisclosure";
import type { AnyMySqlColumn } from "drizzle-orm/mysql-core";
import * as document from "../schema/document";
import * as riskTest from "../schema/riskTest";
import * as portfolioClient from "../schema/portfolioClient";
import * as newInvestorBasic from "../schema/newInvestorBasic";
const { ttdUserNumber, wechatOpenId, ...selfSelect } = user.select;
import type { InferSelectModel, InferInsertModel } from "drizzle-orm";

class UserService extends Service {
  private selfSelect = {
    ...selfSelect,
    passwordReset: sql`${user.model.password} is null`.mapWith(Boolean),
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(user.select).from(user.model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);

    let amountQuery = this.db
      .select({ value: count() })
      .from(user.model)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    const userIds = queryResult.map((it) => it.id);
    const [validatedMap, tagsMap] = await Promise.all([
      identityVerificationService.validatedByUserIds(userIds),
      this.getUserTags(userIds),
    ]);
    // const validatedMap =
    //   await identityVerificationService.validatedByUserIds(userIds);

    const result = queryResult.map((it) => ({
      ...it,
      validated: !!validatedMap[it.id],
      tags: tagsMap[it.id] || [],
    }));

    return paginationResult(result, amountQueryResult[0].value);
  }

  private async getUserTags(userIds: number[]) {
    if (userIds.length === 0) return {};

    const tags = await this.db
      .select({ userId: userTag.tagModel.userId, tag: userTag.tagModel.tag })
      .from(userTag.tagModel)
      .where(inArray(userTag.tagModel.userId, userIds));

    return tags.reduce(
      (acc, cur) => {
        if (!acc[cur.userId as number]) acc[cur.userId as number] = [];
        acc[cur.userId as number].push(cur.tag as string);
        return acc;
      },
      {} as Record<number, string[]>
    );
  }

  async getUserTag(userId: number) {
    const tagsMap = await this.getUserTags([userId]);
    return tagsMap[userId] || [];
  }

  async getByIdSafe(id: number) {
    const result = await this.db
      .select(user.select)
      .from(user.model)
      .where(eq(user.model.id, id));

    return first(result);
  }

  async getById(id: number) {
    const result = await this.getByIdSafe(id);
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getSelf(id: number) {
    const [result] = await this.db
      .select(this.selfSelect)
      .from(user.model)
      .where(eq(user.model.id, id));

    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getTtdUserNoByIdSafe(id: number) {
    const result = await this.getById(id);

    if (isDevEnv()) {
      return "1267797450320687104";
    }

    return result.ttdUserNumber;
  }

  async getTtdUserNoById(id: number) {
    const userNo = await this.getTtdUserNoByIdSafe(id);
    if (!userNo) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "请先完成实名认证" });
    }

    return userNo;
  }

  async getListByNameFuzzy(name: string) {
    if (name.length <= 1) {
      return [];
    }

    return await this.db
      .select(user.select)
      .from(user.model)
      .where(like(user.model.name, `%${name}%`));
  }

  async getListByIdentityNumbers(identityNumbers: string[]) {
    if (!identityNumbers.length) return [];

    return await this.db
      .select(user.select)
      .from(user.model)
      .where(inArray(user.model.identityNumber, identityNumbers));
  }

  async updatePassword(id: number, oldPassword: string, newPassword: string) {
    const [record] = await this.db
      .select({ password: user.model.password })
      .from(user.model)
      .where(eq(user.model.id, id));

    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    if (!!record.password && !(await compare(oldPassword, record.password))) {
      throw new TRPCError({ code: "FORBIDDEN", message: "旧密码不正确" });
    }

    const hashed = await hash(newPassword, 10);

    await this.db
      .update(user.model)
      .set({ password: hashed })
      .where(eq(user.model.id, id));
  }

  async updateUserNo(id: number) {
    const [record, userNo] = await Promise.all([
      this.getByIdSafe(id),
      this.getTtdUserNoByIdSafe(id),
    ]);

    // 暂时只支持个人投资者
    if (record?.type !== "个人") return;
    if (!!userNo) return;

    const { name, identityNumber, identityType } = record;
    if (!identityType) return;

    const newUserNo = await ttdService.createIndividualInvestor({
      name,
      identityNumber,
      identityType,
      valid: true,
    });

    await this.db
      .update(user.model)
      .set({ ttdUserNumber: newUserNo })
      .where(eq(user.model.id, record.id));
  }

  async sendCode(userId: number, type: "phone" | "email", value?: string) {
    if (isDevEnv()) return "123456";

    const user = await this.getById(userId);

    if (type === "email") {
      const email = value || user.email;
      if (!email) {
        throw new TRPCError({ code: "FORBIDDEN" });
      }

      return await emailService.getVerifier(email);
    } else if (type === "phone") {
      const phone = value || user.phone;
      if (!phone) {
        throw new TRPCError({ code: "FORBIDDEN" });
      }

      return await smsService.getVerifier(phone);
    } else {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }
  }

  async verifyCode(code: string, validation: string | undefined) {
    if (!validation) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "请先获取验证码" });
    }

    if (!isDevEnv()) {
      if (code !== validation) {
        throw new TRPCError({ code: "FORBIDDEN", message: "验证码错误" });
      }
    }
  }

  async updateLogin(
    input: z.infer<typeof updateLoginInputSchema>,
    validation: z.infer<typeof updateLoginInputSchema> | undefined,
    userId: number
  ) {
    if (!validation) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "请先获取验证码" });
    }

    const data: z.infer<typeof user.updateSchema> = {};

    if (input.type === "phone") {
      const [{ amount }] = await this.db
        .select({ amount: count() })
        .from(user.model)
        .where(
          and(eq(user.model.phone, input.phone), ne(user.model.id, userId))
        );
      if (!!amount) {
        throw new TRPCError({ code: "FORBIDDEN", message: "该手机号码已注册" });
      }
      data.phone = input.phone;
    } else if (input.type === "email") {
      const [{ amount }] = await this.db
        .select({ amount: count() })
        .from(user.model)
        .where(
          and(eq(user.model.email, input.email), ne(user.model.id, userId))
        );
      if (!!amount) {
        throw new TRPCError({ code: "FORBIDDEN", message: "该邮件地址已注册" });
      }
      data.email = input.email;
    }

    if (!isDevEnv()) {
      if (!isEqual(input, validation)) {
        throw new TRPCError({ code: "FORBIDDEN", message: "验证码错误" });
      }
    }

    await this.db.update(user.model).set(data).where(eq(user.model.id, userId));
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { name, identityNumber } = filter;
    const conditions: SQL[] = [];

    const nameParsed = nameSchema.min(1).safeParse(name);
    if (nameParsed.success) {
      conditions.push(eq(user.model.name, nameParsed.data));
    }

    const identityNumberParsed = identityNumberSchema
      .min(1)
      .safeParse(identityNumber);
    if (identityNumberParsed.success) {
      conditions.push(eq(user.model.identityNumber, identityNumberParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }

  // async createUserTag(name: string, identityNumber: string) {
  //   const account = await this.db
  //     .select(user.select)
  //     .from(user.model)
  //     .where(
  //       and(
  //         eq(user.model.name, name),
  //         eq(user.model.identityNumber, identityNumber)
  //       )
  //     );
  //   if (account.length) {
  //     const trades = await this.db
  //       .select()
  //       .from(trade.model)
  //       .where(eq(trade.model.clientIdentityNumber, account[0].identityNumber));

  //     if (trades.length == 0) {
  //       const existingTag = await this.db
  //         .select(userTag.select)
  //         .from(userTag.tagModel)
  //         .where(
  //           and(
  //             eq(userTag.tagModel.userId, account[0].id)
  //             // eq(userTag.tagModel.tag, "直销专业")
  //           )
  //         );

  //       if (existingTag.length == 0) {
  //         await this.db.insert(userTag.tagModel).values({
  //           userId: account[0].id,
  //           tag: "未持仓客户",
  //         });
  //       } else {
  //         await this.db
  //           .update(userTag.tagModel)
  //           .set({
  //             userId: account[0].id,
  //             tag: "未持仓客户",
  //           })
  //           .where(eq(userTag.tagModel.id, existingTag[0].id));
  //       }
  //       return;
  //       // return "我是未持仓客户";
  //     }
  //     let hasDirectSale = false;

  //     for (const trade of trades) {
  //       if (trade.agency === "直销") {
  //         hasDirectSale = true;
  //         break;
  //       }
  //     }
  //     if (hasDirectSale) {
  //       if (account[0].classification == "专业投资者") {
  //         this.updateUserTag(account[0].id, "直销专业");
  //         // await this.db.insert(userTag.tagModel).values({
  //         //   userId: account[0].id,
  //         //   tag: "直销专业",
  //         // });
  //       }
  //       if (account[0].classification == "普通投资者") {
  //         const portfolioClientRes = await this.db
  //           .select()
  //           .from(portfolioClient.model)
  //           .where(
  //             eq(
  //               portfolioClient.model.identityNumber,
  //               account[0].identityNumber
  //             )
  //           );
  //         if (portfolioClientRes.length != 0) {
  //           const newInvestorBasicRes = await this.db
  //             .select()
  //             .from(newInvestorBasic.model)
  //             .where(
  //               eq(
  //                 newInvestorBasic.model.portfolio_client_id,
  //                 portfolioClientRes[0].id
  //               )
  //             );
  //           if (newInvestorBasicRes.length != 0) {
  //             const assessmentTime = newInvestorBasicRes[0].assessment_time;
  //             if (assessmentTime) {
  //               const threeYearsLater = new Date(assessmentTime);
  //               threeYearsLater.setFullYear(threeYearsLater.getFullYear() + 3);

  //               const now = new Date();
  //               const today = new Date(
  //                 now.getFullYear(),
  //                 now.getMonth(),
  //                 now.getDate()
  //               );

  //               if (today <= threeYearsLater) {
  //                 await this.updateUserTag(account[0].id, "直销普通");
  //               } else {
  //                 // await this.updateUserTag(account[0].id, "直销普通已过期");
  //               }
  //             }
  //             // else {
  //             //     await this.updateUserTag(account[0].id, "直销普通无评估时间");
  //             // }
  //           }
  //         }
  //       }
  //       if (!account[0].classification) {
  //         const portfolioClientRes = await this.db
  //           .select()
  //           .from(portfolioClient.model)
  //           .where(
  //             eq(
  //               portfolioClient.model.identityNumber,
  //               account[0].identityNumber
  //             )
  //           );
  //         if (portfolioClientRes.length != 0) {
  //           if (portfolioClientRes[0].classification == "专业投资者") {
  //             this.updateUserTag(account[0].id, "直销专业");
  //           }
  //           if (portfolioClientRes[0].classification == "普通投资者") {
  //             const portfolioClientRes = await this.db
  //               .select()
  //               .from(portfolioClient.model)
  //               .where(
  //                 eq(
  //                   portfolioClient.model.identityNumber,
  //                   account[0].identityNumber
  //                 )
  //               );
  //             if (portfolioClientRes.length != 0) {
  //               const newInvestorBasicRes = await this.db
  //                 .select()
  //                 .from(newInvestorBasic.model)
  //                 .where(
  //                   eq(
  //                     newInvestorBasic.model.portfolio_client_id,
  //                     portfolioClientRes[0].id
  //                   )
  //                 );
  //               if (newInvestorBasicRes.length != 0) {
  //                 const assessmentTime = newInvestorBasicRes[0].assessment_time;
  //                 if (assessmentTime) {
  //                   const threeYearsLater = new Date(assessmentTime);
  //                   threeYearsLater.setFullYear(
  //                     threeYearsLater.getFullYear() + 3
  //                   );

  //                   const now = new Date();
  //                   const today = new Date(
  //                     now.getFullYear(),
  //                     now.getMonth(),
  //                     now.getDate()
  //                   );

  //                   if (today <= threeYearsLater) {
  //                     await this.updateUserTag(account[0].id, "直销普通");
  //                   } else {
  //                     // await this.updateUserTag(account[0].id, "直销普通已过期");
  //                   }
  //                 }
  //                 // else {
  //                 //     await this.updateUserTag(account[0].id, "直销普通无评估时间");
  //                 // }
  //               }
  //             }
  //           }
  //         }
  //       }
  //     }
  //     if (!hasDirectSale) {
  //       const shares = await this.db
  //         .select()
  //         .from(share.model)
  //         .where(
  //           eq(share.model.clientIdentityNumber, account[0].identityNumber)
  //         );

  //       if (shares.length == 0) {
  //         return;
  //       }
  //       const portfolioIds = shares.map((s) => s.portfolioId);
  //       const disclosureDates = await this.db
  //         .select()
  //         .from(netValueDisclosureShare.model)
  //         .where(
  //           inArray(
  //             netValueDisclosureShare.model.portfolioId as AnyMySqlColumn,
  //             portfolioIds as number[]
  //           )
  //         );
  //       const netValues = await this.db
  //         .select()
  //         .from(netValue.model)
  //         .where(
  //           and(
  //             inArray(
  //               netValue.model.portfolioId as AnyMySqlColumn,
  //               portfolioIds as number[]
  //             ),
  //             inArray(
  //               sql`DATE(${netValue.model.date})`,
  //               disclosureDates.map((d) => sql`DATE(${d.netValueDate})`)
  //             )
  //           )
  //         );
  //       const total = shares.reduce((sum, share) => {
  //         const disclosure = disclosureDates.find(
  //           (d) => d.portfolioId === share.portfolioId
  //         );
  //         const date = disclosure?.netValueDate;

  //         const netValue = date
  //           ? netValues.find(
  //               (nv) =>
  //                 nv.portfolioId === share.portfolioId &&
  //                 formatDate(nv.date) === formatDate(date)
  //             )?.netValue || 0
  //           : 0;

  //         return sum + Number(share.realShares ?? 0) * Number(netValue);
  //       }, 0);
  //       if (Number(total.toFixed(2)) >= 5000000) {

  //       }
  //       if (Number(total.toFixed(2)) >= 3000000) {
  //         const riskRes = await this.db
  //           .select()
  //           .from(riskTest.model)
  //           .where(eq(riskTest.model.userId, account[0].id))
  //           .orderBy(sql`${riskTest.model.createTime} DESC`);
  //         if (riskRes.length != 0) {
  //           const latestTest = riskRes[0];
  //           const score = latestTest.score;
  //           if (score >= 90) {
  //             this.updateUserTag(account[0].id, "代销普通C5");
  //           }
  //         } else {
  //         }
  //       } else {
  //         const documents = await this.db
  //           .select()
  //           .from(document.model)
  //           .where(eq(document.model.userId, account[0].id));

  //         let hasInvestmentCert = false;
  //         let hasAssetCert = false;
  //         if (documents.length != 0) {
  //           for (const doc of documents) {
  //             if (doc.type == "投资经历证明材料") {
  //               hasInvestmentCert = true;
  //             }
  //             if (doc.type == "资产证明") {
  //               hasAssetCert = true;
  //             }
  //           }
  //         }

  //         if (hasInvestmentCert && hasAssetCert) {
  //           this.updateUserTag(account[0].id, "代销认证");
  //         } else {
  //           this.updateUserTag(account[0].id, "代销");
  //         }
  //       }
  //     }

  //     // return trades;
  //   } else {
  //     // return "怎么获取不到哇";
  //   }
  // }

  private async getAccount(name: string, identityNumber: string) {
    return this.db
      .select()
      .from(user.model)
      .where(
        and(
          eq(user.model.name, name),
          eq(user.model.identityNumber, identityNumber)
        )
      );
  }

  private async getTrades(identityNumber: string) {
    return this.db
      .select()
      .from(trade.model)
      .where(eq(trade.model.clientIdentityNumber, identityNumber));
  }

  private async handleNoPositionClient(
    account: InferSelectModel<typeof user.model>
  ) {
    const existingTag = await this.db
      .select(userTag.select)
      .from(userTag.tagModel)
      .where(eq(userTag.tagModel.userId, account.id));

    const tagOperation =
      existingTag.length === 0
        ? this.db.insert(userTag.tagModel).values({
            userId: account.id,
            tag: "未持仓客户",
          })
        : this.db
            .update(userTag.tagModel)
            .set({ tag: "未持仓客户" })
            .where(eq(userTag.tagModel.id, existingTag[0].id));

    await tagOperation;
  }

  private async handleDirectSale(account: InferSelectModel<typeof user.model>) {
    if (account.classification === "专业投资者") {
      this.updateUserTag(account.id, "直销专业");
      return;
    }

    const portfolioClient = await this.getPortfolioClient(
      account.identityNumber
    );
    // if (!portfolioClient) return;

    if (account.classification === "普通投资者") {
      await this.handleOrdinaryInvestor(account, portfolioClient.id);
    } else if (!account.classification) {
      await this.handleUnclassifiedInvestor(account, portfolioClient);
    }
  }

  private async getPortfolioClient(identityNumber: string) {
    const [result] = await this.db
      .select()
      .from(portfolioClient.model)
      .where(eq(portfolioClient.model.identityNumber, identityNumber));
    return result;
  }

  private async handleOrdinaryInvestor(
    account: InferSelectModel<typeof user.model>,
    portfolioClientId: number
  ) {
    const investorBasic = await this.getNewInvestorBasic(portfolioClientId);

    const [latestRiskTest] = await this.db
      .select()
      .from(riskTest.model)
      .where(eq(riskTest.model.userId, account.id))
      .orderBy(sql`${riskTest.model.createTime} DESC`)
      .limit(1);
    if (!investorBasic?.assessment_time) return;


    const isWithin3Years = this.isWithinThreeYears(
      investorBasic.assessment_time
    );
    if (isWithin3Years && latestRiskTest?.score >= 90) {
      this.updateUserTag(account.id, "直销普通C5");
    }
    if (isWithin3Years && latestRiskTest?.score < 90){
      this.updateUserTag(account.id,"直销普通")
    }
    if (!isWithin3Years){
      this.updateUserTag(account.id,"直销普通已过期")
    }
    // const tag = isWithin3Years ? "直销普通" : "直销普通已过期";
    // this.updateUserTag(account.id, tag);
  }

  private async getNewInvestorBasic(portfolioClientId: number) {
    const [result] = await this.db
      .select()
      .from(newInvestorBasic.model)
      .where(eq(newInvestorBasic.model.portfolio_client_id, portfolioClientId));
    return result;
  }

  private isWithinThreeYears(date: Date) {
    const threeYearsLater = new Date(date);
    threeYearsLater.setFullYear(threeYearsLater.getFullYear() + 3);
    return new Date() <= threeYearsLater;
  }

  private async handleUnclassifiedInvestor(
    account: user.User,
    portfolioClient: portfolioClient.PortfolioClient
  ) {
    if (portfolioClient.classification === "专业投资者") {
      this.updateUserTag(account.id, "直销专业");
    } else if (portfolioClient.classification === "普通投资者") {
      this.handleOrdinaryInvestor(account, portfolioClient.id);
    }
  }

  private async handleIndirectSale(account: user.User) {
    const shares = await this.getClientShares(account.identityNumber);
    if (shares.length === 0) return;

    const totalAssets = await this.calculateTotalAssets(shares);
    await this.processAssetClassification(account.id, totalAssets);
  }

  private async getClientShares(identityNumber: string) {
    return this.db
      .select()
      .from(share.model)
      .where(eq(share.model.clientIdentityNumber, identityNumber));
  }

  private async calculateTotalAssets(
    shares: (typeof share.model.$inferSelect)[]
  ) {
    const portfolioIds = shares.map((s) => s.portfolioId);

    const [disclosureDates, netValues] = await Promise.all([
      this.db
        .select()
        .from(netValueDisclosureShare.model)
        .where(
          inArray(
            netValueDisclosureShare.model.portfolioId as AnyMySqlColumn,
            portfolioIds as number[]
          )
        ),
      this.db
        .select()
        .from(netValue.model)
        .where(
          inArray(
            netValue.model.portfolioId as AnyMySqlColumn,
            portfolioIds as number[]
          )
        ),
    ]);

    return shares.reduce((sum, share) => {
      const disclosure = disclosureDates.find(
        (d) => d.portfolioId === share.portfolioId
      );
      const date = disclosure?.netValueDate;

      const netValue = date
        ? netValues.find(
            (nv) =>
              nv.portfolioId === share.portfolioId &&
              formatDate(nv.date) === formatDate(date)
          )?.netValue || 0
        : 0;

      return sum + Number(share.realShares ?? 0) * Number(netValue);
    }, 0);
  }

  private async processAssetClassification(
    userId: number,
    totalAssets: number
  ) {
    const roundedTotal = Number(totalAssets.toFixed(2));
    if (roundedTotal >= 5000000) {
      const over2Years = await this.handleUltraHighValueClient(userId);
      if (over2Years) {
        const documentRes = await this.db
          .select()
          .from(document.model)
          .where(eq(document.model.userId, userId));
        if (documentRes.length != 0) {
          const haveDos = documentRes.some((doc) => {
            doc.type == "专业投资者告知及确认书";
          });
          if (haveDos) {
            this.updateUserTag(userId, "代销专业");
          }
        }
      }
    }
    if (roundedTotal >= 3000000) {
      await this.handleHighValueClient(userId);
    } else {
      await this.handleRegularClient(userId);
    }
  }

  private async handleUltraHighValueClient(userId: number) {
    const userRecord = await this.getById(userId);
    const trades = await this.db
      .select()
      .from(trade.model)
      .where(
        and(
          eq(trade.model.clientIdentityNumber, userRecord.identityNumber),
          eq(trade.model.confStatus, "确认成功"),
          inArray(trade.model.businessType, ["认购", "基金成立", "申购"])
        )
      )
      .orderBy(sql`${trade.model.confDate} ASC`)
      .limit(1);

    if (!trades[0]?.confDate) {
      // await this.updateUserTag(userId, "代销高净值");
      return false; // 默认视为未超期
    }
    const confDate = new Date(trades[0].confDate);
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
    const isOverTwoYears = confDate <= twoYearsAgo;
    return isOverTwoYears;
  }
  private async handleHighValueClient(userId: number) {
    const [latestRiskTest] = await this.db
      .select()
      .from(riskTest.model)
      .where(eq(riskTest.model.userId, userId))
      .orderBy(sql`${riskTest.model.createTime} DESC`)
      .limit(1);

    if (latestRiskTest?.score >= 90) {
      this.updateUserTag(userId, "代销普通C5");
    }
  }

  private async handleRegularClient(userId: number) {
    const documents = await this.db
      .select()
      .from(document.model)
      .where(eq(document.model.userId, userId));

    const certs = {
      investment: documents.some((d) => d.type === "投资经历证明材料"),
      asset: documents.some((d) => d.type === "资产证明"),
    };

    const tag = certs.investment && certs.asset ? "代销认证" : "代销";
    this.updateUserTag(userId, tag);
  }

  async createUserTag(name: string, identityNumber: string) {
    const account = await this.getAccount(name, identityNumber);
    if (!account.length) return;

    const trades = await this.getTrades(account[0].identityNumber);
    if (trades.length === 0) {
      await this.handleNoPositionClient(account[0]);
      return;
    }

    const hasDirectSale = trades.some((t) => t.agency == "直销");
    hasDirectSale
      ? await this.handleDirectSale(account[0])
      : await this.handleIndirectSale(account[0]);
  }

  async updateUserTag(userId: number, tag: string) {
    const existingTag = await this.db
      .select()
      .from(userTag.tagModel)
      .where(eq(userTag.tagModel.userId, userId));

    if (existingTag.length === 0) {
      await this.db.insert(userTag.tagModel).values({ userId, tag });
    } else {
      await this.db
        .update(userTag.tagModel)
        .set({ tag })
        .where(eq(userTag.tagModel.id, existingTag[0].id));
    }
  }
}
const formatDate = (d: Date | string | null | undefined) => {
  if (!d) return "";
  return new Date(d).toISOString().split("T")[0];
};

export default new UserService();
