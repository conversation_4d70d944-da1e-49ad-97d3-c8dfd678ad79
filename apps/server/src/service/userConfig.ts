import { eq } from "drizzle-orm";
import { first } from "lodash";
import { z } from "zod";
import { updateInputSchema } from "../model/userConfig";
import { model, select } from "../schema/userConfig";
import { Service } from "../utils/service";

class UserConfigService extends Service {
  async getByUserId(userId: number) {
    const result = await this.db
      .select(select)
      .from(model)
      .where(eq(model.userId, userId));

    return first(result);
  }

  async update(input: z.infer<typeof updateInputSchema>) {
    const { userId, ...rest } = input;
    await this.db
      .insert(model)
      .values(input)
      .onDuplicateKeyUpdate({ set: rest });
  }
}

export default new UserConfigService();
