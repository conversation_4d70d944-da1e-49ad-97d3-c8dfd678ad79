import { eq } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { updateInputSchema } from "../model/taxDeclaration";
import { model, select } from "../schema/taxDeclaration";
import * as user from "../schema/user";
import { Service } from "../utils/service";

class TaxDeclaration extends Service {
  private select = {
    ...select,
    userType: user.model.type,
  };

  async getByUserId(userId: number) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = this.withJoin(query);
    query = query.where(eq(model.userId, userId));

    const [record] = await query;

    return { item: record };
  }

  async update(userId: number, input: z.infer<typeof updateInputSchema>) {
    const updateData = { formData: JSON.stringify(input) };
    const insertData = { ...updateData, userId };

    await this.db
      .insert(model)
      .values(insertData)
      .onDuplicateKeyUpdate({ set: updateData });
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query.leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }
}

export default new TaxDeclaration();
