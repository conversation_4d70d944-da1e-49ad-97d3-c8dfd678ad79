import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import { and, count, desc, eq, not } from "drizzle-orm";
import { z } from "zod";
import {
  createInputSchema,
  createWordInputSchema,
  updateWordsInputSchema,
} from "../model/videoConfig";
import * as videoConfig from "../schema/videoConfig";
import * as videoConfigWords from "../schema/videoConfigWords";
import ttdService from "../service/ttd";
import { withMutex } from "../utils/lock";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import portfolioService from "./portfolio";
import userService from "./user";

class VideoConfigService extends Service {
  async getWordsList() {
    return await this.db
      .select(videoConfigWords.select)
      .from(videoConfigWords.model);
  }

  async getWordsById(id: number) {
    const [result] = await this.db
      .select()
      .from(videoConfigWords.model)
      .where(eq(videoConfigWords.model.id, id));
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getWordsByName(name: string) {
    const [result] = await this.db
     .select()
     .from(videoConfigWords.model)
     .where(eq(videoConfigWords.model.name, name));
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getLinkByTargetCode(targetCode: string) {
    const [result] = await this.db
      .select()
      .from(videoConfig.model)
      .where(eq(videoConfig.model.targetCode, targetCode));
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return { link: result.openLink };
  }

  async create(input: z.infer<typeof createInputSchema>) {
    const { wordsId, orderNumber } = input;

    const predicate =
      wordsId == null
        ? eq(videoConfigWords.model.isDefault, true)
        : eq(videoConfigWords.model.id, wordsId);

    const [result] = await this.db
      .select()
      .from(videoConfigWords.model)
      .where(predicate)
      .orderBy(desc(videoConfigWords.model.createTime))
      .limit(1);

    const words = result?.words;
    const model = result?.model;
    if (!words || !model) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    const wordsPopulated = await this.populateVariables(words, input);
    return await withMutex(["videoConfig.create", input], async () => {
      const response = await ttdService.createVideoConfig(
        model,
        wordsPopulated
      );

      await this.db.insert(videoConfig.model).values({
        userId: input.userId,
        portfolioId: input.portfolioId,
        targetCode: response.targetCode,
        recordModel: response.recodeModel,
        words: response.words,
        intervalTime: response.intervalTime,
        playSpeed: response.playSpeed,
        configId: response.configId,
        manageCode: response.manageCode,
        openLink: response.openlink,
        orderNumber,
      });

      return { targetCode: response.targetCode, openLink: response.openlink };
    });
  }

  async createWords(input: z.infer<typeof createWordInputSchema>) {
    const { name, model, isDefault } = input;
    const words = input.words.join("@");

    await withMutex("videoConfig.words", async () => {
      await transaction.run(async (tx) => {
        if (isDefault) {
          await tx.update(videoConfigWords.model).set({ isDefault: false });
          const [result] = await tx
            .insert(videoConfigWords.model)
            .values({ name, words, model, isDefault: true });
          return result.insertId;
        }
      });
    });
  }

  async updateWords(input: z.infer<typeof updateWordsInputSchema>) {
    const { id, name, isDefault } = input;
    const words = input.words.join("@");

    await withMutex("videoConfig.words", async () => {
      const predicate = eq(videoConfigWords.model.id, id);

      const [record] = await this.db
        .select()
        .from(videoConfigWords.model)
        .where(predicate);

      if (!record) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }

      await transaction.run(async (tx) => {
        if (isDefault) {
          await tx.update(videoConfigWords.model).set({ isDefault: false });
        } else {
          const [{ amount }] = await tx
            .select({ amount: count() })
            .from(videoConfigWords.model)
            .where(
              and(not(predicate), eq(videoConfigWords.model.isDefault, true))
            );

          if (!amount) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "请至少设置一个默认话术",
            });
          }
        }
        await tx
          .update(videoConfigWords.model)
          .set({ name, words, isDefault })
          .where(predicate);
      });
    });
  }

  private async populateVariables(
    words: string,
    input: z.infer<typeof createInputSchema>
  ) {
    const { userId, portfolioId } = input;
    const user = await userService.getById(userId);
    const portfolio = await portfolioService.getByIdInternal(portfolioId);

    return words
      .slice()
      .replace("{投资者名称}", user.name)
      .replace("{投资者证件类型}", user.identityType || "")
      .replace("{投资者证件号码}", user.identityNumber)
      .replace("{基金名称}", portfolio?.name || "")
      .replace("{投资者风险等级}", user.riskLevel || "")
      .replace("{基金风险等级}", portfolio?.riskLevel || "")
      .replace("{录制日期}", dayjs().format("YYYY年MM月DD日"));
  }
}

export default new VideoConfigService();
