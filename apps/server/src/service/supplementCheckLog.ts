import { and, count, desc, eq, like, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { listInputSchema } from "../model/supplementCheckLog";
import * as portfolio from "../schema/portfolio";
import { model, select } from "../schema/supplementCheckLog";
import * as user from "../schema/user";
import { timestampEqDate } from "../utils/drizzle";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";

class SupplementCheckService extends Service {
  private select = {
    ...select,
    name: user.model.name,
    identityNumber: user.model.identityNumber,
    portfolioName: portfolio.model.name,
    portfolioNo: portfolio.model.no,
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();

    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input);
    query = this.withOrder(query);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(user.model, eq(model.userId, user.model.id))
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id));

    return query;
  }

  private withOrder<T extends MySqlSelect>(query: T) {
    query.orderBy(desc(model.createTime));
    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { identityNumber, name, date, portfolioId, success } = filter;
    const conditions: SQL[] = [];

    const nameParsed = z.string().min(1).safeParse(name);
    if (nameParsed.success) {
      conditions.push(like(user.model.name, `%${nameParsed.data}%`));
    }

    const identityNumberParsed = z.string().min(1).safeParse(identityNumber);
    if (identityNumberParsed.success) {
      conditions.push(eq(user.model.identityNumber, identityNumberParsed.data));
    }

    const portfolioIdParsed = z.number().safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      const predicate = timestampEqDate(model.createTime, dateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    const successParsed = z.boolean().safeParse(success);
    if (successParsed.success) {
      conditions.push(eq(model.success, successParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new SupplementCheckService();
