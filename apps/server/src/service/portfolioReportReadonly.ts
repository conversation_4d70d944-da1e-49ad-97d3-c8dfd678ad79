import { SQL, and, count, desc, eq } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { inputSchema } from "../model/portfolioReportReadonly";
import * as fileUpload from "../schema/fileUpload";
import * as portfolioReport from "../schema/portfolioReport";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import portfolioService from "./portfolio";

class PortfolioReportReadonlyService extends Service {
  private select = {
    id: portfolioReport.model.id,
    date: portfolioReport.model.date,
    fileUploadId: portfolioReport.model.fileUploadId,
    name: fileUpload.model.name,
  };

  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    const access = await portfolioService.getPortfolioAccess({
      id: input.portfolioId,
      identityNumber,
    });

    if (!access?.reportAccess) {
      return { items: [], amount: 0 };
    }

    let query = this.db
      .select(this.select)
      .from(portfolioReport.model)
      .$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);
    query = query.orderBy(desc(portfolioReport.model.date));

    let amountQuery = this.db
      .select({ value: count() })
      .from(portfolioReport.model)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>
  ) {
    query.leftJoin(
      fileUpload.model,
      eq(portfolioReport.model.fileUploadId, fileUpload.model.id)
    );

    const conditions: SQL[] = [
      eq(portfolioReport.model.portfolioId, input.portfolioId),
      eq(portfolioReport.model.published, true),
    ];
    query.where(and(...conditions));

    return query;
  }
}

export default new PortfolioReportReadonlyService();
