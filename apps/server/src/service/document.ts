import { DocumentType } from "@portfolio-service/schema/document";
import { TRPCError } from "@trpc/server";
import { and, eq, inArray, SQL } from "drizzle-orm";
import { z } from "zod";
import { createInputSchema, listInputSchema } from "../model/document";
import { model, select } from "../schema/document";
import * as fileUpload from "../schema/fileUpload";
import { Service } from "../utils/service";
import * as user from "../schema/user";
import ttdService from "./ttd";

class DocumentService extends Service {
  private select = {
    ...select,
    fileUploadName: fileUpload.model.name,
    username: user.model.name,
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = query
      .leftJoin(fileUpload.model, eq(model.fileUploadId, fileUpload.model.id))
      .leftJoin(user.model, eq(model.userId, user.model.id));

    query = query.where(
      and(eq(model.userId, input.userId), eq(model.type, input.type))
    );

    return await query;
  }

  async getByIdSafe(id: number | undefined) {
    if (id == null) {
      return;
    }

    const [result] = await this.db.select().from(model).where(eq(model.id, id));
    return result;
  }

  async getById(id: number | undefined, userId?: number) {
    const result = await this.getByIdSafe(id);
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }
    if (userId != null && result.userId !== userId) {
      throw new TRPCError({ code: "FORBIDDEN" });
    }

    return result;
  }

  async getByIds(ids: number[]) {
    if (!ids.length) {
      return [];
    }

    return await this.db.select().from(model).where(inArray(model.id, ids));
  }

  async getTtdFileIdById(id: number, userId?: number) {
    const record = await this.getById(id, userId);

    const fileId = record.ttdFileId;
    if (!fileId) {
      throw new TRPCError({ code: "BAD_REQUEST" });
    }

    return { fileId };
  }

  async getTtdLinkById(id: number, userId?: number) {
    const record = await this.getById(id, userId);

    const fileId = record.ttdFileId;
    if (!fileId) {
      throw new TRPCError({ code: "BAD_REQUEST" });
    }

    const { url } = await ttdService.querySignedDocument(fileId);
    return { url };
  }

  async getTtdFileById(id: number) {
    const record = await this.getById(id);

    const fileId = record.ttdFileId;
    if (!fileId) {
      throw new TRPCError({ code: "BAD_REQUEST" });
    }

    return await ttdService.querySignedDocument(fileId);
  }

  async create(input: z.infer<typeof createInputSchema>) {
    if (!input.fileUploadId && !input.ttdFileId) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const [result] = await this.db.insert(model).values(input);
    return result.insertId;
  }

  async makeObsolete(ids: number[], types: DocumentType[]) {
    const predicate: SQL[] = [];
    if (!!ids.length) {
      predicate.push(inArray(model.id, ids));
    }

    if (!!types.length) {
      predicate.push(inArray(model.type, types));
    }

    if (!predicate.length) return;
    predicate.push(eq(model.obsolete, false));

    await this.db
      .update(model)
      .set({ obsolete: true })
      .where(and(...predicate));
  }
}

export default new DocumentService();
