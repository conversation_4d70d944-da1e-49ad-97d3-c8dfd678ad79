import { TRPCError } from "@trpc/server";
import { and, count, eq, gt, inArray, like, or, SQL, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { Dictionary, keyBy } from "lodash";
import { number, z } from "zod";
import { inputSchema, PortfolioAccess } from "../model/portfolio";
import * as netValue from "../schema/netValue";
import * as disclosure from "../schema/netValueDisclosure";
import * as portfolio from "../schema/portfolio";
import * as userFollowedProducts from "../schema/userFollowedProducts";
// import { model } from "../schema/portfolio";
import * as complianceConf from "../schema/portfolioComplianceConf";
import * as share from "../schema/share";
import { convertDouble } from "../utils/db";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import * as indexPrice from "../schema/indexPrice";
import { tradeDateUtils, openDay as getOpenDay } from "open-day";
import * as tradeDate from "../schema/tradeDate";
import { Portfolio } from "../schema/portfolio";
import complianceService from "./portfolioComplianceConf";
import moment, { Dayjs } from "dayjs";
import { Dayjs as Moment } from "dayjs";
import { findLast, meanBy, sum } from "lodash";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
dayjs.extend(isSameOrBefore);

const { openDay, ...byIdSelect } = portfolio.select;
export interface BaseReportData {
  date: Moment;
  netValue: number;
  backfill: boolean;
  beforeHigh: number;
  beforeHighDate: Moment;
  beforeLow: number;
  beforeLowDate: Moment;
  return: number;
  yearReturn: number;
  seasonReturn: number;
  monthReturn: number;
  weekReturn: number;
  dayReturn: number;
}
export interface NetValues {
  // 日期
  date: string;
  // 单位净值
  netValue: number;
  // 累计净值
  cumNetValue: number;
  // 总资产
  assetNet: number;
}

export type StartDateType = {
  type: string;
  date?: string;
};

export interface SomeNetValue {
  date: Dayjs;
  netValue: number;
}
type SequenceType = "day" | "week" | "month";
export interface ExtendReportData {
  // 最大回撤
  maxRetracement: number;
  // 年化收益
  annualizedReturn: number;
  // 年化标准差
  annualizedStd: number;
  // 年化下半标准差
  annualizedHalfStd: number;
  sharpe: number | null;
  sortino: number | null;
  doNotShow: boolean;
}
export type CalcProps = {
  selectedProductId: number;
  startDate: StartDateType;
  endDate: string;
  intervalType: string;
  valueType: string;
  indexType: string;
};

class PortfolioCalculator extends Service {
  listToDict<T, U>(
    data: T[],
    k: (x: T) => string,
    v: (x: T) => U
  ): Record<string, U> {
    const result: Record<string, U> = {};
    if (data) {
      data.forEach((x) => {
        result[k(x)] = v(x);
      });
    }
    return result;
  }

  async calculateNetValue(portfolioId: number, totalAssetNet: boolean) {
    // 获取当前产品净值
    const mainNetValues = await this.db
      .select()
      .from(netValue.model)
      .where(eq(netValue.model.portfolioId, portfolioId))
      .orderBy(netValue.model.date);

    if (!totalAssetNet) return mainNetValues;

    // 查找结构化母产品
    const parent = await this.db
      .select()
      .from(portfolio.model)
      .where(
        or(
          eq(portfolio.model.classAShareId, portfolioId),
          eq(portfolio.model.classBShareId, portfolioId),
          eq(portfolio.model.classCShareId, portfolioId)
        )
      )
      .then((res) => res[0]);

    // 获取关联产品ID
    const relatedIds = [
      parent?.classAShareId,
      parent?.classBShareId,
      parent?.classCShareId,
    ].filter((id) => id && id !== portfolioId);

    // 合并关联产品净值
    for (const relatedId of relatedIds) {
      const relatedNetValues = await this.db
        .select()
        .from(netValue.model)
        .where(eq(netValue.model.portfolioId, relatedId as number))
        .orderBy(netValue.model.date);

      let index = 0;
      for (const mainValue of mainNetValues) {
        while (
          index < relatedNetValues.length &&
          dayjs(relatedNetValues[index].date).isSameOrBefore(mainValue.date)
        ) {
          index++;
        }
        if (index > 0) {
          mainValue.netValue = (
            Number(mainValue.netValue || 0) +
            Number(relatedNetValues[index - 1].netValue || 0)
          ).toString();
        }
      }
    }

    return mainNetValues;
  }

  async getIndexPrice(name: string) {
    const result = await this.db
      .select()
      .from(indexPrice.model)
      .where(eq(indexPrice.model.name, name))
      .orderBy(indexPrice.model.date);

    return {
      data: result.map((item) => ({
        date: item.date,
        price: item.price,
      })),
    };
  }

  private async getTradeDate() {
    const result = await this.db
      .select()
      .from(tradeDate.model)
      .orderBy(tradeDate.model.date);

    const list = result
      .map((x) => dayjs(x.date))
      .sort((a, b) => a.valueOf() - b.valueOf());
    const dict = this.listToDict(
      list,
      (x) => x.format("YYYYMMDD"),
      () => true
    );
    return {
      list,
      dict,
    };
  }
  getUtils(dates: Dayjs[], startDate: Dayjs) {
    return tradeDateUtils(dates, startDate.subtract(1, "day"));
  }

  getStartDate(p: Portfolio, props: CalcProps) {
    return props.startDate.type === "establishDate"
      ? p.publishDate
      : props.startDate.type === "startDate"
        ? p.startDate
        : dayjs(props.startDate.date)?.startOf("day");
  }

  async getPortfolioById(portfolioId: number) {
    const result = await this.db
      .select()
      .from(portfolio.model)
      .where(eq(portfolio.model.id, portfolioId));
    return result[0] || null;
  }

  getCompoundValues(data: NetValues[], startDate: Dayjs): SomeNetValue[] {
    if (!data || data.length == 0) {
      return [];
    }
    const result: SomeNetValue[] = [];
    let currentCompound = data[0].netValue;
    result.push({ date: moment(data[0].date), netValue: currentCompound });
    let flag = false;
    for (let i = 1; i < data.length; ++i) {
      const now = data[i];
      const before = data[i - 1];
      const date = moment(now.date);
      if (date.isBefore(startDate)) {
        currentCompound = now.cumNetValue;
        result.push({ date, netValue: currentCompound });
      } else {
        // 含分红的当日净值
        const valueWithDividend =
          now.cumNetValue - (before.cumNetValue - before.netValue);
        currentCompound =
          currentCompound * (valueWithDividend / before.netValue);
        if (isNaN(currentCompound) || !isFinite(currentCompound)) {
          if (!flag) {
            currentCompound = now.cumNetValue;
          } else {
            throw "净值序列包含0";
          }
        } else {
          flag = true;
        }
        result.push({ date, netValue: currentCompound });
      }
    }

    return result;
  }

  getBaseReportData(
    utils: ReturnType<typeof tradeDateUtils>,
    startDate: Moment,
    endDate: Moment,
    netValues: SomeNetValue[]
  ): { data: BaseReportData[]; initialValue: number } {
    if (!netValues || netValues.length <= 0) {
      throw new Error("无净值数据");
    }

    if (startDate.isBefore(netValues[0].date)) {
      throw new Error("建仓日无净值");
    }

    // 把netValues之间的空隙都填满
    const netValueDict: Record<
      string,
      { netValue: number; backfill: boolean }
    > = {};
    let curNetValueIdx = 0;
    for (
      let d = dayjs(netValues[0].date);
      !d.isAfter(endDate);
      d = d.add(1, "day")
    ) {
      if (
        curNetValueIdx < netValues.length &&
        !d.isBefore(netValues[curNetValueIdx].date)
      ) {
        curNetValueIdx += 1;
      }
      netValueDict[d.format("YYYYMMDD")] = {
        netValue: netValues[curNetValueIdx - 1].netValue,
        backfill:
          d.format("YYYYMMDD") !=
          netValues[curNetValueIdx - 1].date.format("YYYYMMDD"),
      };
    }

    // 第一日前一天净值设为1，防止一些边界情况。
    netValueDict[netValues[0].date.subtract(1, "day").format("YYYYMMDD")] = {
      netValue: 1.0,
      backfill: true,
    };

    const netValueOf = (day: Moment) => netValueDict[day.format("YYYYMMDD")];

    const result: BaseReportData[] = [];
    const beforeStartDate = startDate.subtract(1, "day");
    const initialValue = netValueOf(beforeStartDate);

    if (!initialValue) {
      throw new Error("无法获取起始日期的前一日净值数据，请检查净值数据完整性");
    }
    let beforeHigh = initialValue;
    let beforeHighDate = beforeStartDate;
    let beforeLow = initialValue;
    let beforeLowDate = beforeStartDate;

    for (let d = dayjs(startDate); !d.isAfter(endDate); d = d.add(1, "day")) {
      if (utils.isValid(d)) {
        const currentValue = netValueOf(d);
        if (currentValue) {
          if (currentValue.netValue >= beforeHigh.netValue) {
            beforeHigh = currentValue;
            beforeHighDate = d;
          }
          if (currentValue.netValue <= beforeLow.netValue) {
            beforeLow = currentValue;
            beforeLowDate = d;
          }
          result.push({
            date: d,
            netValue: currentValue.netValue,
            backfill: currentValue.backfill,
            beforeHigh: beforeHigh.netValue,
            beforeHighDate,
            beforeLow: beforeLow.netValue,
            beforeLowDate,
            return: currentValue.netValue / initialValue.netValue - 1,
            yearReturn:
              currentValue.netValue / netValueOf(utils.lastYear(d)).netValue -
              1,
            seasonReturn:
              currentValue.netValue / netValueOf(utils.lastSeason(d)).netValue -
              1,
            monthReturn:
              currentValue.netValue / netValueOf(utils.lastMonth(d)).netValue -
              1,
            weekReturn:
              currentValue.netValue / netValueOf(utils.lastWeek(d)).netValue -
              1,
            dayReturn:
              currentValue.netValue /
                netValueOf(utils.validBefore(d.subtract(1, "day"))).netValue -
              1,
          });
        }
      }
    }
    return { data: result, initialValue: initialValue.netValue };
  }

  stdBy<T>(data: T[], key: keyof T) {
    if (data.length <= 1) return 0;
    const avg = meanBy(data, key);
    return Math.sqrt(
      sum(data.map((x) => Math.pow((x[key] as any) - avg, 2))) /
        (data.length - 1)
    );
  }

  getExtendReportData(
    utils: ReturnType<typeof tradeDateUtils>,
    startDate: Moment,
    baseReportData: { data: BaseReportData[]; initialValue: number },
    sequenceType: "day" | "week" | "month",
    sharpeRf: number,
    sortinoMar: number
  ): ExtendReportData {
    const seqReturnType: Record<SequenceType, keyof BaseReportData> = {
      day: "dayReturn",
      week: "weekReturn",
      month: "monthReturn",
    };

    const annualizeParam = {
      day: 252,
      week: 52,
      month: 12,
    };
    // 根据序列类型进行筛选
    const seq: BaseReportData[] = [];
    if (sequenceType === "day") {
      for (let x of baseReportData.data) {
        if (utils.isValid(x.date)) {
          seq.push(x);
        }
      }
    } else if (sequenceType === "week") {
      for (let x of baseReportData.data) {
        if (utils.lastWeek(x.date.add(1, "week")).isSame(x.date)) {
          seq.push(x);
        }
      }
      const last = baseReportData.data[baseReportData.data.length - 1];
      if (last !== seq[seq.length - 1]) {
        seq.push(last);
      }
    } else if (sequenceType === "month") {
      for (let x of baseReportData.data) {
        if (utils.lastMonth(x.date.add(1, "month")).isSame(x.date)) {
          seq.push(x);
        }
      }
      const last = baseReportData.data[baseReportData.data.length - 1];
      if (last !== seq[seq.length - 1]) {
        seq.push(last);
      }
    }

    if (seq.length === 0) {
      return {
        maxRetracement: 0,
        annualizedReturn: 0,
        annualizedStd: 0,
        annualizedHalfStd: 0,
        sharpe: 0,
        sortino: 0,
        doNotShow: true,
      };
    }

    // 最大回撤
    let maxRetracement = 0;
    let maxValue = baseReportData.initialValue;
    for (let x of seq) {
      const retracementNow = (maxValue - x.netValue) / maxValue;
      if (retracementNow > maxRetracement) {
        maxRetracement = retracementNow;
      }
      if (x.netValue > maxValue) {
        maxValue = x.netValue;
      }
    }

    const returnTypeKey = seqReturnType[sequenceType];

    let annualizedReturn = 0;
    if (sequenceType === "day") {
      // 日度年化收益率
      annualizedReturn =
        Math.pow(
          seq[seq.length - 1].netValue / baseReportData.initialValue,
          annualizeParam.day / seq.length
        ) - 1;
    } else {
      // 周度月度年化收益率
      annualizedReturn =
        Math.pow(1 + meanBy(seq, returnTypeKey), annualizeParam[sequenceType]) -
        1;
    }

    // 年化标准差
    const annualizedStd =
      this.stdBy(seq, returnTypeKey) * Math.sqrt(annualizeParam[sequenceType]);
    // 年化下半标准差
    const annualizedHalfStd =
      this.stdBy(
        seq.filter(
          (x) =>
            (x[returnTypeKey] as number) <
            sortinoMar / annualizeParam[sequenceType]
        ),
        returnTypeKey
      ) * Math.sqrt(annualizeParam[sequenceType]);

    const sharpe = annualizedStd
      ? (annualizedReturn - sharpeRf) / annualizedStd
      : null;

    const sortino = annualizedHalfStd
      ? (annualizedReturn - sortinoMar) / annualizedHalfStd
      : null;

    return {
      maxRetracement,
      annualizedReturn,
      annualizedStd,
      annualizedHalfStd,
      sharpe,
      sortino,
      doNotShow:
        startDate
          .add(183, "day")
          .isAfter(baseReportData.data[baseReportData.data.length - 1].date) ||
        annualizedReturn < 0,
    };
  }

  async calculate(calcProps: CalcProps) {
    const p = await this.getPortfolioById(calcProps.selectedProductId);
    const startDate = this.getStartDate(p, calcProps);
    const rawNetValues = await this.calculateNetValue(
      calcProps.selectedProductId,
      false
    );

    const convertedValues = rawNetValues.map((x) => ({
      date: dayjs(x.date)?.format("YYYY-MM-DD") || "",
      netValue: x.netValue ? parseFloat(x.netValue) : 0,
      cumNetValue: x.cumNetValue ? parseFloat(x.cumNetValue) : 0,
      assetNet: x.assetNet ? parseFloat(x.assetNet) : 0,
    }));
    let compoundValues = this.getCompoundValues(
      convertedValues as NetValues[],
      startDate as Dayjs
    );
    let values: SomeNetValue[] | null = null;
    const unitNetValue = rawNetValues.map((x) => ({
      date: dayjs(x.date),
      netValue: x.netValue ? parseFloat(x.netValue) : 0,
    }));

    const cumNetValue = rawNetValues.map((x) => ({
      date: dayjs(x.date),
      netValue: x.netValue ? parseFloat(x.netValue) : 0,
    }));

    if (calcProps.valueType == "unit") {
      values = unitNetValue;
    } else if (calcProps.valueType == "accumulate") {
      values = cumNetValue;
    } else if (calcProps.valueType == "compound") {
      values = compoundValues;
    }

    const tradeDate = await this.getTradeDate();
    const dateUtils = this.getUtils(
      tradeDate.list,
      dayjs(startDate)?.add(1, "day")
    );

    const baseReport = this.getBaseReportData(
      dateUtils,
      dayjs(startDate).add(1, "day"),
      dayjs(calcProps.endDate).add(1, "day"),
      values as SomeNetValue[]
    );

    const extendReport = this.getExtendReportData(
      dateUtils,
      dayjs(startDate).add(1, "day"),
      baseReport,
      calcProps.intervalType as SequenceType,
      calcProps.valueType === "excess" || calcProps.valueType === "extraExcess"
        ? 0
        : p.sharpeRf!,
      calcProps.valueType === "excess" || calcProps.valueType === "extraExcess"
        ? 0
        : p.sortinoMar!
    );

    let highValue: SomeNetValue = {
      date: startDate as Dayjs,
      netValue: baseReport.initialValue,
    };
    let highValueIndex = -1;
    let maxRetracement = 0;
    let retracementInterval: [SomeNetValue, SomeNetValue] | null = null;
    let retracementIndex: [number, number] | null = null;
    let fixAt: SomeNetValue | null = null;
    let fixIndex: number | null = null;
    for (let [i, x] of baseReport.data.entries()) {
      const retracementNow =
        (highValue.netValue - x.netValue) / highValue.netValue;
      if (retracementNow > maxRetracement) {
        maxRetracement = retracementNow;
        retracementInterval = [highValue, x];
        retracementIndex = [highValueIndex, i];
        fixAt = null;
        fixIndex = null;
      }
      if (x.netValue > highValue.netValue) {
        highValue = x;
        highValueIndex = i;
      }
      if (
        retracementInterval &&
        x.netValue >= retracementInterval[0].netValue &&
        !fixAt
      ) {
        fixAt = x;
        fixIndex = i;
      }
    }

    const lastBaseReport = baseReport.data[baseReport.data.length - 1];
    return {
      return: lastBaseReport.return,
      annualizedReturn: extendReport.annualizedReturn,
      annualizedStd: extendReport.annualizedStd,
      sharpe: extendReport.sharpe,
      maxRetracement: extendReport.maxRetracement,
      kama: extendReport.annualizedReturn / extendReport.maxRetracement,
      sortino: extendReport.sortino,
      maxRetracementInterval: retracementInterval
        ? [retracementInterval[0].date, retracementInterval[1].date]
        : null,
      maxRetracementDays:
        retracementInterval && retracementIndex
          ? retracementIndex[1] - retracementIndex[0]
          : null,
      maxRetracementFixtureInterval:
        fixAt && retracementInterval
          ? [retracementInterval[1].date, fixAt.date]
          : null,
      maxRetracementFixtureDays:
        fixAt && retracementIndex ? fixIndex! - retracementIndex[1] : null,
    };
  }
}

export default new PortfolioCalculator();
