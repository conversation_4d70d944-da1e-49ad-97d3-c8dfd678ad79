import { datePattern } from "@portfolio-service/schema/utils";
import dayjs, { Dayjs } from "dayjs";
import { SQL, and, count, eq, isNotNull, isNull, like, or } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import { z } from "zod";
import {
  deleteInputSchema,
  inputSchema,
  portfolioIdSchema,
  statusSchema,
} from "../model/netValueDisclosureConf";
import {
  disclosePeriodSchema,
  disclosePeriodTransform,
  insertSchema,
  model,
  select,
  startDateTransform,
} from "../schema/netValueDisclosureConf";
import {
  model as portfolioModel,
  select as portfolioSelect,
  stateSchema,
} from "../schema/portfolio";
import { tradeDateStorage } from "../store/tradeDate";
import { min } from "../utils/dayjs";
import { dateGte, dateLt } from "../utils/drizzle";
import { withMutex } from "../utils/lock";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import dividendService from "./dividend";
import disclosureService from "./netValueDisclosure";
import netValueLatestService from "./netValueLatest";
import portfolioService from "./portfolio";
import tradeService from "./trade";
import openDayService from "./openDay";

class NetValueDisclosureConfService extends Service {
  private select = {
    ...select,
    portfolioId: portfolioSelect.id,
    portfolioName: portfolioSelect.name,
    portfolioNo: portfolioSelect.no,
    portfolioState: portfolioSelect.state,
    portfolioPublishDate: portfolioSelect.publishDate,
  };

  async getByPortfolioId(portfolioId: number) {
    const result = await this.db
      .select(select)
      .from(model)
      .where(eq(model.portfolioId, portfolioId));

    return first(result);
  }

  async getDatesByPortfolioId(
    portfolioId: number,
    amount?: number,
    offset?: Dayjs
  ) {
    const record = await this.getByPortfolioId(portfolioId);
    if (!record) {
      return [];
    }

    const { disclosePeriod } = record;
    if (!disclosePeriod.length) {
      return [];
    }

    const portfolio = await portfolioService.getByIdInternal(portfolioId);
    const startDate =
      record.startDate === "establish"
        ? portfolio.publishDate
        : portfolio.investDate;

    if (!startDate) {
      return [];
    }

    const netValue = await netValueLatestService.getByPortfolioId(portfolioId);
    const today = min(offset, netValue?.date) || dayjs();

    const dates: Dayjs[] = [];

    let dividendDates: Set<string> | undefined = undefined;
    let liquidateDates: Set<string> | undefined = undefined;
    let openDates: Set<string> | undefined = undefined;

    if (disclosePeriod.includes("dividend")) {
      const dates = await dividendService.getDatesByPortfolioId(portfolioId);
      dividendDates = new Set(
        dates.map((date) => dayjs(date).format(datePattern))
      );
    }

    if (disclosePeriod.includes("liquidate")) {
      const dates =
        await tradeService.getLiquidateDatesByPortfolioId(portfolioId);
      liquidateDates = new Set(
        dates.map((date) => dayjs(date).format(datePattern))
      );
    }

    if (disclosePeriod.includes("openday")) {
      const openDayList =
        await openDayService.getListByPortfolioId(portfolioId);
      openDates = new Set(
        openDayList.map((it) => dayjs(it).format(datePattern))
      );
    }

    const investDate = portfolio?.investDate
      ? dayjs(portfolio.investDate)
      : undefined;

    const establishDate = portfolio?.publishDate
      ? dayjs(portfolio.publishDate)
      : undefined;

    let date = today;

    const conditionMap: Record<
      z.infer<typeof disclosePeriodSchema>[number],
      (date: Dayjs) => boolean
    > = {
      workday: () => true,
      openday: (date) => openDates?.has(date.format(datePattern)) || false,
      friday: (date) =>
        this.getLastTradeDateOfWeek(date)?.isSame(date) || false,
      dividend: (date) => dividendDates?.has(date.format(datePattern)) || false,
      liquidate: (date) =>
        liquidateDates?.has(date.format(datePattern)) || false,
      invest: (date) => investDate?.isSame(date, "day") || false,
      establish: (date) => establishDate?.isSame(date, "day") || false,
    };

    while (true) {
      if (date.year() < 2019) break;
      if (date.isBefore(startDate, "day")) break;
      if (amount != null && dates.length >= amount) break;

      if (!tradeDateStorage.value.has(date.format(datePattern))) {
        date = date.subtract(1, "day");
        continue;
      }

      for (const period of disclosePeriod) {
        const getCondition = conditionMap[period];
        if (getCondition(date)) {
          dates.push(date);
          break;
        }
      }

      date = date.subtract(1, "day");
    }

    return dates;
  }

  async getLatestDateByPortfolioId(portfolioId: number, offset?: Dayjs) {
    const result = await this.getDatesByPortfolioId(portfolioId, 1, offset);
    return first(result);
  }

  async getLatestNetValueDates() {
    const portfolios = await portfolioService.getAll();

    const result: {
      portfolioId: number;
      portfolioName: string | null;
      portfolioNo: string | null;
      netValueDate: string;
    }[] = [];

    for (const portfolio of portfolios) {
      const [date] = await this.getDatesByPortfolioId(portfolio.id, 1);
      if (!date) continue;

      result.push({
        portfolioId: portfolio.id,
        portfolioName: portfolio.name,
        portfolioNo: portfolio.no,
        netValueDate: date.format(datePattern),
      });
    }

    return result;
  }

  async getLatestNetValueDatesCached() {
    const result = await disclosureService.getList();

    return result.map((it) => ({
      ...it,
      netValueDate: dayjs(it.netValueDate).format(datePattern),
    }));
  }

  async getList(input: z.infer<typeof inputSchema>) {
    let query = this.db.select(this.select).from(portfolioModel).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);

    let amountQuery = this.db
      .select({ value: count() })
      .from(portfolioModel)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async getDisplayList(input: z.infer<typeof inputSchema>) {
    let query = this.db.select(this.select).from(portfolioModel).$dynamic();
    query = this.withCondition(query, input);

    const queryResult = await query;

    const items = queryResult.map((it) => {
      return {
        portfolioId: it.portfolioId,
        status: it.status ? "已设置" : "未设置",
        disclosePeriod: it.disclosePeriod
          ?.map((it) => disclosePeriodTransform[it])
          ?.join("，"),
        startDate: startDateTransform[it.startDate],
        portfolioName: it.portfolioName,
        portfolioNo: it.portfolioNo,
        portfolioState: it.portfolioState,
        portfolioPublishDate: it.portfolioPublishDate
          ? dayjs(it.portfolioPublishDate).format(datePattern)
          : undefined,
      };
    });

    return { items, amount: queryResult.length };
  }

  async update(input: z.infer<typeof insertSchema>) {
    const { portfolioId, startDate, disclosePeriod } = input;

    const data = {
      startDate,
      disclosePeriod: JSON.stringify(disclosePeriod),
    };

    await withMutex(
      ["netValueDisclosureConf.update", portfolioId],
      async () => {
        await transaction.run(async (tx) => {
          await tx
            .insert(model)
            .values({ portfolioId, ...data })
            .onDuplicateKeyUpdate({ set: data });
          await disclosureService.update(portfolioId);
        });
      }
    );
  }

  async delete(input: z.infer<typeof deleteInputSchema>) {
    await transaction.run(async (tx) => {
      await tx.delete(model).where(eq(model.portfolioId, input.portfolioId));
      await disclosureService.delete(input.portfolioId);
    });
  }

  getLastTradeDateOfWeek(date: Dayjs) {
    const monday = date.clone().day(1);
    const friday = date.clone().day(5);

    let current = friday;

    while (!current.isBefore(monday)) {
      if (tradeDateStorage.value.has(current.format("YYYY-MM-DD"))) {
        return current;
      }
      current = current.subtract(1, "day");
    }
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof inputSchema>
  ) {
    query.leftJoin(model, eq(portfolioModel.id, model.portfolioId));

    const conditions: SQL[] = [
      eq(portfolioModel.investmentAdviserType, "私募产品（非投顾）"),
    ];

    const {
      portfolioId,
      portfolioState,
      status,
      disclosePeriod,
      portfolioPublishDate,
    } = filter || {};

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(portfolioModel.id, portfolioIdParsed.data));
    }

    const portfolioStateParsed = stateSchema.safeParse(portfolioState);
    if (portfolioStateParsed.success) {
      conditions.push(eq(portfolioModel.state, portfolioStateParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      const field = model.portfolioId;
      conditions.push(
        statusParsed.data === "已设置" ? isNotNull(field) : isNull(field)
      );
    }

    const disclosePeriodParsed = disclosePeriodSchema.safeParse(disclosePeriod);
    if (disclosePeriodParsed.success) {
      const predicate = or(
        ...disclosePeriodParsed.data.map((period) =>
          like(model.disclosePeriod, `%${period}%`)
        )
      );
      if (predicate) {
        conditions.push(predicate);
      }
    }

    const publishDateSchema = z.tuple([z.date(), z.date()]);
    const publishDateParsed = publishDateSchema.safeParse(portfolioPublishDate);
    if (publishDateParsed.success) {
      const [start, end] = publishDateParsed.data;
      const predicate = and(
        dateGte(portfolioModel.publishDate, start),
        dateLt(portfolioModel.publishDate, end)
      );
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new NetValueDisclosureConfService();
