import { toCN } from "@portfolio-service/currency";
import { DocumentType } from "@portfolio-service/schema/document";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import AdmZip from "adm-zip";
import dayjs from "dayjs";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import path from "path";
import { z } from "zod";
import env from "../config/env";
import textLabel from "../config/textLabel";
import typeCode from "../config/typeCode";
import {
  contractReviewInputSchema,
  contractSignInputSchema,
  createInputSchema,
  documentsSignInputSchema,
  listInputSchema,
  NotificationType,
  ownListInputSchema,
  payInputSchema,
  portfolioIdSchema,
  terminateInputSchema,
  ttdOrderReviewInputSchema,
  userIdSchema,
  videoRecordInputSchema,
} from "../model/subscribeProcess";
import { DataStr } from "../model/ttd";
import * as portfolio from "../schema/portfolio";
import {
  model,
  select,
  subscribeProcessDocument,
  updateSchema,
} from "../schema/subscribeProcess";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import { isDevEnv } from "../utils/dev";
import { timestampEqDate } from "../utils/drizzle";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import {
  subscribeMachine as machine,
  subscribeActiveStates,
} from "../workflow/subscribe";
import documentService from "./document";
import fileUploadService from "./fileUpload";
import notificationService from "./notification";
import portfolioService from "./portfolio";
import tradeApplyProcessService from "./tradeApplyProcess";
import ttdService from "./ttd";
import userService from "./user";
import videoConfigService from "./videoConfig";
import workflowService from "./workflow";

class SubscribeProcessService extends Service {
  private select = {
    ...select,
    portfolioName: portfolio.select.name,
    portfolioNo: portfolio.select.no,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
    username: user.model.name,
    userType: user.model.type,
    identityNumber: user.model.identityNumber,
    identityType: user.model.identityType,
    classification: user.model.classification,
    userNo: user.model.ttdUserNumber,
    phone: user.model.phone,
    email: user.model.email,
  };

  async getByIdSafe(id: number, userId?: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.id, id))
      .$dynamic();

    query = this.withJoin(query);
    const record = first(await query);

    if (!record || (userId != null && record.userId !== userId)) {
      return;
    }

    return record;
  }

  async getById(id: number, userId?: number) {
    const record = await this.getByIdSafe(id, userId);
    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByWorkflowId(workflowId: string) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.workflowId, workflowId))
      .$dynamic();

    query = this.withJoin(query);
    const [record] = await query;

    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByUserIdAndPortfolioId(userId: number, portfolioId: number) {
    const record = await this.getByUserIdAndPortfolioIdSafe(
      userId,
      portfolioId
    );
    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByUserIdAndPortfolioIdSafe(userId: number, portfolioId: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(
        and(
          eq(model.userId, userId),
          eq(model.portfolioId, portfolioId),
          eq(model.terminated, false)
        )
      )
      .$dynamic();

    query = this.withJoin(query);
    return first(await query);
  }

  async getListByIdentityNumber(identityNumber: string) {
    let query = this.db.select(select).from(model).$dynamic();
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = query.where(eq(user.model.identityNumber, identityNumber));

    const result = await query;

    const documentPromises = result.map(async (it) => {
      const documents = await this.getDocumentsById(it.id);
      return { id: it.id, documents };
    });

    const documentsList = await Promise.all(documentPromises);

    return result.map((it) => ({
      ...it,
      documents: documentsList.find(({ id }) => id === it.id)?.documents,
    }));
  }

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input);
    query = this.withOrder(query);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    const tradeApplyProcessIds = queryResult
      .map((it) => it.tradeApplyProcessId)
      .filter(Boolean) as number[];

    const tradeApplyProcesses =
      await tradeApplyProcessService.getByIds(tradeApplyProcessIds);

    const result = queryResult.map((it) => ({
      ...it,
      tradeApplyProcess: tradeApplyProcesses.find(
        (trade) => trade.parentProcessId === it.id
      ),
    }));

    return paginationResult(result, amountQueryResult[0].value);
  }

  async getOwnList(userId: number, input: z.infer<typeof ownListInputSchema>) {
    return await this.getList({ ...input, userId });
  }

  async getActiveAmount(userId: number) {
    const [result] = await this.db
      .select({ amount: count() })
      .from(model)
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .where(
        and(eq(model.userId, userId), eq(workflowTask.model.status, "active"))
      );

    const tradeApplyResult = await tradeApplyProcessService.getActiveAmount(
      userId,
      "subscribe"
    );

    return { amount: result.amount + tradeApplyResult.amount };
  }

  async getByPortfolioId(portfolioId: number) {
    let query = this.db.select(select).from(model).$dynamic();

    query = query.leftJoin(
      workflowTask.model,
      eq(model.workflowId, workflowTask.model.workflowId)
    );
    query = query.where(
      and(
        eq(model.portfolioId, portfolioId),
        eq(workflowTask.model.status, "done")
      )
    );

    return await query;
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(subscribeProcessDocument)
      .where(eq(subscribeProcessDocument.subscribeProcessId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async getDocumentsWithTtdFilesById(id: number) {
    const documents = await this.getDocumentsById(id);

    const ttdFileIds = documents
      .map((it) => it.ttdFileId)
      .filter(Boolean) as string[];

    const ttdFiles = await Promise.all(
      ttdFileIds.map((id) => ttdService.querySignedDocument(id))
    );

    return documents.map((document) => ({
      ...document,
      ttdFile: ttdFiles.find((file) => file.fileId === document.ttdFileId),
    }));
  }

  async getTtdFileIdsById(id: number) {
    const documents = await this.getDocumentsById(id);

    const fileIds: string[] = [];
    for (const { ttdFileId, obsolete } of documents) {
      if (!!ttdFileId && !obsolete) {
        fileIds.push(ttdFileId);
      }
    }

    return fileIds;
  }

  async getContractLinkById(id: number) {
    const { orderNumber } = await this.getById(id);

    const relations = await this.db
      .select()
      .from(subscribeProcessDocument)
      .where(eq(subscribeProcessDocument.subscribeProcessId, id));

    const documentIds = relations.map((it) => it.documentId);
    const documents = await documentService.getByIds(documentIds);
    const contract = documents.find(
      (document) => document.type === "电子合同" && !document.obsolete
    );

    const fileUploadId = contract?.fileUploadId;

    let url: string | undefined = undefined;

    if (!!orderNumber) {
      const ttdOrder = await ttdService.queryOrder(orderNumber);
      url = ttdOrder.contract?.fileUrl;
    }

    return { url, fileUploadId };
  }

  async getContractAndRiskRevealLinkById(id: number) {
    const { orderNumber } = await this.getById(id);
    if (!orderNumber) {
      return {};
    }

    const ttdOrder = await ttdService.queryOrder(orderNumber);

    return {
      contract: ttdOrder.contract?.fileUrl,
      riskReveal: ttdOrder.risk?.fileUrl,
      supplements: ttdOrder.supplements?.map(
        ({ version, fileUrl, fileState }) => ({
          version,
          fileUrl,
          fileState,
        })
      ),
    };
  }

  async create(input: z.infer<typeof createInputSchema>) {
    const { portfolioId, userId, type } = input;

    const id = await withMutex(
      ["subscribeProcessService.create", userId, portfolioId],
      async () => {
        return await transaction.run(async () => {
          if (type === "首次申购") {
            return await this.firstTime(input);
          } else if (type === "追加申购") {
            return await this.addition(input);
          }
        });
      }
    );

    if (id) {
      return await this.getById(id);
    }
  }

  private isValidIDCard(idCard: string): boolean {
    return /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/i.test(
      idCard
    );
  }

  private isValidateIDCard(idVal: string): boolean {
    let idRe18 =
      /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    let idRe15 =
      /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/;
    if (idRe18.test(idVal) || idRe15.test(idVal)) {
      return true;
    } else {
      return false;
    }
  }

  public isOver70Simple(idCard: string): boolean {
    if (!this.isValidateIDCard(idCard)) return false;

    const birthYear = parseInt(idCard.substring(6, 10));
    const currentYear = new Date().getFullYear();

    return currentYear - birthYear >= 70;
  }

  private async firstTime(input: z.infer<typeof createInputSchema>) {
    const {
      operatorId,
      userId,
      portfolioId,
      videoConfigWordsId,
      amount,
      productNumber,
      time,
      type,
      riskMatch,
    } = input;

    // if (!!(await this.getByUserIdAndPortfolioIdSafe(userId, portfolioId))) {
    //   throw new TRPCError({
    //     code: "FORBIDDEN",
    //     message: "该客户-产品组合的首次申购已存在",
    //   });
    // }

    const user = await userService.getById(userId);

    let targetCode: string | null = null;

    if (user.classification === "普通投资者") {
      if (!videoConfigWordsId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "普通投资者首次申购需要选择双录话术",
        });
      }

      const videoConfig = await videoConfigService.create({
        userId,
        portfolioId,
        wordsId: videoConfigWordsId,
      });
      targetCode = videoConfig.targetCode;
    }

    if (
      user.classification === "专业投资者" &&
      this.isOver70Simple(user.identityNumber)
    ) {
      const videoConfigWords =
        await videoConfigService.getWordsByName(
          "专业投资者大于70岁风险匹配双录"
        );
      if (!videoConfigWords.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "专业投资者大于70岁首次申购需要选择双录话术",
        });
      }

      const videoConfig = await videoConfigService.create({
        userId,
        portfolioId,
        wordsId: videoConfigWords.id,
      });
      targetCode = videoConfig.targetCode;
    }

    const userNo = await userService.getTtdUserNoById(userId);

    const { orderNo } = await ttdService.createOrder({
      amount,
      productNumber,
      time,
      userNo,
    });

    const actor = await workflowService
      .createActor(machine, { input: { type: "首次申购" } })
      .start(operatorId, userId);

    const [result] = await this.db.insert(model).values({
      workflowId: actor.id,
      userId,
      portfolioId,
      amount,
      videoConfigTargetCode: targetCode,
      type,
      orderNumber: orderNo,
      productNumber,
      time: dayjs(time).format("YYYY-MM-DD HH:mm:ss"),
      riskMatch,
      contractCreateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    });
    const id = result.insertId;

    await this.createTradeApplyDoc(userId, id, amount, time);

    if (user.classification === "普通投资者") {
      await this.createRiskMatchDoc(userId, id, !!riskMatch);
    } else if (user.classification === "专业投资者") {
      await this.createProfessionalInvestorLetter(userId, id);
    }

    return id;
  }

  private async addition(input: z.infer<typeof createInputSchema>) {
    const {
      operatorId,
      userId,
      portfolioId,
      amount,
      productNumber,
      time,
      type,
      riskMatch,
    } = input;

    const record = await this.getByUserIdAndPortfolioIdSafe(
      userId,
      portfolioId
    );

    const actor = await workflowService
      .createActor(machine, { input: { type: "追加申购" } })
      .start(operatorId, userId);

    const [result] = await this.db.insert(model).values({
      workflowId: actor.id,
      userId,
      portfolioId,
      amount,
      type,
      orderNumber: record?.orderNumber,
      productNumber,
      time: dayjs(time).format("YYYY-MM-DD HH:mm:ss"),
      riskMatch,
      contractCreateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    });
    const id = result.insertId;

    await this.createTradeApplyDoc(userId, id, amount, time);

    return id;
  }

  async videoRecord(
    userId: number,
    input: z.infer<typeof videoRecordInputSchema>
  ) {
    await withMutex(
      ["subscribeProcessService.videoRecord", input],
      async () => {
        const record = await this.getById(input.id, userId);

        await transaction.run(async () => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["ttdOrder", "ttdOrderRedo"],
          });

          await this.db
            .update(model)
            .set({ videoRecordStatus: true })
            .where(eq(model.id, input.id));

          if (await this.getTtdOrderStatus(userId, input.id)) {
            await actor.send({ type: "event.next" }, userId, null);
          }
        });
      }
    );
  }

  async documentsSign(
    userId: number,
    input: z.infer<typeof documentsSignInputSchema>
  ) {
    await withMutex(
      ["subscribeProcessService.documentsSign", input],
      async () => {
        const record = await this.getById(input.id, userId);
        const types: DocumentType[] = ["交易申请书"];
        const { classification } = await userService.getById(userId);

        if (classification === "普通投资者") {
          types.push("风险匹配告知书", "风险不匹配告知书");
        }

        await this.validateDocumentsSign(input.id, types);

        await transaction.run(async (tx) => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["ttdOrder", "ttdOrderRedo"],
          });

          await tx
            .update(model)
            .set({ documentsStatus: true })
            .where(eq(model.id, input.id));

          if (await this.getTtdOrderStatus(userId, input.id)) {
            await actor.send({ type: "event.next" }, userId, null);
          }
        });
      }
    );
  }

  async ttdOrderReview(input: z.infer<typeof ttdOrderReviewInputSchema>) {
    await withMutex(
      ["subscribeProcessService.ttdOrderReview", input],
      async () => {
        const { id, operatorId, pass, redo } = input;

        const record = await this.getById(id);
        const { userId, amount, time, riskMatch } = record;

        await transaction.run(async () => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["ttdOrderReview"],
          });

          const data: z.infer<typeof updateSchema> = {
            contractCreateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          };

          if (redo?.videoRecord) data.videoRecordStatus = false;
          if (redo?.documents) data.documentsStatus = false;

          if (!pass) {
            if (!Object.keys(data).length) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "请至少选择一项需要重做的任务",
              });
            }

            if (!!redo?.documents) {
              const documents = await this.getDocumentsById(id);
              await documentService.makeObsolete(
                documents.map((it) => it.id),
                ["交易申请书", "风险匹配告知书", "风险不匹配告知书"]
              );
              await this.createTradeApplyDoc(
                userId,
                id,
                amount,
                new Date(time)
              );

              const riskMatchDocTypes: DocumentType[] = [
                "风险匹配告知书",
                "风险不匹配告知书",
              ];
              if (
                documents.some(
                  ({ type }) => !!type && riskMatchDocTypes.includes(type)
                ) &&
                riskMatch != null
              ) {
                await this.createRiskMatchDoc(userId, id, riskMatch);
              }
            }

            await this.db.update(model).set(data).where(eq(model.id, id));
          }

          await actor.send(
            { type: pass ? "event.next" : "event.redo" },
            operatorId,
            record.userId
          );
        });
      }
    );
  }

  async contractSign(
    userId: number,
    input: z.infer<typeof contractSignInputSchema>
  ) {
    await withMutex(
      ["subscribeProcessService.contractSign", input],
      async () => {
        const { orderNumber, workflowId } = await this.getById(
          input.id,
          userId
        );
        if (!orderNumber) {
          throw new TRPCError({ code: "FORBIDDEN", message: "无订单号" });
        }
        await this.validateContractSign(orderNumber);

        await transaction.run(async (tx) => {
          const actor = await workflowService.getActor(machine, {
            id: workflowId,
            state: ["contractSign", "contractSignRedo"],
          });

          await tx
            .update(model)
            .set({
              contractStatus: true,
              contractSignTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            })
            .where(eq(model.id, input.id));

          await actor.send({ type: "event.next" }, userId, null);
        });
      }
    );
  }

  async contractReview(input: z.infer<typeof contractReviewInputSchema>) {
    await withMutex(
      ["subscribeProcessService.contractReview", input],
      async () => {
        const { id, operatorId, pass } = input;

        const record = await this.getById(id);
        const { amount, time, orderNumber, productNumber } = record;
        let userNo = record.userNo;

        if (isDevEnv() && !userNo) {
          userNo = "1245367304074739712";
        }

        await transaction.run(async () => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["contractReview"],
          });

          if (!pass) {
            if (!orderNumber) {
              throw new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: "无订单号",
              });
            }

            if (!userNo) {
              throw new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: "无妥妥递用户编号",
              });
            }

            if (!productNumber) {
              throw new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: "未关联妥妥递产品",
              });
            }

            if (!!orderNumber) {
              await ttdService.invalidateOrder(orderNumber);
              const { orderNo } = await ttdService.createOrder({
                amount,
                productNumber,
                time: new Date(time),
                userNo,
              });
              await this.db
                .update(model)
                .set({ orderNumber: orderNo, contractStatus: false })
                .where(eq(model.id, id));
            }
          }

          await actor.send(
            { type: pass ? "event.next" : "event.redo" },
            operatorId,
            record.userId
          );
        });
      }
    );
  }

  async pay(userId: number, input: z.infer<typeof payInputSchema>) {
    await withMutex(["subscribeProcessService.pay", input], async () => {
      const record = await this.getById(input.id);

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: ["pay"],
        });
        await actor.send({ type: "event.next" }, userId, null);
      });
    });
  }

  async updateTradeApplyProcessId(id: number, processId: number) {
    await this.db
      .update(model)
      .set({ tradeApplyProcessId: processId })
      .where(eq(model.id, id));
  }

  async terminate(input: z.infer<typeof terminateInputSchema>) {
    await withMutex(["subscribeProcessService.terminate", input], async () => {
      const { id, operatorId } = input;

      const record = await this.getById(id);
      if (record.terminated) {
        throw new TRPCError({ code: "FORBIDDEN", message: "流程已中止" });
      }

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: subscribeActiveStates,
        });

        if (!!record.orderNumber) {
          try {
            await ttdService.invalidateOrder(record.orderNumber);
          } catch (e) {
            console.error(e);
          }
        }
        await this.db
          .update(model)
          .set({ terminated: true })
          .where(eq(model.id, id));
        await actor.stop(operatorId);
      });

      this.sendNotification(record.workflowId, "terminate");
    });
  }

  async sendNotification(
    workflowId: string | undefined,
    type: NotificationType
  ) {
    if (!workflowId) return;

    const record = await this.getByWorkflowId(workflowId);
    await notificationService.create(this.getNotificationEntity(record, type));
  }

  async getDocumentsZipById(id: number) {
    const record = await this.getById(id);
    const zip = new AdmZip();

    const documents = await this.getDocumentsById(id);
    for (const document of documents) {
      if (document.obsolete) continue;

      if (document.fileUploadId) {
        const file = await fileUploadService.getByIdSafe(document.fileUploadId);
        zip.addLocalFile(
          path.resolve(env.FILE_UPLOAD_DIR, document.fileUploadId),
          undefined,
          `${document.type}_${file?.name}`
        );
        continue;
      }

      if (document.ttdFileId) {
        const { url } = await ttdService.querySignedDocument(
          document.ttdFileId
        );
        const arrayBuffer = await fetch(url).then((response) =>
          response.arrayBuffer()
        );
        zip.addFile(`${document.type}.pdf`, Buffer.from(arrayBuffer));
      }
    }

    const { contract, riskReveal, supplements } =
      await this.getContractAndRiskRevealLinkById(id);

    const contractDocuments: { url: string; filename: string }[] = [];
    if (contract)
      contractDocuments.push({ url: contract, filename: "电子合同.pdf" });

    if (riskReveal)
      contractDocuments.push({ url: riskReveal, filename: "风险揭示.pdf" });

    supplements?.forEach((it) =>
      contractDocuments.push({
        url: it.fileUrl,
        filename: `补充协议_${it.version}.pdf`,
      })
    );

    for (const { url, filename } of contractDocuments) {
      const arrayBuffer = await fetch(url).then((response) =>
        response.arrayBuffer()
      );
      zip.addFile(filename, Buffer.from(arrayBuffer));
    }

    return {
      filename: `${record.type}_${record.portfolioName}_${record.identityNumber}_${record.username}.zip`,
      data: zip.toBuffer(),
    };
  }

  private async validateDocumentsSign(id: number, types: DocumentType[]) {
    const relations = await this.db
      .select()
      .from(subscribeProcessDocument)
      .where(eq(subscribeProcessDocument.subscribeProcessId, id));

    if (!relations.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const promises = relations.map(async ({ documentId }) => {
      const { ttdFileId, type } = await documentService.getById(documentId);
      if (!ttdFileId) {
        return;
      }

      if (!type || !types.includes(type)) {
        return;
      }

      const ttdDocument = await ttdService.querySignedDocument(ttdFileId);
      if (!ttdDocument) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      if (ttdDocument.investorState !== 1) {
        throw new TRPCError({ code: "FORBIDDEN", message: "文件未签署" });
      }
    });

    await Promise.all(promises);
  }

  private async validateContractSign(orderNumber: string) {
    const ttdOrder = await ttdService.queryOrder(orderNumber);

    if (ttdOrder.contract?.fileState !== 2) {
      throw new TRPCError({ code: "FORBIDDEN", message: "合同未签署" });
    }

    if (ttdOrder.risk?.investorState !== 1) {
      throw new TRPCError({ code: "FORBIDDEN", message: "风险揭示书未签署" });
    }

    const orderWithSupplements = await ttdService.querySupplements(orderNumber);
    if (!!orderWithSupplements?.supplements?.length) {
      throw new TRPCError({ code: "FORBIDDEN", message: "补充协议未签署" });
    }
  }

  private async getTtdOrderStatus(userId: number, id: number) {
    const record = await this.getById(id, userId);
    if (record.type === "追加申购") {
      return record.documentsStatus;
    }

    const videoRecordStatus =
      !record.videoConfigTargetCode || record.videoRecordStatus;

    return record.documentsStatus && videoRecordStatus;
  }

  private async createTradeApplyDoc(
    userId: number,
    id: number,
    amount: number,
    time: Date
  ) {
    const { portfolioId } = await this.getById(id);
    const user = await userService.getById(userId);
    const portfolio = await portfolioService.getByIdInternal(portfolioId);

    const dataStr: DataStr = {
      [textLabel.基金名称]: { value: portfolio.name! },
      [textLabel.基金代码]: { value: portfolio.no! },
      [textLabel.投资者全称]: { value: user.name },
      [textLabel.投资者类型]: { value: user.classification! },
      [textLabel.证件类型]: { value: user.identityType! },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.认申购金额]: { value: toCN(amount) },
      [textLabel.日期]: { value: dayjs().format("YYYY年MM月DD日") },
      [textLabel.开放日]: { value: dayjs(time).format("YYYY年MM月DD日") },
    };

    if (!!user.phone) {
      dataStr[textLabel.移动电话] = { value: user.phone };
    }

    const { fileId } = await ttdService.createDocumentForSign(
      typeCode.认申购单,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId,
      type: "交易申请书",
      ttdFileId: fileId,
    });
    await this.db.insert(subscribeProcessDocument).values({
      documentId,
      subscribeProcessId: id,
    });
  }

  private async createRiskMatchDoc(
    userId: number,
    id: number,
    riskMatch: boolean
  ) {
    const user = await userService.getById(userId);

    const dataStr: DataStr = {
      [textLabel.投资者全称]: { value: user.name },
      [textLabel.证件类型]: { value: user.identityType },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.投资者类型]: { value: user.classification },
      [textLabel.投资者风险等级]: { value: user.riskLevel },
      [textLabel.管理人名称]: { value: "上海衍复投资管理有限公司" },
    };

    const { fileId } = await ttdService.createDocumentForSign(
      riskMatch ? typeCode.风险匹配告知书 : typeCode.风险不匹配告知书,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId,
      type: riskMatch ? "风险匹配告知书" : "风险不匹配告知书",
      ttdFileId: fileId,
    });
    await this.db.insert(subscribeProcessDocument).values({
      documentId,
      subscribeProcessId: id,
    });
  }

  private async createProfessionalInvestorLetter(userId: number, id: number) {
    const user = await userService.getById(userId);

    const dataStr: DataStr = {
      [textLabel.投资者全称]: { value: user.name },
      [textLabel.证件类型]: { value: user.identityType },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.日期]: { value: dayjs().format("YYYY-MM-DD") },
    };

    const { fileId } = await ttdService.createDocumentForSign(
      typeCode.专业投资者告知及确认书,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId,
      type: "专业投资者告知及确认书",
      ttdFileId: fileId,
    });
    await this.db.insert(subscribeProcessDocument).values({
      documentId,
      subscribeProcessId: id,
    });
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id))
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { userId, portfolioId, status, state, date, bookDate } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      conditions.push(eq(workflowTask.model.status, statusParsed.data));
    }

    const stateParsed = z.string().safeParse(state);
    if (stateParsed.success) {
      conditions.push(eq(workflowTask.model.state, stateParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      const predicate = timestampEqDate(model.createTime, dateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    const bookDateParsed = z.date().safeParse(bookDate);
    if (bookDateParsed.success) {
      const predicate = timestampEqDate(model.time, bookDateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }

  private withOrder<T extends MySqlSelect>(query: T) {
    query.orderBy(desc(model.createTime));
    return query;
  }

  private getNotificationEntity(
    record: Awaited<ReturnType<typeof this.getByWorkflowId>>,
    type: NotificationType
  ): Parameters<typeof notificationService.create>[0] {
    const { createTime, portfolioName, userId } = record;
    const date = dayjs(createTime).format("YYYY-MM-DD");

    switch (type) {
      case "ttdOrderReviewPass":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}预约订单审核通过`,
            content: `您申请于${date}申购《${portfolioName}》的订单，已审核通过。`,
            key: "交易提醒",
          },
        ];
      case "contractSignEntry":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}基金合同签署通知`,
            content: `您申请于${date}申购《${portfolioName}》的订单，已进入基金合同签署环节。`,
            key: "交易提醒",
          },
        ];
      case "payEntry":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}打款提醒`,
            content: `您申请于${date}申购《${portfolioName}》的订单，已进入打款环节，为保证交易正常完成，请及时划款至募集账户。若已打款，该提醒可忽略。`,
            key: "交易提醒",
          },
        ];
      case "reject":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}预约订单审核未通过`,
            content: `您申请于${date}申购《${portfolioName}》的订单，很遗憾未能审核通过，如有疑问请联系相关负责人。`,
            key: "交易提醒",
          },
        ];

      case "terminate":
        return [
          {
            type: "reminder",
            userId,
            title: `${portfolioName}预约订单中止`,
            content: `您申请于${date}申购《${portfolioName}》的订单已中止，如有疑问请联系相关负责人。`,
            key: "交易提醒",
          },
        ];
    }
  }
}

export default new SubscribeProcessService();
