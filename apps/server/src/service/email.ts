import { range } from "lodash";
import mailer, { SendMailOptions } from "nodemailer";
import env from "../config/env";

class EmailService {
  private createClient() {
    return mailer.createTransport({
      host: "smtp.exmail.qq.com",
      port: 465,
      secure: true,
      auth: {
        user: env.EMAIL_USERNAME,
        pass: env.EMAIL_PASSWORD,
      },
    });
  }

  async getVerifier(email: string) {
    const code = range(0, 6)
      .map(() => Math.floor(Math.random() * 10))
      .reduce((acc, curr) => `${acc}${curr}`, "");

    const client = this.createClient();
    const mailOptions: SendMailOptions = {
      from: `上海衍复投资管理有限公司 <${env.EMAIL_USERNAME}>`,
      to: email,
      subject: "衍复投资——验证码",
      text: `您的验证码为：${code}，请勿泄露于他人！`,
    };

    await client.sendMail(mailOptions);
    return code;
  }
}

export default new EmailService();
