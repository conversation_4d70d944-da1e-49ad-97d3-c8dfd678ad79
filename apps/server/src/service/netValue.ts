import dayjs, { Dayjs } from "dayjs";
import { SQL, and, asc, desc, eq, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import { z } from "zod";
import { infoInputSchema, inputSchema } from "../model/netValue";
import * as netValue from "../schema/netValue";
import disclosureService from "../service/netValueDisclosure";
import portfolioService from "../service/portfolio";
import { convertDouble } from "../utils/db";
import { dateEq, dateGte, dateInArray } from "../utils/drizzle";
import { Service } from "../utils/service";
import disclosureConfService from "./netValueDisclosureConf";
import complianceConfService from "./portfolioComplianceConf";

class NetValueService extends Service {
  private infoEmptyOutput = {
    netValueDate: null,
    netValue: null,
    cumNetValue: null,
    startOfYearNetValue: null,
    startOfYearCumNetValue: null,
    publishNetValue: null,
    publishCumNetValue: null,
  };

  async getInfo(
    input: z.infer<typeof infoInputSchema>,
    identityNumber: string
  ) {
    const hasAccess = await portfolioService.hasPortfolioAccess({
      id: input,
      identityNumber,
    });
    if (!hasAccess) {
      return this.infoEmptyOutput;
    }

    const complianceConf = await complianceConfService.getByPortfolioId(input);
    if (!complianceConf) {
      return this.infoEmptyOutput;
    }

    const netValueDate = await disclosureService.getLatestShareDate(input);
    if (!netValueDate) {
      return this.infoEmptyOutput;
    }

    const latestDate = dayjs(netValueDate);
    const latestQuery = this.db
      .select({
        netValue: convertDouble(netValue.model.netValue),
        cumNetValue: convertDouble(netValue.model.cumNetValue),
        date: netValue.model.date,
      })
      .from(netValue.model)
      .where(
        and(
          eq(netValue.model.portfolioId, input),
          dateEq(netValue.model.date, latestDate.toDate())
        )
      );

    const startOfYear = dayjs(`${latestDate.year()}-01-01`).toDate();
    const startOfYearQuery = this.db
      .select({
        netValue: convertDouble(netValue.model.netValue),
        cumNetValue: convertDouble(netValue.model.cumNetValue),
      })
      .from(netValue.model)
      .where(
        and(
          eq(netValue.model.portfolioId, input),
          dateGte(netValue.model.date, startOfYear)
        )
      )
      .orderBy(asc(netValue.model.date))
      .limit(1);

    const [latestQueryResult, startOfYearQueryResult] = await Promise.all([
      latestQuery,
      startOfYearQuery,
    ]);

    let startOfYearNetValue: number | null = null;
    let startOfYearCumNetValue: number | null = null;
    let publishNetValue: number | null = null;
    let publishCumNetValue: number | null = null;

    if (complianceConf.earningRateVisibility.includes("since_this_year")) {
      startOfYearNetValue = startOfYearQueryResult[0]?.netValue;
      startOfYearCumNetValue = startOfYearQueryResult[0]?.cumNetValue;
    }

    if (complianceConf.earningRateVisibility.includes("since_establish")) {
      publishNetValue = 1;
      publishCumNetValue = 1;
    }

    return {
      netValueDate: latestDate,
      netValue: latestQueryResult[0]?.netValue,
      cumNetValue: latestQueryResult[0]?.cumNetValue,
      startOfYearNetValue,
      startOfYearCumNetValue,
      publishNetValue,
      publishCumNetValue,
    };
  }

  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    const hasAccess = await portfolioService.hasPortfolioAccess({
      id: input.portfolioId,
      identityNumber,
    });
    if (!hasAccess) {
      return [];
    }

    const select = {
      date: netValue.model.date,
      netValue: convertDouble(netValue.model.netValue),
      cumNetValue: convertDouble(netValue.model.cumNetValue),
    };

    let offset: Dayjs | undefined = undefined;
    if (!!input.offset) {
      offset = dayjs(input.offset);
    } else {
      const netValueDate = await disclosureService.getLatestShareDate(
        input.portfolioId
      );
      if (!!netValueDate) {
        offset = dayjs(netValueDate);
      }
    }

    const dates = await disclosureConfService.getDatesByPortfolioId(
      input.portfolioId,
      input.pageSize,
      offset
    );

    let query = this.db.select(select).from(netValue.model).$dynamic();
    query = this.withCondition(query, input, dates);
    query = query.orderBy(desc(netValue.model.date));

    return await query;
  }

  async getAssetNetList(
    input: z.infer<typeof inputSchema>,
    identityNumber: string
  ) {
    const hasAccess = await portfolioService.hasPortfolioAccess({
      id: input.portfolioId,
      identityNumber,
    });
    if (!hasAccess) {
      return [];
    }

    const parentId = await portfolioService.getParentIdByIdSafe(
      input.portfolioId
    );
    if (parentId != null) {
      input.portfolioId = parentId;
    }

    const select = {
      date: netValue.model.date,
      assetNet: convertDouble(netValue.model.assetNet),
    };

    let offset: Dayjs | undefined = undefined;
    if (!!input.offset) {
      offset = dayjs(input.offset);
    } else {
      const netValueDate = await disclosureService.getLatestShareDate(
        input.portfolioId
      );
      if (!!netValueDate) {
        offset = dayjs(netValueDate);
      }
    }

    const dates = await disclosureConfService.getDatesByPortfolioId(
      input.portfolioId,
      input.pageSize,
      offset
    );

    let query = this.db.select(select).from(netValue.model).$dynamic();
    query = this.withCondition(query, input, dates);
    query = query.orderBy(desc(netValue.model.date));

    return await query;
  }

  async getLatestByPortfolioId(portfolioId: number) {
    const date =
      await disclosureConfService.getLatestDateByPortfolioId(portfolioId);
    if (!date) {
      return;
    }

    const result = await this.db
      .select({
        date: netValue.model.date,
        netValue: convertDouble(netValue.model.netValue),
        cumNetValue: convertDouble(netValue.model.cumNetValue),
      })
      .from(netValue.model)
      .where(
        and(
          eq(netValue.model.portfolioId, portfolioId),
          dateEq(netValue.model.date, date.toDate())
        )
      );

    return first(result);
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof inputSchema>,
    dates: Dayjs[]
  ) {
    if (!dates.length) return query.where(sql`false`);

    const { portfolioId } = filter;
    const conditions: SQL[] = [
      eq(netValue.model.portfolioId, portfolioId),
      dateInArray(
        netValue.model.date,
        dates.map((date) => date.toDate())
      ),
    ];

    query.where(and(...conditions));

    return query;
  }
}

export default new NetValueService();
