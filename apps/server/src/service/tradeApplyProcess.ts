import { toCN } from "@portfolio-service/currency";
import { DocumentType } from "@portfolio-service/schema/document";
import { TRPCError } from "@trpc/server";
import dayjs from "dayjs";
import { and, count, eq, inArray } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import textLabel from "../config/textLabel";
import typeCode from "../config/typeCode";
import {
  createInputSchema,
  nextInputSchema,
  reviewInputSchema,
} from "../model/tradeApplyProcess";
import { DataStr } from "../model/ttd";
import * as portfolio from "../schema/portfolio";
import {
  model,
  select,
  selectSchema,
  tradeApplyProcessDocument,
} from "../schema/tradeApplyProcess";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import { tradeApplyMachine as machine } from "../workflow/tradeApply";
import documentService from "./document";
import portfolioService from "./portfolio";
import redeemProcessService from "./redeemProcess";
import subscribeProcessService from "./subscribeProcess";
import ttdService from "./ttd";
import userService from "./user";
import workflowService from "./workflow";

class TradeApplyProcessService extends Service {
  private select = {
    ...select,
    portfolioName: portfolio.select.name,
    portfolioNo: portfolio.select.no,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
    username: user.model.name,
    identityNumber: user.model.identityNumber,
    identityType: user.model.identityType,
    classification: user.model.classification,
  };

  async getById(id: number, userId?: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.id, id))
      .$dynamic();

    query = this.withJoin(query);

    const [record] = await query;

    if (!record || (userId != null && record.userId !== userId)) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByIds(ids: number[]) {
    if (!ids.length) {
      return [];
    }

    let query = this.db
      .select(this.select)
      .from(model)
      .where(inArray(model.id, ids))
      .$dynamic();

    query = this.withJoin(query);
    return await query;
  }

  async getListByIdentityNumber(identityNumber: string) {
    let query = this.db.select(select).from(model).$dynamic();
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = query.where(eq(user.model.identityNumber, identityNumber));

    const result = await query;

    const documentPromises = result.map(async (it) => {
      const documents = await this.getDocumentsById(it.id);
      return { id: it.id, documents };
    });

    const documentsList = await Promise.all(documentPromises);

    return result.map((it) => ({
      ...it,
      documents: documentsList.find(({ id }) => id === it.id)?.documents,
    }));
  }

  async create(input: z.infer<typeof createInputSchema>) {
    const { operatorId, amount, time, parentProcessId, type } = input;

    const service =
      type === "subscribe" ? subscribeProcessService : redeemProcessService;
    const parent = await service.getById(parentProcessId);

    if (parent.status !== "done") {
      throw new TRPCError({ code: "FORBIDDEN", message: "流程正在进行中" });
    }

    const { userId, portfolioId, orderNumber } = parent;

    await transaction.run(async (tx) => {
      const actor = await workflowService
        .createActor(machine)
        .start(operatorId, userId);

      const [result] = await tx.insert(model).values({
        workflowId: actor.id,
        userId,
        portfolioId,
        amount,
        type,
        orderNumber,
        parentProcessId,
        time: dayjs(time).format("YYYY-MM-DD HH:mm:ss"),
      });

      await service.updateTradeApplyProcessId(parentProcessId, result.insertId);
      await this.createTradeApplyDoc(userId, result.insertId, amount, time);
    });
  }

  async getActiveAmount(
    userId: number,
    type: z.infer<typeof selectSchema.shape.type>
  ) {
    const [result] = await this.db
      .select({ amount: count() })
      .from(model)
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .where(
        and(
          eq(model.userId, userId),
          eq(workflowTask.model.status, "active"),
          eq(model.type, type)
        )
      );

    return result;
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(tradeApplyProcessDocument)
      .where(eq(tradeApplyProcessDocument.tradeApplyProcessId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async getDocumentsWithTtdFilesById(id: number) {
    const documents = await this.getDocumentsById(id);

    const ttdFiles = await Promise.all(
      documents.map((document) =>
        ttdService.querySignedDocument(document.ttdFileId)
      )
    );

    return documents.map((document) => ({
      ...document,
      ttdFile: ttdFiles.find((file) => file.fileId === document.ttdFileId),
    }));
  }

  async getTtdFileIdsById(id: number) {
    const documents = await this.getDocumentsById(id);

    const fileIds: string[] = [];
    for (const { ttdFileId, obsolete } of documents) {
      if (!!ttdFileId && !obsolete) {
        fileIds.push(ttdFileId);
      }
    }

    return fileIds;
  }

  async documentsSign(userId: number, input: z.infer<typeof nextInputSchema>) {
    const record = await this.getById(input.id, userId);
    const types: DocumentType[] = ["交易申请书"];

    await this.validateDocumentsSign(input.id, types);

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["tradeApplySign", "tradeApplySignRedo"],
      });

      await actor.send({ type: "event.next" }, userId, null);
    });
  }

  async documentsReview(input: z.infer<typeof reviewInputSchema>) {
    const { id, operatorId, pass } = input;

    const record = await this.getById(id);
    const { userId, amount, time } = record;

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["tradeApplyReview"],
      });

      if (pass) {
        if (record.type === "subscribe") {
          const parent = await subscribeProcessService.getById(
            record.parentProcessId
          );
          if (parent.type === "首次申购") {
            const { orderNumber } = record;
            if (!orderNumber) {
              throw new TRPCError({ code: "FORBIDDEN", message: "无订单号" });
            }

            await ttdService.updateOrderAmount(
              record.orderNumber!,
              record.amount
            );
          }
        }
      }

      if (!pass) {
        const documents = await this.getDocumentsById(id);
        await documentService.makeObsolete(
          documents.map((it) => it.id),
          ["交易申请书"]
        );
        await this.createTradeApplyDoc(userId, id, amount, new Date(time));
      }

      await actor.send(
        { type: pass ? "event.next" : "event.redo" },
        operatorId,
        record.userId
      );
    });
  }

  private async validateDocumentsSign(id: number, types: DocumentType[]) {
    const relations = await this.db
      .select()
      .from(tradeApplyProcessDocument)
      .where(eq(tradeApplyProcessDocument.tradeApplyProcessId, id));

    if (!relations.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const promises = relations.map(async ({ documentId }) => {
      const { ttdFileId, type } = await documentService.getById(documentId);
      if (!ttdFileId) {
        return;
      }

      if (!type || !types.includes(type)) {
        return;
      }

      const ttdDocument = await ttdService.querySignedDocument(ttdFileId);
      if (!ttdDocument) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      if (ttdDocument.investorState !== 1) {
        throw new TRPCError({ code: "FORBIDDEN", message: "文件未签署" });
      }
    });

    await Promise.all(promises);
  }

  private async createTradeApplyDoc(
    userId: number,
    id: number,
    amount: number,
    time: Date
  ) {
    const record = await this.getById(id);
    const { portfolioId } = record;

    const user = await userService.getById(userId);
    const portfolio = await portfolioService.getByIdInternal(portfolioId);

    const dataStr: DataStr = {
      [textLabel.基金名称]: { value: portfolio.name! },
      [textLabel.基金代码]: { value: portfolio.no! },
      [textLabel.投资者全称]: { value: user.name },
      [textLabel.投资者类型]: { value: user.classification! },
      [textLabel.证件类型]: { value: user.identityType! },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.移动电话]: { value: user.phone },
      [textLabel.日期]: { value: dayjs(time).format("YYYY年MM月DD日") },
    };

    if (!!user.phone) {
      dataStr[textLabel.移动电话] = { value: user.phone };
    }

    const amountKey =
      record.type === "subscribe" ? textLabel.认申购金额 : textLabel.赎回份额;

    dataStr[amountKey] = { value: toCN(amount) };

    const templateCode =
      record.type === "subscribe" ? typeCode.认申购单 : typeCode.赎回单;

    const { fileId } = await ttdService.createDocumentForSign(
      templateCode,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId,
      type: "交易申请书",
      ttdFileId: fileId,
    });
    await this.db.insert(tradeApplyProcessDocument).values({
      documentId,
      tradeApplyProcessId: id,
    });
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id))
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }
}

export default new TradeApplyProcessService();
