import { typeSchema } from "@portfolio-service/schema/report";
import { TRPCError } from "@trpc/server";
import { SQL, and, count, desc, eq } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  createInputSchema,
  deleteInputSchema,
  inputSchema,
  portfolioIdSchema,
  publishInputSchema,
  publishedSchema,
  updateInputSchema,
} from "../model/periodicReport";
import * as fileUpload from "../schema/fileUpload";
import { model, select } from "../schema/periodicReport";
import * as portfolio from "../schema/portfolio";
import { dateEq } from "../utils/drizzle";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";

class PeriodicReportService extends Service {
  async getList(input: z.infer<typeof inputSchema>) {
    let query = this.db.select(select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);
    query = query.orderBy(desc(model.date));

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async create(input: z.infer<typeof createInputSchema>) {
    await this.db.insert(model).values(input);
  }

  async update(input: z.infer<typeof updateInputSchema>) {
    const { id, ...data } = input;
    const predicate = eq(model.id, id);

    const record = await this.db.select().from(model).where(predicate);
    if (!record) throw new TRPCError({ code: "NOT_FOUND" });

    await this.db.update(model).set(data).where(predicate);
  }

  async publish(input: z.infer<typeof publishInputSchema>) {
    const { id, published } = input;
    const predicate = eq(model.id, id);

    const record = await this.db.select().from(model).where(predicate);
    if (!record) throw new TRPCError({ code: "NOT_FOUND" });

    await this.db.update(model).set({ published }).where(predicate);
  }

  async delete(input: z.infer<typeof deleteInputSchema>) {
    await this.db.delete(model).where(eq(model.id, input.id));
  }

  withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof inputSchema>
  ) {
    query
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id))
      .leftJoin(fileUpload.model, eq(model.fileUploadId, fileUpload.model.id));

    if (!filter) return query;

    const { portfolioId, type, date, published } = filter;
    const conditions: SQL[] = [];

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    const typeParsed = typeSchema.safeParse(type);
    if (typeParsed.success) {
      conditions.push(eq(model.type, typeParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      conditions.push(dateEq(model.date, dateParsed.data));
    }

    const publishedParsed = publishedSchema.safeParse(published);
    if (publishedParsed.success) {
      conditions.push(eq(model.published, publishedParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new PeriodicReportService();
