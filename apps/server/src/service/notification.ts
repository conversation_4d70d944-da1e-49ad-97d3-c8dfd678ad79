import { TRPCError } from "@trpc/server";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  createInputSchema,
  keySchema,
  listInputScheam,
  readSchema,
  typeSchema,
  userIdSchema,
} from "../model/notification";
import { model, select } from "../schema/notification";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";

class NotificationService extends Service {
  async getById(id: number, userId?: number) {
    const predicate: SQL[] = [eq(model.id, id)];
    if (userId != null) {
      predicate.push(eq(model.userId, userId));
    }

    const [record] = await this.db
      .select(select)
      .from(model)
      .where(and(...predicate));
    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getList(input: z.infer<typeof listInputScheam>) {
    let query = this.db.select(select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);
    query = query.orderBy(desc(model.createTime));

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getOwnList(userId: number, input: z.infer<typeof listInputScheam>) {
    return await this.getList({ ...input, userId });
  }

  async getUnreadAmount(
    userId: number,
    input: z.infer<typeof listInputScheam>
  ) {
    let query = this.db.select({ amount: count() }).from(model).$dynamic();
    query = this.withCondition(query, { ...input, userId, read: false });

    const [result] = await query;
    return result;
  }

  async create(input: z.infer<typeof createInputSchema>[]) {
    if (!input.length) return;

    await this.db.insert(model).values(input);
  }

  async read(userId: number, id: number) {
    const record = await this.getById(id, userId);
    await this.db
      .update(model)
      .set({ read: true })
      .where(eq(model.id, record.id));
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputScheam>
  ) {
    if (!filter) return query;

    const { key, read, userId, type } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const keyParsed = keySchema.safeParse(key);
    if (keyParsed.success) {
      conditions.push(eq(model.key, keyParsed.data));
    }

    const readParsed = readSchema.safeParse(read);
    if (readParsed.success) {
      conditions.push(eq(model.read, readParsed.data));
    }

    const typeParsed = typeSchema.safeParse(type);
    if (typeParsed.success) {
      conditions.push(eq(model.type, typeParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new NotificationService();
