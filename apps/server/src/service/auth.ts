import { TRPCError } from "@trpc/server";
import { compare, hash } from "bcrypt";
import { and, eq } from "drizzle-orm";
import { first, isEqual } from "lodash";
import { z } from "zod";
import { loginInputSchema, sendCodeInputSchema } from "../model/auth";
import { faceRecognitionDataSchema } from "../model/identityVerification";
import { registerIdentityValidateInputSchema } from "../model/register";
import {
  forgetPasswordFaceRecognitionInputSchema,
  forgetPasswordFaceRecognitionUrlInputSchema,
  forgetPasswordInputSchema,
} from "../model/user";
import * as registerUser from "../schema/registerUser";
import { model, User } from "../schema/user";
import ttdUrlService from "../service/ttdUrl";
import { decrypt, encrypt } from "../utils/crypto";
import { isDevEnv } from "../utils/dev";
import { Service } from "../utils/service";
import { parseJSON } from "../utils/zod";
import emailService from "./email";
import smsService from "./sms";
import ttdService from "./ttd";

class AuthService extends Service {
  async sendCode(input: z.infer<typeof sendCodeInputSchema>) {
    if (isDevEnv()) return "123456";

    if (input.type === "phone") {
      return await smsService.getVerifier(input.phone);
    } else if (input.type === "email") {
      return await emailService.getVerifier(input.email);
    }

    throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
  }

  async loginByCode(
    input: z.infer<typeof loginInputSchema>,
    validation: z.infer<typeof loginInputSchema> | undefined
  ) {
    if (!validation) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "请先获取验证码" });
    }

    let user: User | undefined = undefined;

    if (input.type === "phone") {
      const [record] = await this.db
        .select()
        .from(model)
        .where(eq(model.phone, input.phone));
      user = record;
    } else if (input.type === "email") {
      const [record] = await this.db
        .select()
        .from(model)
        .where(eq(model.email, input.email));
      user = record;
    }

    if (!user) {
      throw new TRPCError({ code: "FORBIDDEN", message: "登录信息有误" });
    }

    if (!isDevEnv()) {
      if (!isEqual(input, validation)) {
        throw new TRPCError({ code: "FORBIDDEN", message: "验证码错误" });
      }
    }

    return user;
  }

  async loginByIdentityNumber(username: string, password: string) {
    if (isDevEnv()) {
      if (username === "test" && password === "DKX2S0rk") {
        username = "310105198604250454";
        password = "<EMAIL>";
      }
    }

    const [record] = await this.db
      .select()
      .from(model)
      .where(eq(model.identityNumber, username));

    if (!record) {
      throw new TRPCError({ code: "FORBIDDEN", message: "登录信息有误" });
    }

    if (record.identityNumber === username) {
      let predicate: boolean;
      if (!!record.password) {
        predicate = await compare(password, record.password);
      } else {
        const identitySliced = record.identityNumber.slice(
          Math.max(0, record.identityNumber.length - 8)
        );
        predicate = password === identitySliced;
      }

      if (isDevEnv()) {
        predicate = predicate || password === "<EMAIL>";
      }

      if (!predicate) {
        throw new TRPCError({ code: "FORBIDDEN", message: "登录信息有误" });
      }
    }

    return record;
  }

  async getByIdentityNumber(identityNumber: string) {
    const [result] = await this.db
      .select()
      .from(model)
      .where(eq(model.identityNumber, identityNumber));

    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async verifyIdentity(
    input: z.infer<typeof registerIdentityValidateInputSchema>
  ) {
    const [existing] = await this.db
      .select()
      .from(registerUser.model)
      .where(eq(registerUser.model.identityNumber, input.identityNumber));

    if (input.userType === "个人") {
      const verificationNeeded =
        !existing ||
        existing.name !== input.name ||
        !existing.identityVerificationStatus;

      if (!verificationNeeded) return true;

      const { authorResult } = await ttdService.verifyIdentity("duo", {
        name: input.name,
        identityNumber: input.identityNumber,
      });
      return authorResult;
    } else if (input.userType === "机构") {
      const representativeVerificationNeeded =
        !existing ||
        existing.representativeName !== input.representativeName ||
        existing.representativeIdentityNumber !==
          input.representativeIdentityNumber ||
        !existing.identityVerificationStatus;

      if (!representativeVerificationNeeded) return true;

      const { authorResult } = await ttdService.verifyIdentity("duo", {
        name: input.representativeName,
        identityNumber: input.representativeIdentityNumber,
      });
      return authorResult;
    }
  }

  async createTtdInvestor(user: registerUser.RegisterUser) {
    switch (user.userType) {
      case "个人":
        return await ttdService.createIndividualInvestor({
          name: user.name,
          identityType: user.identityType,
          identityNumber: user.identityNumber,
          valid: user.identityVerificationStatus,
        });
      case "机构":
        return await ttdService.createOrganizationInvestor({
          name: user.name,
          identityType: user.identityType,
          identityNumber: user.identityNumber,
          representativeName: user.representativeName!,
          representativeIdentityType: user.representativeIdentityType!,
          representativeIdentityNumber: user.representativeIdentityNumber!,
          valid: user.identityVerificationStatus,
        });
      default:
        throw new TRPCError({ code: "FORBIDDEN" });
    }
  }

  async getForgetPasswordFaceRecognitionUrl(
    input: z.infer<typeof forgetPasswordFaceRecognitionUrlInputSchema>,
    sessionId: string
  ) {
    const { identityNumber, redirectUrl } = input;

    const result = await this.db
      .select()
      .from(model)
      .where(eq(model.identityNumber, identityNumber));
    const user = first(result);

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "未找到用户，请检查信息是否填写正确",
      });
    }

    const data: z.infer<typeof faceRecognitionDataSchema> = {
      userId: user.id,
      sessionId,
    };
    const suffix = encrypt(JSON.stringify(data));

    const url = await ttdUrlService.getFaceRecognitionUrl(
      { redirectUrl: `${redirectUrl}/${suffix}` },
      user.id
    );

    return { url, user };
  }

  async forgetPasswordFaceRecognition(
    userId: number,
    input: z.infer<typeof forgetPasswordFaceRecognitionInputSchema>,
    sessionId: string
  ) {
    const { encrypted } = input;

    const decrypted = decrypt(encrypted);
    const data = parseJSON(decrypted, faceRecognitionDataSchema);

    console.log(data, userId, sessionId);

    if (!data || data.userId !== userId || data.sessionId !== sessionId) {
      throw new TRPCError({ code: "FORBIDDEN", message: "会话失效，请重试" });
    }
  }

  async forgetPassword(
    id: number,
    input: z.infer<typeof forgetPasswordInputSchema>
  ) {
    const hashed = await hash(input.password, 10);

    await this.db
      .update(model)
      .set({ password: hashed })
      .where(eq(model.id, id));
  }
}

export default new AuthService();
