import { SQL, and, count, eq, inArray, isNotNull, isNull } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import { z } from "zod";
import {
  deleteInputSchema,
  inputSchema,
  portfolioIdSchema,
  statusSchema,
} from "../model/portfolioComplianceConf";
import {
  model as portfolioModel,
  select as portfolioSelect,
  selectSchema as portfolioSelectSchema,
  stateSchema,
} from "../schema/portfolio";
import {
  earningRateVisibilityTransform,
  earningVisibilityTransform,
  fieldVisibilityTransform,
  insertSchema,
  model,
  select,
  visibilitySchema,
  visibilityTransform,
} from "../schema/portfolioComplianceConf";
import { withMutex } from "../utils/lock";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import { dateGte, dateLt } from "../utils/drizzle";
import dayjs from "dayjs";
import { datePattern } from "@portfolio-service/schema/utils";

class PortfolioComplianceConfService extends Service {
  private listSelect = {
    ...select,
    portfolioId: portfolioSelect.id,
    portfolioName: portfolioSelect.name,
    portfolioNo: portfolioSelect.no,
    portfolioState: portfolioSelect.state,
    portfolioPublishDate: portfolioSelect.publishDate,
  };

  async getByPortfolioIds(portfolioIds: number[]) {
    if (!portfolioIds.length) {
      return [];
    }

    return await this.db
      .select(select)
      .from(model)
      .where(inArray(model.portfolioId, portfolioIds));
  }

  async getByPortfolioId(portfolioId: number) {
    const result = await this.getByPortfolioIds([portfolioId]);
    return first(result);
  }

  async getList(input: z.infer<typeof inputSchema>) {
    let query = this.db.select(this.listSelect).from(portfolioModel).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);

    let amountQuery = this.db
      .select({ value: count() })
      .from(portfolioModel)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async getDisplayList(input: z.infer<typeof inputSchema>) {
    let query = this.db.select(this.listSelect).from(portfolioModel).$dynamic();
    query = this.withCondition(query, input);

    const queryResult = await query;
    const items = queryResult.map((it) => {
      return {
        portfolioId: it.portfolioId,
        status: it.status ? "已设置" : "未设置",
        portfolioName: it.portfolioName,
        portfolioNo: it.portfolioNo,
        portfolioPublishDate: it.portfolioPublishDate
          ? dayjs(it.portfolioPublishDate).format(datePattern)
          : undefined,
        portfolioState: it.portfolioState,
        visibility: visibilityTransform[it.visibility],
        earningVisibility: it.earningVisibility
          ?.map((it) => earningVisibilityTransform[it])
          ?.join("，"),
        fieldVisibility: it.fieldVisibility
          ?.map((it) => fieldVisibilityTransform[it])
          ?.join("，"),
        reportVisibility: visibilityTransform[it.reportVisibility],
        earningRateVisibility: it.earningRateVisibility
          ?.map((it) => earningRateVisibilityTransform[it])
          ?.join("，"),
      };
    });

    return { items, amount: queryResult.length };
  }

  async update(input: z.infer<typeof insertSchema>) {
    const {
      portfolioId,
      fieldVisibility,
      earningVisibility,
      earningRateVisibility,
      ...rest
    } = input;

    const data = {
      fieldVisibility: JSON.stringify(fieldVisibility),
      earningVisibility: JSON.stringify(earningVisibility),
      earningRateVisibility: JSON.stringify(earningRateVisibility),
      ...rest,
    };

    await withMutex(
      ["portfolioComplianceConf.update", portfolioId],
      async () =>
        await this.db
          .insert(model)
          .values({ portfolioId, ...data })
          .onDuplicateKeyUpdate({ set: data })
    );
  }

  async delete(input: z.infer<typeof deleteInputSchema>) {
    await this.db.delete(model).where(eq(model.portfolioId, input.portfolioId));
  }

  async sanitizePortfolioFields(
    portfolio: Partial<z.infer<typeof portfolioSelectSchema>>
  ) {
    const { id } = portfolio;
    if (id == null) {
      return;
    }

    const conf = await this.getByPortfolioId(id);
    if (!conf) {
      return;
    }

    const { fieldVisibility } = conf;

    if (!fieldVisibility.includes("risk")) {
      portfolio.riskLevel = null;
    }

    if (!fieldVisibility.includes("custodian")) {
      portfolio.custodian = null;
    }

    if (!fieldVisibility.includes("no")) {
      portfolio.no = null;
    }

    if (!fieldVisibility.includes("establish")) {
      portfolio.publishDate = null;
    }

    return portfolio;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof inputSchema>
  ) {
    query.leftJoin(model, eq(portfolioModel.id, model.portfolioId));

    const conditions: SQL[] = [
      eq(portfolioModel.investmentAdviserType, "私募产品（非投顾）"),
    ];

    const {
      portfolioId,
      portfolioState,
      status,
      visibility,
      reportVisibility,
      portfolioPublishDate,
    } = filter || {};

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(portfolioModel.id, portfolioIdParsed.data));
    }

    const portfolioStateParsed = stateSchema.safeParse(portfolioState);
    if (portfolioStateParsed.success) {
      conditions.push(eq(portfolioModel.state, portfolioStateParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      const field = model.portfolioId;
      conditions.push(
        statusParsed.data === "已设置" ? isNotNull(field) : isNull(field)
      );
    }

    const visibilityParsed = visibilitySchema.safeParse(visibility);
    if (visibilityParsed.success) {
      conditions.push(eq(model.visibility, visibilityParsed.data));
    }

    const reportVisibilityParsed = visibilitySchema.safeParse(reportVisibility);
    if (reportVisibilityParsed.success) {
      conditions.push(eq(model.reportVisibility, reportVisibilityParsed.data));
    }

    const publishDateSchema = z.tuple([z.date(), z.date()]);
    const publishDateParsed = publishDateSchema.safeParse(portfolioPublishDate);
    if (publishDateParsed.success) {
      const [start, end] = publishDateParsed.data;
      const predicate = and(
        dateGte(portfolioModel.publishDate, start),
        dateLt(portfolioModel.publishDate, end)
      );
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new PortfolioComplianceConfService();
