import { statusSchema } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import { z } from "zod";
import textLabel from "../config/textLabel";
import typeCode from "../config/typeCode";
import {
  createInputSchema,
  listInputSchema,
  managerCreateInputSchema,
  nextInputSchema,
  riskTestInputSchema,
} from "../model/riskTestProcess";
import { DataStr } from "../model/ttd";
import * as document from "../schema/document";
import {
  riskTestProcessDocument as documentsRelation,
  model,
  select,
} from "../schema/riskTestProcess";
import * as questionaire from "../schema/riskTestQuestionaire";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import ttdService from "../service/ttd";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import { riskTestMachine as machine } from "../workflow/riskTest";
import documentService from "./document";
import riskTestService from "./riskTest";
import questionaireService from "./riskTestQuestionaire";
import userService from "./user";
import workflowService from "./workflow";

class RiskTestProcessService extends Service {
  private select = {
    ...select,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db
      .select({ ...this.select, username: user.model.name })
      .from(model)
      .$dynamic();

    query = withPagination(query, input);
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = this.withJoin(query);
    query = this.withCondition(query, input);
    query = query.orderBy(desc(model.createTime));

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = amountQuery.leftJoin(
      user.model,
      eq(model.userId, user.model.id)
    );
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getByIdSafe(id: number, userId?: number) {
    const predicate: SQL[] = [eq(model.id, id)];
    if (userId != null) predicate.push(eq(model.userId, userId));

    let query = this.db
      .select({ ...this.select, config: questionaire.select.config })
      .from(model)
      .where(and(...predicate))
      .$dynamic();

    query = this.withJoin(query);
    query = query.leftJoin(
      questionaire.model,
      eq(model.questionaireId, questionaire.model.id)
    );

    return first(await query);
  }

  async getById(id: number, userId?: number) {
    const record = await this.getByIdSafe(id, userId);
    if (!record) throw new TRPCError({ code: "NOT_FOUND" });

    return record;
  }

  async getOwn(userId: number) {
    const { questionaireId, answers, ...select } = this.select;

    let query = this.db
      .select(select)
      .from(model)
      .where(eq(model.userId, userId))
      .orderBy(desc(model.createTime))
      .limit(1)
      .$dynamic();
    query = this.withJoin(query);

    const result = await query;
    return { item: first(result) };
  }

  async getOwnActive(userId: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(
        and(eq(model.userId, userId), eq(workflowTask.model.status, "active"))
      )
      .orderBy(desc(model.createTime))
      .limit(1)
      .$dynamic();
    query = this.withJoin(query);

    const result = await query;
    return { item: first(result) };
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(documentsRelation)
      .where(eq(documentsRelation.riskTestProcessId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async getDocumentsDetailById(id: number) {
    const documents = await this.getDocumentsById(id);

    const ttdFileIds = documents
      .map((it) => it.ttdFileId)
      .filter(Boolean) as string[];

    const ttdFiles = await Promise.all(
      ttdFileIds.map((id) => ttdService.querySignedDocument(id))
    );

    return documents.map((document) => ({
      ...document,
      ttdFile: ttdFiles.find((file) => file.fileId === document.ttdFileId),
    }));
  }

  async getTtdFileIdsById(id: number) {
    const documents = await this.getDocumentsById(id);

    const fileIds: string[] = [];
    for (const { ttdFileId, obsolete } of documents) {
      if (!!ttdFileId && !obsolete) {
        fileIds.push(ttdFileId);
      }
    }

    return fileIds;
  }

  async create(
    userId: number,
    input: z.infer<typeof createInputSchema>,
    operatorId: number = userId
  ) {
    const { questionaireId, answers } = input;
    await withMutex(["riskTestProcess.general", userId], async () => {
      const active = await this.getOwnActive(userId);
      if (!!active.item) {
        throw new TRPCError({ code: "FORBIDDEN", message: "流程正在进行中" });
      }

      const { score, level } = await questionaireService.getScoreAndLevel(
        questionaireId,
        answers
      );

      await transaction.run(async (tx) => {
        const actor = await workflowService
          .createActor(machine)
          .start(operatorId, userId);
        const [result] = await tx.insert(model).values({
          workflowId: actor.id,
          userId,
          questionaireId,
          score,
          level,
          answers: JSON.stringify(answers),
        });
        await this.generateDocumentsForSign(result.insertId);
      });
    });
  }

  async managerCreate(input: z.infer<typeof managerCreateInputSchema>) {
    const { userId, operatorId, skipQuestionnaire } = input;

    if (skipQuestionnaire) {
      const riskTest = await riskTestService.getLastestByUserId(userId);
      if (!riskTest) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "该客户从未进行过风险测评，不能跳过问卷环节",
        });
      }

      const { questionaireId, answers } = riskTest;

      await this.create(
        userId,
        { questionaireId, answers: JSON.parse(answers) },
        operatorId
      );

      return;
    }

    await withMutex(["riskTestProcess.general", userId], async () => {
      const active = await this.getOwnActive(userId);
      if (!!active.item) {
        throw new TRPCError({ code: "FORBIDDEN", message: "流程正在进行中" });
      }

      const questionnaire = await questionaireService.getLatest(userId);
      if (!questionnaire) {
        throw new TRPCError({ code: "FORBIDDEN", message: "客户未设置类型" });
      }

      await transaction.run(async (tx) => {
        const actor = await workflowService
          .createActor(machine, { input: { isManager: true } })
          .start(operatorId, userId);

        await tx.insert(model).values({
          workflowId: actor.id,
          userId,
          questionaireId: questionnaire.id,
        });
      });
    });
  }

  async riskTest(userId: number, input: z.infer<typeof riskTestInputSchema>) {
    const { id, questionaireId, answers } = input;

    await withMutex(["riskTestProcess.general", userId], async () => {
      const record = await this.getById(id);
      if (record.questionaireId !== questionaireId) {
        throw new TRPCError({ code: "FORBIDDEN" });
      }

      const { score, level } = await questionaireService.getScoreAndLevel(
        questionaireId,
        answers
      );

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: ["riskTest"],
        });
        await this.db.update(model).set({
          score,
          level,
          answers: JSON.stringify(answers),
        });
        await this.generateDocumentsForSign(id);
        await actor.send({ type: "event.next" }, userId, null);
      });
    });
  }

  async documentsSign(userId: number, input: z.infer<typeof nextInputSchema>) {
    const record = await this.getById(input.id, userId);
    await this.validateDocumentsSign(input.id);

    const { level, score } = record;
    if (!level || score == null) {
      throw new TRPCError({ code: "FORBIDDEN" });
    }

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["documentsSign"],
      });
      const [result] = await riskTestService.create({
        answers: JSON.stringify(record.answers),
        level,
        score,
        questionaireId: record.questionaireId,
        userId,
      });
      await this.db
        .update(model)
        .set({ riskTestId: result.insertId })
        .where(eq(model.id, record.id));
      await this.db
        .update(user.model)
        .set({ riskLevel: record.level })
        .where(eq(user.model.id, userId));
      await actor.send({ type: "event.next" }, userId, null);
    });
  }

  async getRiskTestQuestionnaireDocument(riskTestId: number) {
    const result = await this.db
      .select({ id: model.id })
      .from(model)
      .where(eq(model.riskTestId, riskTestId));
    const record = first(result);
    if (!record) return;

    return await this.getRiskTestQuestionnaireDocumentById(record.id);
  }

  async getRiskTestQuestionnaireDocumentById(id: number) {
    const documentQuery = this.db
      .select(document.select)
      .from(documentsRelation)
      .$dynamic();

    documentQuery
      .leftJoin(
        document.model,
        eq(documentsRelation.documentId, document.model.id)
      )
      .where(
        and(
          eq(documentsRelation.riskTestProcessId, id),
          eq(document.model.type, "风险测评问卷"),
          eq(document.model.obsolete, false)
        )
      );
    const documentResult = await documentQuery;
    return first(documentResult);
  }

  async getQuestionnaireLink(id: number) {
    const document = await this.getRiskTestQuestionnaireDocumentById(id);
    if (!document?.ttdFileId) return { url: undefined };

    const ttdFile = await ttdService.querySignedDocumentSafe(
      document.ttdFileId
    );

    return { url: ttdFile?.url };
  }

  private async generateDocumentsForSign(id: number) {
    const record = await this.getById(id);
    const user = await userService.getById(record.userId);

    const { level, score } = record;
    if (!level || score == null) {
      throw new TRPCError({ code: "FORBIDDEN" });
    }

    const dataStr: DataStr = {
      [textLabel.投资者姓名]: { value: user.name },
      [textLabel.移动电话]: { value: user.phone || "" },
      [textLabel.证件类型]: { value: user.identityType },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.总分]: { value: score.toString() || "" },
      [textLabel.投资者风险等级]: { value: record.level || "" },
    };

    for (const answer of record.answers) {
      const key =
        textLabel[`ans${answer.questionId}` as keyof typeof textLabel];
      const value = answer.optionId;

      if (!key || !value) {
        continue;
      }

      dataStr[key] = { value };
    }

    const levelKey = textLabel[level];
    dataStr[levelKey] = { value: "√" };

    const portfolioLevel = level
      .slice()
      .replace("C", "R") as keyof typeof textLabel;

    dataStr[textLabel.产品风险等级] = { value: portfolioLevel };

    const portfolioLevelKey = textLabel[portfolioLevel];
    dataStr[portfolioLevelKey] = { value: "√" };

    const { fileId } = await ttdService.createDocumentForSign(
      typeCode.个人风险测评问卷,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId: record.userId,
      type: "风险测评问卷",
      ttdFileId: fileId,
    });
    await this.db.insert(documentsRelation).values({
      documentId,
      riskTestProcessId: record.id,
    });
  }

  private async validateDocumentsSign(id: number) {
    const relations = await this.db
      .select()
      .from(documentsRelation)
      .where(eq(documentsRelation.riskTestProcessId, id));

    if (!relations.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const promises = relations.map(async ({ documentId }) => {
      const { ttdFileId } = await documentService.getById(documentId);
      if (!ttdFileId) {
        return;
      }

      const ttdDocument = await ttdService.querySignedDocument(ttdFileId);
      if (!ttdDocument) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      if (ttdDocument.investorState !== 1) {
        throw new TRPCError({ code: "FORBIDDEN", message: "文件未签署" });
      }
    });

    await Promise.all(promises);
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query.leftJoin(
      workflowTask.model,
      eq(model.workflowId, workflowTask.model.workflowId)
    );

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { state, status, userId } = filter;
    const conditions: SQL[] = [];

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      conditions.push(eq(workflowTask.model.status, statusParsed.data));
    }

    const stateParsed = z.string().safeParse(state);
    if (stateParsed.success) {
      conditions.push(eq(workflowTask.model.state, stateParsed.data));
    }

    const userIdParsed = z.number().safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new RiskTestProcessService();
