import { toCN } from "@portfolio-service/currency";
import { DocumentType } from "@portfolio-service/schema/document";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import AdmZip from "adm-zip";
import dayjs from "dayjs";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import path from "path";
import { z } from "zod";
import env from "../config/env";
import textLabel from "../config/textLabel";
import typeCode from "../config/typeCode";
import {
  createInputSchema,
  listInputSchema,
  nextInputSchema,
  NotificationType,
  ownListInputSchema,
  portfolioIdSchema,
  reviewInputSchema,
  terminateInputSchema,
  userIdSchema,
} from "../model/redeemProcess";
import { DataStr } from "../model/ttd";
import * as portfolio from "../schema/portfolio";
import { model, redeemProcessDocument, select } from "../schema/redeemProcess";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import { timestampEqDate } from "../utils/drizzle";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import {
  redeemMachine as machine,
  redeemActiveStates,
} from "../workflow/redeem";
import documentService from "./document";
import fileUploadService from "./fileUpload";
import notificationService from "./notification";
import portfolioService from "./portfolio";
import subscribeProcessService from "./subscribeProcess";
import tradeApplyProcessService from "./tradeApplyProcess";
import ttdService from "./ttd";
import userService from "./user";
import workflowService from "./workflow";

class RedeemService extends Service {
  private select = {
    ...select,
    portfolioName: portfolio.select.name,
    portfolioNo: portfolio.select.no,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
    username: user.model.name,
    userType: user.model.type,
    identityNumber: user.model.identityNumber,
    identityType: user.model.identityType,
    classification: user.model.classification,
  };

  async getByIdSafe(id: number, userId?: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.id, id))
      .$dynamic();

    query = this.withJoin(query);

    const [record] = await query;

    if (!record || (userId != null && record.userId !== userId)) {
      return;
    }

    return record;
  }

  async getById(id: number, userId?: number) {
    const record = await this.getByIdSafe(id, userId);
    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getByWorkflowId(workflowId: string) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.workflowId, workflowId))
      .$dynamic();

    query = this.withJoin(query);
    const [record] = await query;

    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getListByIdentityNumber(identityNumber: string) {
    let query = this.db.select(select).from(model).$dynamic();
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = query.where(eq(user.model.identityNumber, identityNumber));

    const result = await query;

    const documentPromises = result.map(async (it) => {
      const documents = await this.getDocumentsById(it.id);
      return { id: it.id, documents };
    });

    const documentsList = await Promise.all(documentPromises);

    return result.map((it) => ({
      ...it,
      documents: documentsList.find(({ id }) => id === it.id)?.documents,
    }));
  }

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input);
    query = this.withOrder(query);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    const tradeApplyProcessIds = queryResult
      .map((it) => it.tradeApplyProcessId)
      .filter(Boolean) as number[];

    const tradeApplyProcesses =
      await tradeApplyProcessService.getByIds(tradeApplyProcessIds);

    const result = queryResult.map((it) => ({
      ...it,
      tradeApplyProcess: tradeApplyProcesses.find(
        (trade) => trade.parentProcessId === it.id
      ),
    }));

    return paginationResult(result, amountQueryResult[0].value);
  }

  async getOwnList(userId: number, input: z.infer<typeof ownListInputSchema>) {
    return await this.getList({ ...input, userId });
  }

  async getActiveAmount(userId: number) {
    const [result] = await this.db
      .select({ amount: count() })
      .from(model)
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .where(
        and(eq(model.userId, userId), eq(workflowTask.model.status, "active"))
      );

    const tradeApplyResult = await tradeApplyProcessService.getActiveAmount(
      userId,
      "subscribe"
    );

    return { amount: result.amount + tradeApplyResult.amount };
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(redeemProcessDocument)
      .where(eq(redeemProcessDocument.redeemProcessId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async getDocumentsWithTtdFilesById(id: number) {
    const documents = await this.getDocumentsById(id);

    const ttdFileIds = documents
      .map((it) => it.ttdFileId)
      .filter(Boolean) as string[];

    const ttdFiles = await Promise.all(
      ttdFileIds.map((id) => ttdService.querySignedDocument(id))
    );

    return documents.map((document) => ({
      ...document,
      ttdFile: ttdFiles.find((file) => file.fileId === document.ttdFileId),
    }));
  }

  async getTtdFileIdsById(id: number) {
    const documents = await this.getDocumentsById(id);

    const fileIds: string[] = [];
    for (const { ttdFileId, obsolete } of documents) {
      if (!!ttdFileId && !obsolete) {
        fileIds.push(ttdFileId);
      }
    }

    return fileIds;
  }

  async create(input: z.infer<typeof createInputSchema>) {
    const { operatorId, portfolioId, userId, shareAmount, time } = input;

    const subscribeProcess =
      await subscribeProcessService.getByUserIdAndPortfolioIdSafe(
        userId,
        portfolioId
      );

    const id = await transaction.run(async (tx) => {
      const actor = await workflowService
        .createActor(machine)
        .start(operatorId, userId);

      const [result] = await tx.insert(model).values({
        workflowId: actor.id,
        userId,
        portfolioId,
        shareAmount,
        orderNumber: subscribeProcess?.orderNumber,
        time: dayjs(time).format("YYYY-MM-DD HH:mm:ss"),
      });
      const id = result.insertId;

      await this.createTradeApplyDoc(userId, id, shareAmount, time);

      return id;
    });

    if (id) {
      return await this.getById(id);
    }
  }

  async documentsSign(userId: number, input: z.infer<typeof nextInputSchema>) {
    await withMutex(["redeemProcessService.documentsSign", input], async () => {
      const record = await this.getById(input.id, userId);
      const types: DocumentType[] = ["交易申请书"];

      await this.validateDocumentsSign(input.id, types);

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: ["documentsSign", "documentsSignRedo"],
        });

        await actor.send({ type: "event.next" }, userId, null);
      });
    });
  }

  async documentsReview(input: z.infer<typeof reviewInputSchema>) {
    const { id, operatorId, pass } = input;

    await withMutex(
      ["redeemProcessService.documentsReview", input],
      async () => {
        const record = await this.getById(id);
        const { userId, shareAmount, time } = record;

        await transaction.run(async () => {
          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["documentsReview"],
          });

          if (!pass) {
            const documents = await this.getDocumentsById(id);
            await documentService.makeObsolete(
              documents.map((it) => it.id),
              ["交易申请书"]
            );
            await this.createTradeApplyDoc(
              userId,
              id,
              shareAmount,
              new Date(time)
            );
          }

          await actor.send(
            { type: pass ? "event.next" : "event.redo" },
            operatorId,
            record.userId
          );
        });
      }
    );
  }

  async updateTradeApplyProcessId(id: number, processId: number) {
    await this.db
      .update(model)
      .set({ tradeApplyProcessId: processId })
      .where(eq(model.id, id));
  }

  async terminate(input: z.infer<typeof terminateInputSchema>) {
    await withMutex(["redeemProcessService.terminate", input], async () => {
      const { id, operatorId } = input;

      const record = await this.getById(id);

      await transaction.run(async () => {
        const actor = await workflowService.getActor(machine, {
          id: record.workflowId,
          state: redeemActiveStates,
        });

        await actor.stop(operatorId);
      });

      this.sendNotification(record.workflowId, "terminate");
    });
  }

  async sendNotification(
    workflowId: string | undefined,
    type: NotificationType
  ) {
    if (!workflowId) return;

    const record = await this.getByWorkflowId(workflowId);
    await notificationService.create(this.getNotificationEntity(record, type));
  }

  async getDocumentsZipById(id: number) {
    const record = await this.getById(id);
    const zip = new AdmZip();

    const documents = await this.getDocumentsById(id);
    for (const document of documents) {
      if (document.obsolete) continue;

      if (document.fileUploadId) {
        const file = await fileUploadService.getByIdSafe(document.fileUploadId);
        zip.addLocalFile(
          path.resolve(env.FILE_UPLOAD_DIR, document.fileUploadId),
          undefined,
          `${document.type}_${file?.name}`
        );
        continue;
      }

      if (document.ttdFileId) {
        const { url } = await ttdService.querySignedDocument(
          document.ttdFileId
        );
        const arrayBuffer = await fetch(url).then((response) =>
          response.arrayBuffer()
        );
        zip.addFile(`${document.type}.pdf`, Buffer.from(arrayBuffer));
      }
    }

    return {
      filename: `赎回_${record.identityNumber}_${record.username}.zip`,
      data: zip.toBuffer(),
    };
  }

  private async validateDocumentsSign(id: number, types: DocumentType[]) {
    const relations = await this.db
      .select()
      .from(redeemProcessDocument)
      .where(eq(redeemProcessDocument.redeemProcessId, id));

    if (!relations.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const promises = relations.map(async ({ documentId }) => {
      const { ttdFileId, type } = await documentService.getById(documentId);
      if (!ttdFileId) {
        return;
      }

      if (!type || !types.includes(type)) {
        return;
      }

      const ttdDocument = await ttdService.querySignedDocument(ttdFileId);
      if (!ttdDocument) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      if (ttdDocument.investorState !== 1) {
        throw new TRPCError({ code: "FORBIDDEN", message: "文件未签署" });
      }
    });

    await Promise.all(promises);
  }

  private async createTradeApplyDoc(
    userId: number,
    id: number,
    amount: number,
    time: Date
  ) {
    const { portfolioId } = await this.getById(id);
    const user = await userService.getById(userId);
    const portfolio = await portfolioService.getByIdInternal(portfolioId);

    const dataStr: DataStr = {
      [textLabel.基金名称]: { value: portfolio.name! },
      [textLabel.基金代码]: { value: portfolio.no! },
      [textLabel.投资者全称]: { value: user.name },
      [textLabel.投资者类型]: { value: user.classification! },
      [textLabel.证件类型]: { value: user.identityType! },
      [textLabel.证件号码]: { value: user.identityNumber },
      [textLabel.赎回份额]: { value: toCN(amount) },
      [textLabel.日期]: { value: dayjs().format("YYYY年MM月DD日") },
      [textLabel.开放日]: { value: dayjs(time).format("YYYY年MM月DD日") },
    };

    if (!!user.phone) {
      dataStr[textLabel.移动电话] = { value: user.phone };
    }

    const { fileId } = await ttdService.createDocumentForSign(
      typeCode.赎回单,
      undefined,
      dataStr
    );
    const documentId = await documentService.create({
      userId,
      type: "交易申请书",
      ttdFileId: fileId,
    });
    await this.db.insert(redeemProcessDocument).values({
      documentId,
      redeemProcessId: id,
    });
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id))
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { userId, portfolioId, status, state, date, bookDate } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const portfolioIdParsed = portfolioIdSchema.safeParse(portfolioId);
    if (portfolioIdParsed.success) {
      conditions.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      conditions.push(eq(workflowTask.model.status, statusParsed.data));
    }

    const stateParsed = z.string().safeParse(state);
    if (stateParsed.success) {
      conditions.push(eq(workflowTask.model.state, stateParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      const predicate = timestampEqDate(model.createTime, dateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    const bookDateParsed = z.date().safeParse(bookDate);
    if (bookDateParsed.success) {
      const predicate = timestampEqDate(model.time, bookDateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }

  private withOrder<T extends MySqlSelect>(query: T) {
    query.orderBy(desc(model.createTime));
    return query;
  }

  private getNotificationEntity(
    record: Awaited<ReturnType<typeof this.getByWorkflowId>>,
    type: NotificationType
  ): Parameters<typeof notificationService.create>[0] {
    const { createTime, portfolioName, userId } = record;
    const date = dayjs(createTime).format("YYYY-MM-DD");

    switch (type) {
      case "complete":
        return [
          {
            type: "reminder",
            userId: userId,
            title: `${portfolioName}预约订单审核通过`,
            content: `您申请于${date}赎回《${portfolioName}》的订单，已审核通过。`,
            key: "交易提醒",
          },
        ];
      case "reject":
        return [
          {
            type: "reminder",
            userId: userId,
            title: `${portfolioName}预约订单审核未通过`,
            content: `您申请于${date}赎回《${portfolioName}》的订单，很遗憾未能审核通过，如有疑问请联系相关负责人。`,
            key: "交易提醒",
          },
        ];
      case "terminate":
        return [
          {
            type: "reminder",
            userId: userId,
            title: `${portfolioName}预约订单中止`,
            content: `您申请于${date}赎回《${portfolioName}》的订单已中止，如有疑问请联系相关负责人。`,
            key: "交易提醒",
          },
        ];
    }
  }
}

export default new RedeemService();
