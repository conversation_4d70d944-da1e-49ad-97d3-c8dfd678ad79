import { and, count, eq } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { listInputParams } from "../model/workflowTask";
import { model, select } from "../schema/workflowTask";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";

class WorkflowTaskService extends Service {
  async getList(input: z.infer<typeof listInputParams>, userId: number) {
    let query = this.db.select(select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, userId);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withCondition(amountQuery, userId);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  private withCondition<T extends MySqlSelect>(query: T, userId: number) {
    query.where(and(eq(model.status, "active"), eq(model.assigneeId, userId)));
    return query;
  }
}

export default new WorkflowTaskService();
