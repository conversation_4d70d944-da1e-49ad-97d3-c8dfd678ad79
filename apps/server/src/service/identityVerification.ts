import { IdentityType } from "@portfolio-service/schema/userType";
import { TRPCError } from "@trpc/server";
import { and, count, eq, inArray, sql } from "drizzle-orm";
import { z } from "zod";
import {
  faceRecognitionDataSchema,
  faceRecognitionInputSchema,
  faceRecognitionUrlInputSchema,
  quartletInputSchema,
} from "../model/identityVerification";
import { insertSchema, model } from "../schema/identityValidation";
import ttdService from "../service/ttd";
import { decrypt, encrypt } from "../utils/crypto";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import { identityTypeValidationTransform } from "../utils/ttd";
import { parseJSON } from "../utils/zod";
import bankCardService from "./bankCard";
import ttdUrlService from "./ttdUrl";
import userService from "./user";

class IdentityVerificationService extends Service {
  async create(input: z.infer<typeof insertSchema>) {
    const [result] = await this.db.insert(model).values(input);
    return result.insertId;
  }

  async validatedByUserIds(
    userIds: number[]
  ): Promise<Record<number, boolean>> {
    if (!userIds.length) return {};

    const result = await this.db
      .select({
        userId: model.userId,
        validated: sql`count(*) > 0`.mapWith(Boolean),
      })
      .from(model)
      .where(inArray(model.userId, userIds))
      .groupBy(model.userId);

    return Object.fromEntries(result.map((it) => [it.userId, it.validated]));
  }

  async validatedByUserId(userId: number) {
    const [{ amount }] = await this.db
      .select({ amount: count() })
      .from(model)
      .where(and(eq(model.userId, userId), eq(model.status, true)));

    return !!amount;
  }

  async getFaceRecognitionUrl(
    userId: number,
    input: z.infer<typeof faceRecognitionUrlInputSchema>,
    sessionId: string
  ) {
    const data: z.infer<typeof faceRecognitionDataSchema> = {
      userId,
      sessionId,
    };

    const suffix = encrypt(JSON.stringify(data));
    const searchString = input.search || "";

    const redirectUrl = `${input.redirectUrl}/${suffix}${searchString}`;

    return await ttdUrlService.getFaceRecognitionUrl({ redirectUrl }, userId);
  }

  async faceRecognition(
    userId: number,
    input: z.infer<typeof faceRecognitionInputSchema>,
    sessionId: string
  ) {
    const { encrypted } = input;

    const decrypted = decrypt(encrypted);
    const data = parseJSON(decrypted, faceRecognitionDataSchema);

    if (!data || data.userId !== userId || data.sessionId !== sessionId) {
      throw new TRPCError({ code: "FORBIDDEN", message: "会话失效，请重试" });
    }

    if (!!input.save) {
      await transaction.run(async () => {
        await this.create({ userId, type: "人脸识别", status: true });
        await userService.updateUserNo(userId);
      });
    }
  }

  async quartlet(userId: number, input: z.infer<typeof quartletInputSchema>) {
    const { accountName, bankNumber, bindBankCard, branch, phone, save } =
      input;

    const user = await userService.getById(userId);

    const name = user.type === "个人" ? user.name : user.representativeName;

    const identityNumber =
      user.type === "个人"
        ? user.identityNumber
        : user.representativeIdentityNumber;

    const identityType =
      user.type === "个人"
        ? user.identityType
        : user.representativeIdentityType;

    if (input.name !== name || input.identityNumber !== identityNumber) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "请填写正确的身份信息",
      });
    }

    const { authorResult } = await ttdService.verifyIdentity("quartlet", {
      identityNumber,
      identityType:
        identityTypeValidationTransform[identityType as IdentityType],
      name,
      bankNumber,
      phone,
    });

    if (!authorResult) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "认证失败，请检查填写的信息是否有误",
      });
    }

    await transaction.run(async () => {
      if (bindBankCard) {
        await bankCardService.createSafe(userId, {
          bankNumber,
          accountName,
          branch,
          phone,
          ttdValidationStatus: authorResult,
        });
      }

      if (!!save) {
        await this.create({
          type: "四要素",
          userId,
          status: true,
          quartletFormData: JSON.stringify(input),
        });
        await userService.updateUserNo(userId);
      }
    });
  }
}

export default new IdentityVerificationService();
