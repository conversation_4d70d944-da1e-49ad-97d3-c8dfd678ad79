import { dividendMethodSchema } from "@portfolio-service/schema/dividend";
import { SQL, and, count, desc, eq, like, or, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import { model, select } from "../schema/userFollowedProducts";
import { Service } from "../utils/service";
import { mode } from "crypto-js";
import { v4 as uuidv4 } from "uuid";

class UserFollowedProducts extends Service {
  async addProducts(products: string[], identityNumber: string) {
    return this.db.transaction(async (tx) => {
      await tx
        .delete(model)
        .where(eq(model.clientIdentityNumber, identityNumber));

      if (products.length > 0) {
        await tx.insert(model).values(
          products.map((product) => ({
            productName: product,
            clientIdentityNumber: identityNumber
          }))
        );
      }

      return products;
    });
  }

  async getProducts(identityNumber: string) {
    const result = await this.db.select().from(model).where(eq(model.clientIdentityNumber, identityNumber));
    return result.map((it) => it.productName);
  }
}

export default new UserFollowedProducts();
