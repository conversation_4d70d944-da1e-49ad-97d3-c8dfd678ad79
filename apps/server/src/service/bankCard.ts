import { TRPCError } from "@trpc/server";
import { searchCardBin } from "bankcard";
import { and, count, eq, like, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  accountNameSchema,
  bankNumberSchema,
  createInputSchema,
  listInputSchema,
  userIdSchema,
} from "../model/bankCard";
import { model, select } from "../schema/bankCard";
import * as user from "../schema/user";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import ttdService from "./ttd";
import userService from "./user";

class BankCardService extends Service {
  private select = {
    ...select,
    name: user.model.name,
    identityNumber: user.model.identityNumber,
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getListByUserId(userId: number) {
    return await this.db.select().from(model).where(eq(model.userId, userId));
  }

  async createSafe(userId: number, input: z.infer<typeof createInputSchema>) {
    const { accountName, branch, phone } = input;

    const bankNumber = input.bankNumber.replace(" ", "");
    const cardBin = searchCardBin(bankNumber) || {};

    let isNew = true;

    await withMutex(["bankCard.create", userId, bankNumber], async () => {
      const records = await this.db
        .select()
        .from(model)
        .where(eq(model.userId, userId));
      if (records.some((it) => it.bankNumber === bankNumber)) {
        isNew = false;
        return;
      }

      const { name, identityNumber } = await userService.getById(userId);

      let ttdValidationStatus = input.ttdValidationStatus;
      if (ttdValidationStatus == null) {
        const { authorResult } = await ttdService.verifyIdentity("trio", {
          name,
          identityNumber,
          bankNumber,
        });
        if (!authorResult) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "银行卡认证失败，请检查信息是否有误",
          });
        }

        ttdValidationStatus = authorResult;
      }

      await this.db.insert(model).values({
        ...cardBin,
        userId,
        bankNumber,
        accountName,
        branch,
        phone,
        ttdValidationStatus,
      });
    });

    return { isNew };
  }

  async create(userId: number, input: z.infer<typeof createInputSchema>) {
    const { isNew } = await this.createSafe(userId, input);
    if (!isNew) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "该银行卡已绑定" });
    }
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query.leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { userId, accountName, bankNumber } = filter || {};

    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const accountNameParsed = accountNameSchema.min(1).safeParse(accountName);
    if (accountNameParsed.success) {
      conditions.push(like(model.accountName, `%${accountNameParsed.data}%`));
    }

    const bankNumberParsed = bankNumberSchema.min(1).safeParse(bankNumber);
    if (bankNumberParsed.success) {
      conditions.push(eq(model.bankNumber, bankNumberParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new BankCardService();
