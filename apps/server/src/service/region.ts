import { TRPCError } from "@trpc/server";
import { Service } from "../utils/service";
import * as regionSchema from "../schema/region";

class RegionService extends Service {
  async getAllRegions() {
    try {
      return await this.db.select(regionSchema.select).from(regionSchema.region);
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取地区数据失败",
        cause: error
      });
    }
  }
}

export default new RegionService();


