import { and, count, eq } from "drizzle-orm";
import { model } from "../schema/openDay";
import { dateEq } from "../utils/drizzle";
import { Service } from "../utils/service";

class OpenDayService extends Service {
  async exists(portfolioId: number, date: Date) {
    const [{ amount }] = await this.db
      .select({ amount: count() })
      .from(model)
      .where(and(eq(model.portfolioId, portfolioId), dateEq(model.date, date)));

    return !!amount;
  }

  async getListByPortfolioId(portfolioId: number) {
    const queryResult = await this.db
      .select()
      .from(model)
      .where(eq(model.portfolioId, portfolioId));

    return queryResult.map((it) => it.date);
  }
}

export default new OpenDayService();
