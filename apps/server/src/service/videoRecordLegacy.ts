import { z } from "zod";
import { Service } from "../utils/service";
import {
  listInputSchema,
  userIdSchema,
  portfolioIdSchema,
  identityNumberSchema,
} from "../model/videoRecordLegacy";
import { select, model } from "../schema/videoRecordLegacy";
import * as user from "../schema/user";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { and, eq, SQL } from "drizzle-orm";

class VideoRecordLegacyService extends Service {
  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(select).from(model).$dynamic();
    query = this.withCondition(query, input);

    return await query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof listInputSchema>
  ) {
    if (!input) {
      return query;
    }

    const predicates: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(input.userId);
    if (userIdParsed.success) {
      predicates.push(eq(model.userId, userIdParsed.data));
    }

    const identityNumberParsed = identityNumberSchema.safeParse(
      input.identityNumber
    );
    if (identityNumberParsed.success) {
      query.leftJoin(user.model, eq(model.userId, user.model.id));
      predicates.push(eq(user.model.identityNumber, identityNumberParsed.data));
    }

    const portfolioIdParsed = portfolioIdSchema.safeParse(input.portfolioId);
    if (portfolioIdParsed.success) {
      predicates.push(eq(model.portfolioId, portfolioIdParsed.data));
    }

    if (!!predicates.length) {
      query.where(and(...predicates));
    }

    return query;
  }
}

export default new VideoRecordLegacyService();
