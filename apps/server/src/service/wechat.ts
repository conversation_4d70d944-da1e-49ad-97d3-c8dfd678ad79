import { TRPCError } from "@trpc/server";
import crypto from "crypto";
import { v4 as uuid } from "uuid";
import { wechatJsapiTicketStorage } from "../store/wechat";

class WechatService {
  getSignature(url: string) {
    const jsapiTicket = wechatJsapiTicketStorage.value;
    if (!jsapiTicket) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const nonce = uuid();
    const timestamp = Math.floor(new Date().getTime() / 1000);

    const params = new URLSearchParams();
    params.set("jsapi_ticket", jsapiTicket);
    params.set("noncestr", nonce);
    params.set("timestamp", timestamp.toString());
    params.set("url", url);

    const signature = this.sha1(decodeURIComponent(params.toString()));

    return {
      nonce,
      timestamp,
      signature,
    };
  }

  private sha1(value: string) {
    return crypto.createHash("sha1").update(value).digest("hex");
  }
}

export default new WechatService();
