import { and, eq } from "drizzle-orm";
import { z } from "zod";
import {
  createSupplementToCheckAltInputSchema,
  createSupplementToCheckInputSchema,
} from "../model/supplementProcess";
import { altModel, model } from "../schema/supplementToCheck";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import subscribeProcessService from "./subscribeProcess";
import supplementProcessService from "./supplementProcess";
import userService from "./user";

class SupplementToCheckService extends Service {
  async getList() {
    return await this.db.select().from(model);
  }

  async getAltList() {
    return await this.db.select().from(altModel);
  }

  async create(input: z.infer<typeof createSupplementToCheckInputSchema>) {
    const { operatorId, portfolioId } = input;
    const processes =
      await subscribeProcessService.getByPortfolioId(portfolioId);

    if (!processes.length) {
      return { amount: 0 };
    }

    await this.db
      .insert(model)
      .values(processes.map(({ id }) => ({ operatorId, processId: id })));

    supplementProcessService.createFromToCheck();
    return { amount: processes.length };
  }

  async createAlt(
    input: z.infer<typeof createSupplementToCheckAltInputSchema>
  ) {
    const { operatorId, portfolioId, productNumber, identityNumbers } = input;

    const users = await userService.getListByIdentityNumbers(identityNumbers);
    const userIds = users.map(({ id }) => id);

    if (!userIds.length) {
      return { amount: 0 };
    }

    await transaction.run(async (tx) => {
      for (const userId of userIds) {
        await tx
          .insert(altModel)
          .values({ operatorId, portfolioId, productNumber, userId })
          .onDuplicateKeyUpdate({ set: { productNumber, operatorId } });
      }
    });

    supplementProcessService.createFromToCheckAlt();
    return { amount: userIds.length };
  }

  async deleteByProcessId(processId: number) {
    await this.db.delete(model).where(eq(model.processId, processId));
  }

  async deleteAltById(input: { userId: number; portfolioId: number }) {
    await this.db
      .delete(altModel)
      .where(
        and(
          eq(altModel.userId, input.userId),
          eq(altModel.portfolioId, input.portfolioId)
        )
      );
  }
}

export default new SupplementToCheckService();
