import { createHash } from "crypto";
import { eq, sql } from "drizzle-orm";
import { UploadedFile } from "express-fileupload";
import { first } from "lodash";
import path from "path";
import env from "../config/env";
import * as fileUpload from "../schema/fileUpload";
import { Service } from "../utils/service";

const md5 = (file: UploadedFile) => {
  const hash = createHash("md5");
  hash.update(Buffer.concat([file.data, Buffer.from(file.name)]));
  return hash.digest("hex");
};

class FileUploadService extends Service {
  async getByIdSafe(id: string) {
    const result = await this.db
      .select()
      .from(fileUpload.model)
      .where(eq(fileUpload.model.id, id));

    return first(result);
  }

  async upload(file: UploadedFile) {
    await this.save(file);

    const id = md5(file);

    await this.db
      .insert(fileUpload.model)
      .values({ id: md5(file), name: file.name })
      .onDuplicateKeyUpdate({
        set: { name: file.name, createTime: sql`current_timestamp()` },
      });

    return id;
  }

  private save(file: UploadedFile) {
    return new Promise<void>((resolve, reject) => {
      file.mv(path.resolve(env.FILE_UPLOAD_DIR, md5(file)), (err) => {
        if (err) {
          return reject(err);
        }

        resolve();
      });
    });
  }
}

export default new FileUploadService();
