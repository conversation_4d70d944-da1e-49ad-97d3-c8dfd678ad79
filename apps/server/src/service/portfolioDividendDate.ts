import { eq } from "drizzle-orm";
import { first } from "lodash";
import { model } from "../schema/portfolioDividendDate";
import { Service } from "../utils/service";

class PortofolioDividendDateService extends Service {
  async getByPortfolioId(portfolioId: number) {
    const result = await this.db
      .select()
      .from(model)
      .where(eq(model.portfolioId, portfolioId));

    return first(result);
  }
}

export default new PortofolioDividendDateService();
