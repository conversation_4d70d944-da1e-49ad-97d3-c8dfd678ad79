import { businessTypeSchema } from "@portfolio-service/schema/trade";
import {
  SQL,
  and,
  count,
  desc,
  eq,
  isNotNull,
  like,
  notInArray,
  or,
  sql,
  inArray,
} from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { inputSchema, tradeCalculatorFilter } from "../model/trade";
import * as trade from "../schema/trade";
import * as tradeConfirm from "../schema/tradeConfirm";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import * as tradeDate from "../schema/tradeDate";
class TradeService extends Service {
  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    const select = {
      portfolioId: trade.model.portfolioId,
      portfolioName: trade.model.portfolioName,
      portfolioNo: trade.model.portfolioNo,
      agency: trade.model.portfolioName,
      businessType: trade.model.businessType,
      applyDate: trade.model.applyDate,
      applyBalance: trade.model.applyBalance,
      applyShare: trade.model.applyShare,
      confDate: trade.model.confDate,
      confBalance: trade.model.confBalance,
      confShare: trade.model.confShare,
      transactionPrice: trade.model.transactionPrice,
      transactionFee: trade.model.transactionFee,
      confStatus: trade.model.confStatus,
      transferFee: trade.model.transferFee,
    };

    let query = this.db.select(select).from(trade.model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input, identityNumber);
    query = query.orderBy(desc(trade.model.applyDate));

    let amountQuery = this.db
      .select({ value: count() })
      .from(trade.model)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input, identityNumber);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async getNew() {
    let query = this.db.select().from(trade.model).$dynamic();

    const subQuery = sql`${this.db.select().from(tradeConfirm.model)}`;
    query.where(
      and(
        isNotNull(trade.model.confNumber),
        notInArray(trade.model.confNumber, subQuery)
      )
    );

    return await query;
  }

  async getLiquidateDatesByPortfolioId(portfolioId: number) {
    const result = await this.db
      .selectDistinct({ liquidateDate: trade.model.applyDate })
      .from(trade.model)
      .where(eq(trade.model.portfolioId, portfolioId));

    return result.map((it) => it.liquidateDate).filter(Boolean) as string[];
  }

  async getApplyDatesByPortfolioId(portfolioId: number) {
    const result = await this.db
      .selectDistinct({ applyDate: trade.model.applyDate })
      .from(trade.model)
      .where(eq(trade.model.portfolioId, portfolioId));

    return result.map((it) => it.applyDate).filter(Boolean) as string[];
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>,
    identityNumber: string
  ) {
    const conditions: SQL[] = [
      eq(trade.model.clientIdentityNumber, identityNumber),
    ];

    const portfolioParsed = z.string().safeParse(input?.portfolio);
    if (portfolioParsed.success) {
      const value = `%${portfolioParsed.data}%`;
      const predicate = or(
        like(trade.model.portfolioName, value),
        like(trade.model.portfolioNo, value)
      );

      if (predicate) {
        conditions.push(predicate);
      }
    }

    const businessTypeParsed = businessTypeSchema.safeParse(
      input?.businessType
    );
    if (businessTypeParsed.success) {
      conditions.push(eq(trade.model.businessType, businessTypeParsed.data));
    }

    query.where(and(...conditions));

    return query;
  }

  async getTradeDate() {
    const result = await this.db.select().from(tradeDate.model);
    return result;
  }

  async getApplyDatesByProduct(identityNumber: string, productName: string) {
    const result = await this.db
      .selectDistinct({
        applyDate: trade.model.applyDate,
      })
      .from(trade.model)
      .where(
        and(
          eq(trade.model.portfolioName, productName),
          eq(trade.model.clientIdentityNumber, identityNumber),
          inArray(trade.model.businessType, ["赎回", "强制赎回", "基金清盘"])
        )
      )
      .prepare()
      .execute();

    return result.map((item) => item.applyDate).filter(Boolean) as string[];
  }

  async search(
    identityNumber: string,
    input: z.infer<typeof tradeCalculatorFilter>
  ) {
    const select = {
      portfolioId: trade.model.portfolioId,
      portfolioName: trade.model.portfolioName,
      portfolioNo: trade.model.portfolioNo,
      agency: trade.model.portfolioName,
      businessType: trade.model.businessType,
      applyDate: trade.model.applyDate,
      applyBalance: trade.model.applyBalance,
      applyShare: trade.model.applyShare,
      confDate: trade.model.confDate,
      confBalance: trade.model.confBalance,
      confShare: trade.model.confShare,
      transactionPrice: trade.model.transactionPrice,
      transactionFee: trade.model.transactionFee,
      confStatus: trade.model.confStatus,
      transferFee: trade.model.transferFee,
    };

    const conditions: SQL[] = [
      eq(trade.model.clientIdentityNumber, identityNumber),
      inArray(trade.model.businessType, ["赎回", "强制赎回", "基金清盘"]),
    ];

    if (input.productName) {
      conditions.push(eq(trade.model.portfolioName, input.productName));
    }

    if (input.applyDate) {
      conditions.push(eq(trade.model.applyDate, input.applyDate));
    }

    const result = await this.db
      .select(select)
      .from(trade.model)
      .where(and(...conditions))
      .limit(1)
      .prepare()
      .execute();

    return result[0] || null;
  }

  async getTradesByIdentityNumber(identityNumber: string) {
    return await this.db
      .select()
      .from(trade.model)
      .where(eq(trade.model.clientIdentityNumber, identityNumber))
      .execute();
  }
}

export default new TradeService();
