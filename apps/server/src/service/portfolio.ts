import { TRPCError } from "@trpc/server";
import { and, count, eq, gt, inArray, like, or, SQL, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { Dictionary, keyBy } from "lodash";
import { z } from "zod";
import { inputSchema, PortfolioAccess } from "../model/portfolio";
import * as netValue from "../schema/netValue";
import * as disclosure from "../schema/netValueDisclosure";
import * as portfolio from "../schema/portfolio";
import * as userFollowedProducts from "../schema/userFollowedProducts";
import { model } from "../schema/portfolio";
import * as complianceConf from "../schema/portfolioComplianceConf";
import * as share from "../schema/share";
import { convertDouble } from "../utils/db";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import complianceService from "./portfolioComplianceConf";

const { openDay, ...byIdSelect } = portfolio.select;

class PortfolioService extends Service {
  private select = {
    id: model.id,
    name: model.name,
    no: model.no,
    netValue: convertDouble(netValue.model.netValue),
    netValueDate: netValue.model.date,
    cumNetValue: convertDouble(netValue.model.cumNetValue),
  };

  async getAll() {
    return await this.db.select(portfolio.select).from(portfolio.model);
  }

  async getDetailList(
    input: z.infer<typeof inputSchema>,
    identityNumber: string
  ) {
    if (input?.category == "sub") {
      const followedProducts = await this.db
        .select({ productName: userFollowedProducts.model.productName })
        .from(userFollowedProducts.model)
        .where(
          eq(userFollowedProducts.model.clientIdentityNumber, identityNumber)
        );

      if (followedProducts.length === 0) {
        return { items: [], amount: 0 };
      }

      let query = this.db
        .select(this.select)
        .from(portfolio.model)
        .where(
          inArray(
            portfolio.model.name,
            followedProducts.map((p) => p.productName) as string[]
          )
        )
        .$dynamic();

      query = withPagination(query, input);
      query = this.withJoin(query, identityNumber);
      // query = this.withCondition(query, input, identityNumber);

      let amountQuery = this.db
        .select({ value: count() })
        .from(portfolio.model)
        .where(
          inArray(
            portfolio.model.name,
            followedProducts.map((p) => p.productName) as string[]
          )
        )
        .$dynamic();

      amountQuery = this.withJoin(amountQuery, identityNumber);
      // amountQuery = this.withCondition(amountQuery, input, identityNumber);

      const [queryResult, amountQueryResult] = await Promise.all([
        query,
        amountQuery,
      ]);

      return { items: queryResult, amount: amountQueryResult[0].value };
    } else {
      let query = this.db.select(this.select).from(portfolio.model).$dynamic();
      query = withPagination(query, input);
      query = this.withJoin(query, identityNumber);
      query = this.withCondition(query, input, identityNumber);
      let amountQuery = this.db
        .select({ value: count() })
        .from(portfolio.model)
        .$dynamic();
      amountQuery = this.withJoin(amountQuery, identityNumber);
      amountQuery = this.withCondition(amountQuery, input, identityNumber);

      const [queryResult, amountQueryResult] = await Promise.all([
        query,
        amountQuery,
      ]);

      return { items: queryResult, amount: amountQueryResult[0].value };
    }
  }

  async getBaseList() {
    return await this.db.select({ name: model.name, no: model.no }).from(model);
  }

  async getByIdInternal(id: number) {
    const [result] = await this.db
      .select(portfolio.select)
      .from(model)
      .where(eq(model.id, id));
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getById(id: number, identityNumber: string) {
    const hasAccess = await this.hasPortfolioAccess({ id, identityNumber });
    if (!hasAccess) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    const [result] = await this.db
      .select(byIdSelect)
      .from(portfolio.model)
      .where(eq(portfolio.model.id, id));

    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return complianceService.sanitizePortfolioFields(result);
  }

  async getByIdNoAccess(id: number) {
    const [result] = await this.db
      .select(byIdSelect)
      .from(portfolio.model)
      .where(eq(portfolio.model.id, id));

    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return complianceService.sanitizePortfolioFields(result);
  }

  async getParentIdByIdSafe(id: number) {
    const query = this.db
      .select({ id: portfolio.model.id })
      .from(portfolio.model)
      .where(
        or(
          eq(portfolio.model.classAShareId, id),
          eq(portfolio.model.classBShareId, id),
          eq(portfolio.model.classCShareId, id),
          eq(portfolio.model.classDShareId, id)
        )
      );

    const [result] = await query;
    return result?.id;
  }

  async getPortfoliosAccess(input: { ids: number[]; identityNumber: string }) {
    const { ids, identityNumber } = input;
    if (!ids.length) {
      return {};
    }

    const complianceConfs = await complianceService.getByPortfolioIds(ids);
    const confDict = keyBy(complianceConfs, (conf) => conf.portfolioId);

    const shares = await this.db
      .select({
        portfolioId: share.model.portfolioId,
        realShares: share.model.realShares,
      })
      .from(share.model)
      .where(
        and(
          eq(share.model.clientIdentityNumber, identityNumber),
          inArray(share.model.portfolioId, ids)
        )
      );
    const shareDict = keyBy(
      shares.filter((share) => share.portfolioId != null),
      (share) => share.portfolioId
    ) as Dictionary<(typeof shares)[number]>;

    const result: Record<number, PortfolioAccess> = {};

    for (const id of ids) {
      const conf = confDict[id];
      if (!conf) continue;

      const share = shareDict[id];
      const hold = !!share;
      const holding = (share?.realShares || 0) > 0;

      const accessDict: Record<
        z.infer<typeof complianceConf.visibilitySchema>,
        boolean
      > = { all: true, hold, holding };

      result[id] = {
        access: accessDict[conf.visibility],
        reportAccess: accessDict[conf.reportVisibility],
        holding: accessDict.holding,
      };
    }

    return result;
  }

  async getPortfolioAccess(input: { id: number; identityNumber: string }) {
    const { id, identityNumber } = input;

    const result = await this.getPortfoliosAccess({
      ids: [id],
      identityNumber,
    });

    return result[id] as PortfolioAccess | undefined;
  }

  async hasPortfolioAccess(input: { id: number; identityNumber: string }) {
    const result = await this.getPortfolioAccess(input);
    return result?.access || false;
  }

  private withJoin<T extends MySqlSelect>(query: T, identityNumber: string) {
    query
      .leftJoin(
        share.model,
        and(
          eq(portfolio.model.id, share.model.portfolioId),
          eq(share.model.clientIdentityNumber, identityNumber)
        )
      )
      .leftJoin(
        complianceConf.model,
        eq(portfolio.model.id, complianceConf.model.portfolioId)
      )
      .leftJoin(
        disclosure.shareModel,
        eq(portfolio.model.id, disclosure.shareModel.portfolioId)
      )
      .leftJoin(
        netValue.model,
        and(
          eq(disclosure.shareModel.portfolioId, netValue.model.portfolioId),
          eq(disclosure.shareModel.netValueDate, netValue.model.date)
        )
      );

    return query;
  }

  private withCondition = <T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof inputSchema>,
    identityNumber: string
  ) => {
    const complianceCondition = or(
      inArray(complianceConf.model.visibility, ["all"]),
      and(
        eq(complianceConf.model.visibility, "holding"),
        gt(share.model.realShares, 0)
      ),
      and(
        eq(complianceConf.model.visibility, "hold"),
        eq(share.model.clientIdentityNumber, identityNumber)
      )
    );

    if (!complianceCondition) {
      query.where(sql`false`);
      return query;
    }

    const conditions: SQL[] = [complianceCondition];

    const portfolioParsed = z.string().safeParse(filter?.portfolio);
    if (portfolioParsed.success) {
      const value = `%${portfolioParsed.data}%`;
      const predicate = or(
        like(portfolio.model.name, value),
        like(portfolio.model.no, value)
      );
      if (predicate) {
        conditions.push(predicate);
      }
    }

    query.where(and(...conditions));

    return query;
  };
}

export default new PortfolioService();
