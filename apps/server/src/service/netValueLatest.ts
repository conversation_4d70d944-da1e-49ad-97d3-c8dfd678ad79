import { eq } from "drizzle-orm";
import { first } from "lodash";
import { latestModel } from "../schema/netValue";
import { Service } from "../utils/service";

class NetValueLatestService extends Service {
  async getByPortfolioId(portfolioId: number) {
    const result = await this.db
      .select()
      .from(latestModel)
      .where(eq(latestModel.portfolioId, portfolioId));

    return first(result);
  }
}

export default new NetValueLatestService();
