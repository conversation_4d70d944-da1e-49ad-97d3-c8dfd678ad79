import { datePattern } from "@portfolio-service/schema/utils";
import { TRPCError } from "@trpc/server";
import dayjs, { Dayjs } from "dayjs";
import { count, eq, max } from "drizzle-orm";
import { first } from "lodash";
import { model, select, shareModel } from "../schema/netValueDisclosure";
import * as portfolio from "../schema/portfolio";
import { tradeDateStorage } from "../store/tradeDate";
import { Service } from "../utils/service";
import {
  getLatestTradeDate,
  subtractMany,
  subtractOne,
} from "../utils/tradeDate";
import disclosureConfService from "./netValueDisclosureConf";
import portfolioService from "./portfolio";
import portfolioDividendDateService from "./portfolioDividendDate";

class NetValueDisclosureService extends Service {
  private select = {
    ...select,
    portfolioName: portfolio.model.name,
    portfolioNo: portfolio.model.no,
  };

  async getList() {
    const query = this.db.select(this.select).from(model).$dynamic();
    query.leftJoin(portfolio.model, eq(model.portfolioId, portfolio.model.id));

    return await query;
  }

  async getByPortfolioIdSafe(portfolioId: number) {
    const result = await this.db
      .select()
      .from(model)
      .where(eq(model.portfolioId, portfolioId));
    return first(result);
  }

  async getByPortfolioId(portfolioId: number) {
    const result = await this.getByPortfolioIdSafe(portfolioId);
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async update(portfolioId: number) {
    const now = dayjs();
    const today = getLatestTradeDate(now);

    let offset = await this.getDividendDateOffset(portfolioId, today);
    if (!offset) offset = subtractMany(today, now.hour() >= 14 ? 2 : 3);

    const date = await disclosureConfService.getLatestDateByPortfolioId(
      portfolioId,
      offset
    );
    if (!date) {
      await this.delete(portfolioId);
      return;
    }

    const record = await this.getByPortfolioIdSafe(portfolioId);
    if (!record) {
      await this.db
        .insert(model)
        .values({ portfolioId, netValueDate: date.toDate() });
      return;
    }

    if (!date.isAfter(dayjs(record.netValueDate), "day")) return;

    await this.db
      .update(model)
      .set({ netValueDate: date.toDate() })
      .where(eq(model.portfolioId, portfolioId));
  }

  async delete(portfolioId: number) {
    await this.db.delete(model).where(eq(model.portfolioId, portfolioId));
  }

  async refresh() {
    const portfolios = await portfolioService.getAll();

    for (const portfolio of portfolios) {
      try {
        await this.update(portfolio.id);
      } catch (e) {
        console.error(e);
      }
    }
  }

  async setup() {
    const [result] = await this.db.select({ amount: count() }).from(model);
    if (!!result.amount) return;

    await this.refresh();
  }

  async getLatestShareDate(portfolioId: number) {
    const [{ date }] = await this.db
      .select({ date: max(shareModel.netValueDate) })
      .from(shareModel)
      .where(eq(shareModel.portfolioId, portfolioId));

    return date;
  }

  async getDividendDateOffset(portfolioId: number, now: Dayjs) {
    const dividendDates =
      await portfolioDividendDateService.getByPortfolioId(portfolioId);
    const { dividendConfirmDate, dividendDate } = dividendDates || {};
    if (!dividendConfirmDate) return;

    const confirmDate = dayjs(dividendConfirmDate);

    let confirmDateDiff = 0;
    let count = 0;
    const date = now.startOf("day");

    if (date.isAfter(confirmDate, "day")) {
      let current = date;

      while (current.isAfter(confirmDate) && count < 10000) {
        if (tradeDateStorage.value.has(current.format(datePattern))) {
          confirmDateDiff++;
        }

        current = current.subtract(1, "day");
        count++;
      }
    } else if (confirmDate.isAfter(date, "day")) {
      let current = confirmDate;

      while (current.isAfter(date) && count < 10000) {
        if (tradeDateStorage.value.has(current.format(datePattern))) {
          confirmDateDiff--;
        }

        current = current.subtract(1, "day");
        count++;
      }
    }
    if (now.hour() < 14) confirmDateDiff--;
    if (confirmDateDiff < -3 || confirmDateDiff >= 1) return;
    if (!!dividendDate) return subtractOne(dayjs(dividendDate));

    const subtract = 3;
    const offset =
      now.hour() >= 14
        ? now.startOf("day")
        : now.subtract(1, "day").startOf("day");

    return subtractMany(offset, subtract);
  }
}

export default new NetValueDisclosureService();
