import { and, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  createInputSchema,
  deleteInputSchema,
  filterSchema,
} from "../model/signCheck";
import { model, selectSchema } from "../schema/signCheck";
import { withMutex } from "../utils/lock";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import appropriatenessProcessService from "./appropriatenessProcess";
import redeemProcessService from "./redeemProcess";
import riskTestProcessService from "./riskTestProcess";
import subscribeProcessService from "./subscribeProcess";
import supplementProcessService from "./supplementProcess";

class SignCheckService extends Service {
  async getList(userId?: number, input?: z.infer<typeof filterSchema>) {
    let query = this.db.select().from(model).$dynamic();
    query = this.withCondition(query, input, userId);

    return await query;
  }

  async create(userId: number, input: z.infer<typeof createInputSchema>) {
    await this.db
      .insert(model)
      .values({ ...input, userId })
      .onDuplicateKeyUpdate({ set: { userId } });
  }

  async delete(input: z.infer<typeof deleteInputSchema>) {
    const { entityId, type } = input;

    await this.db
      .delete(model)
      .where(and(eq(model.entityId, entityId), eq(model.type, type)));
  }

  async check(userId?: number, input?: z.infer<typeof filterSchema>) {
    await withMutex("signCheckService.check", async () => {
      const records = await this.getList(userId, input);

      for (const item of records) {
        await withMutex(["signCheckService.check", item], async () => {
          try {
            await transaction.run(async () => {
              switch (item.type) {
                case "appropriateness":
                  await this.checkAppropriateness(item);
                  break;
                case "subscribe":
                  await this.checkSubscribe(item);
                  break;
                case "redeem":
                  await this.checkRedeem(item);
                  break;
                case "supplement":
                  await this.checkSupplement(item);
                  break;
                case "riskTest":
                  await this.checkRiskTest(item);
                  break;
              }
              await this.delete(item);
            });
          } catch (e) {
            console.error(e);
          }
        });
      }
    });
  }

  private async checkAppropriateness(data: z.infer<typeof selectSchema>) {
    const { entityId, userId } = data;

    const record = await appropriatenessProcessService.getByIdSafe(
      entityId,
      userId
    );
    if (record?.status !== "active") return;

    const { id, state } = record;
    if (!["documentsSign", "documentsSignRedo"].includes(state)) return;

    await appropriatenessProcessService.documentsSign(userId, { id });
  }

  private async checkSubscribe(data: z.infer<typeof selectSchema>) {
    const { entityId, userId } = data;

    const record = await subscribeProcessService.getByIdSafe(entityId, userId);
    if (record?.status !== "active") return;

    const { id, state } = record;
    if (["ttdOrder", "ttdOrderRedo"].includes(state)) {
      await subscribeProcessService.documentsSign(userId, { id });
    } else if (["contractSign", "contractSignRedo"].includes(state)) {
      await subscribeProcessService.contractSign(userId, { id });
    }
  }

  private async checkRedeem(data: z.infer<typeof selectSchema>) {
    const { entityId, userId } = data;

    const record = await redeemProcessService.getByIdSafe(entityId, userId);
    if (record?.status !== "active") return;

    const { id, state } = record;
    if (!["documentsSign", "documentsSignRedo"].includes(state)) return;

    await redeemProcessService.documentsSign(userId, { id });
  }

  private async checkSupplement(data: z.infer<typeof selectSchema>) {
    const { entityId, userId } = data;

    const record = await supplementProcessService.getByIdSafe(entityId, userId);
    if (record?.status !== "active") return;

    const { id, state } = record;
    if (!["supplementSign", "supplementSignRedo"].includes(state)) return;

    await supplementProcessService.supplementSign(userId, { id });
  }

  private async checkRiskTest(data: z.infer<typeof selectSchema>) {
    const { entityId, userId } = data;

    const record = await riskTestProcessService.getByIdSafe(entityId, userId);
    if (record?.status !== "active") return;

    const { id, state } = record;
    if (state !== "documentsSign") return;

    await riskTestProcessService.documentsSign(userId, { id });
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof filterSchema>,
    userId?: number
  ) {
    const conditions: SQL[] = [];

    const typeParsed = selectSchema.shape.type.safeParse(filter?.type);
    if (typeParsed.success) {
      conditions.push(eq(model.type, typeParsed.data));
    }

    const userIdParsed = selectSchema.shape.userId.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    if (!conditions.length) return query;

    return query.where(and(...conditions));
  }
}

export default new SignCheckService();
