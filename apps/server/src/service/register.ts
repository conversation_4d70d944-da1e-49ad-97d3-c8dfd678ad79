import { TRPCError } from "@trpc/server";
import crypto from "crypto";
import { SQL, and, count, eq, like } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { isEqual } from "lodash";
import { z } from "zod";
import {
  filterSchema,
  listInputSchema,
  registerCodeValidateInputSchema,
  registerIdentityValidateInputSchema,
  registerInputSchema,
  sendCodeInputSchema,
} from "../model/register";
import * as registerUser from "../schema/registerUser";
import { model } from "../schema/user";
import authService from "../service/auth";
import { isDevEnv } from "../utils/dev";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import emailService from "./email";
import smsService from "./sms";

class RegisterService extends Service {
  async getByIdSafe(id: number) {
    const [result] = await this.db
      .select()
      .from(registerUser.model)
      .where(eq(registerUser.model.id, id));

    return result;
  }

  async getById(id: number) {
    const record = await this.getByIdSafe(id);
    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select().from(registerUser.model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);

    let amountQuery = this.db
      .select({ value: count() })
      .from(registerUser.model)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async generateCode(id: number) {
    const record = await this.getById(id);
    const code = crypto.randomBytes(4).toString("hex");

    await this.db
      .update(registerUser.model)
      .set({ validationCode: code })
      .where(eq(registerUser.model.id, record.id));
  }

  async redoIdentityVerification(id: number) {
    const record = await this.getById(id);

    await this.db
      .update(registerUser.model)
      .set({ redoIdentityVerification: true })
      .where(eq(registerUser.model.id, record.id));
  }

  async registerIdentityValidate(
    input: z.infer<typeof registerIdentityValidateInputSchema>
  ) {
    return await withMutex(
      ["auth.registerIdentityValidate", input.identityNumber],
      async () => {
        const [existingUser] = await this.db
          .select({ id: model.id })
          .from(model)
          .where(eq(model.identityNumber, input.identityNumber));

        if (existingUser?.id != null) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "该证件号码已注册",
          });
        }

        const insert: registerUser.RegisterUserInsert = {
          name: input.name,
          userType: input.userType,
          identityType: input.identityType,
          identityNumber: input.identityNumber,
        };

        if (input.userType === "机构") {
          insert.representativeName = input.representativeName;
          insert.representativeIdentityType = input.representativeIdentityType;
          insert.representativeIdentityNumber =
            input.representativeIdentityNumber;
        }

        const verificationResult = await authService.verifyIdentity(input);
        if (!verificationResult) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "身份认证失败，请检查信息是否有误",
          });
        }

        insert.identityVerificationStatus = verificationResult;

        const { identityNumber, ...update } = insert;

        await this.db
          .insert(registerUser.model)
          .values(insert)
          .onDuplicateKeyUpdate({
            set: { ...update, redoIdentityVerification: false },
          });

        const [record] = await this.db
          .select()
          .from(registerUser.model)
          .where(eq(registerUser.model.identityNumber, identityNumber));
        return record;
      }
    );
  }

  async registerCodeValidate(
    input: z.infer<typeof registerCodeValidateInputSchema>,
    user: registerUser.RegisterUser
  ) {
    if (!isDevEnv()) {
      const [record] = await this.db
        .select({ code: registerUser.model.validationCode })
        .from(registerUser.model)
        .where(eq(registerUser.model.id, user.id));

      if (record?.code !== input.code) {
        throw new TRPCError({ code: "FORBIDDEN", message: "关联码错误" });
      }
    }

    await this.db
      .update(registerUser.model)
      .set({ validationStatus: true })
      .where(eq(registerUser.model.id, user.id));
  }

  async register(
    input: z.infer<typeof registerInputSchema>,
    validation: z.infer<typeof registerInputSchema> | undefined,
    user: registerUser.RegisterUser
  ) {
    if (!validation) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "请先获取验证码" });
    }

    if (input.type === "phone") {
      const [{ amount }] = await this.db
        .select({ amount: count() })
        .from(model)
        .where(eq(model.phone, input.phone));
      if (!!amount) {
        throw new TRPCError({ code: "FORBIDDEN", message: "该手机号码已注册" });
      }
    } else if (input.type === "email") {
      const [{ amount }] = await this.db
        .select({ amount: count() })
        .from(model)
        .where(eq(model.email, input.email));
      if (!!amount) {
        throw new TRPCError({ code: "FORBIDDEN", message: "该邮件地址已注册" });
      }
    }

    if (!isDevEnv()) {
      if (!isEqual(input, validation)) {
        throw new TRPCError({ code: "FORBIDDEN", message: "验证码错误" });
      }
    }

    const [record] = await this.db
      .select()
      .from(registerUser.model)
      .where(eq(registerUser.model.id, user.id));
    if (!record) {
      throw new TRPCError({ code: "BAD_REQUEST" });
    }

    const ttdUserNumber = await authService.createTtdInvestor(user);

    const [result] = await this.db.insert(model).values({
      name: record.name,
      type: record.userType,
      identityNumber: record.identityNumber,
      identityType: record.identityType,
      identityVerificationStatus: record.identityVerificationStatus,
      representativeName: record.representativeName,
      representativeIdentityType: record.representativeIdentityType,
      representativeIdentityNumber: record.representativeIdentityNumber,
      phone: input.type === "phone" ? input.phone : undefined,
      email: input.type === "email" ? input.email : undefined,
      ttdUserNumber,
    });

    const [newUser] = await this.db
      .select()
      .from(model)
      .where(eq(model.id, result.insertId));

    return newUser;
  }

  async sendCode(input: z.infer<typeof sendCodeInputSchema>) {
    if (isDevEnv()) return "123456";

    if (input.type === "phone") {
      return await smsService.getVerifier(input.phone);
    } else if (input.type === "email") {
      return await emailService.getVerifier(input.email);
    }

    throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) {
      return query;
    }

    const conditions: SQL[] = [];

    const userTypeParsed = filterSchema.shape.userType.safeParse(
      filter.userType
    );
    if (userTypeParsed.success) {
      conditions.push(eq(registerUser.model.userType, userTypeParsed.data));
    }

    const nameParsed = filterSchema.shape.name.safeParse(filter.name);
    if (nameParsed.success) {
      conditions.push(like(registerUser.model.name, `%${nameParsed.data}%`));
    }

    const identityNumberParsed = filterSchema.shape.identityNumber.safeParse(
      filter.identityNumber
    );
    if (identityNumberParsed.success) {
      conditions.push(
        eq(registerUser.model.identityNumber, identityNumberParsed.data)
      );
    }

    const identityTypeParsed = filterSchema.shape.identityType.safeParse(
      filter.identityType
    );
    if (identityTypeParsed.success) {
      conditions.push(
        eq(registerUser.model.identityType, identityTypeParsed.data)
      );
    }

    const validationStatusParsed =
      filterSchema.shape.validationStatus.safeParse(filter.validationStatus);
    if (validationStatusParsed.success) {
      conditions.push(
        eq(registerUser.model.validationStatus, validationStatusParsed.data)
      );
    }

    if (conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new RegisterService();
