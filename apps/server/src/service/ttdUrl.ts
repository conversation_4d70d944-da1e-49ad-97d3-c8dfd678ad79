import userService from "./user";
import { Service } from "../utils/service";
import {
  getSignUrl,
  getBatchSignUrl,
  getFaceRecognitionUrl,
  getOrderSignUrl,
} from "../utils/ttdUrl";
import {
  batchSignUrlInputSchema,
  faceRecognitionUrlInputSchema,
  orderSignUrlInputSchema,
  signUrlInputSchema,
} from "../model/ttdUrl";
import ttdService from "./ttd";
import { z } from "zod";
import { TRPCError } from "@trpc/server";

class TtdUrlService extends Service {
  async getSignUrl(input: z.infer<typeof signUrlInputSchema>, userId: number) {
    const { fileId, redirectUrl, password } = input;
    const userNo = await this.verifySignPassword(userId, password);

    const url = getSignUrl({ fileId, userNo, redirectUrl });
    return { url };
  }

  async getBatchSignUrl(
    input: z.infer<typeof batchSignUrlInputSchema>,
    userId: number
  ) {
    const { redirectUrl, fileIds, orderNumber, password } = input;
    const userNo = await this.verifySignPassword(userId, password);

    const url = getBatchSignUrl({ fileIds, orderNumber, redirectUrl, userNo });
    return { url };
  }

  async getOrderSignUrl(
    input: z.infer<typeof orderSignUrlInputSchema>,
    userId: number
  ) {
    const { password, ...rest } = input;
    await this.verifySignPassword(userId, password);

    const url = getOrderSignUrl(rest);
    return { url };
  }

  async getFaceRecognitionUrl(
    input: z.infer<typeof faceRecognitionUrlInputSchema>,
    userId: number
  ) {
    const { redirectUrl } = input;
    const { name, identityNumber } = await userService.getById(userId);

    const url = getFaceRecognitionUrl({ redirectUrl, name, identityNumber });
    return { url };
  }

  private async verifySignPassword(userId: number, password: string) {
    const userNo = await userService.getTtdUserNoByIdSafe(userId);
    if (!userNo) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "获取链接失败，请先完成实名认证",
      });
    }

    const { signPassword } = await ttdService.queryUserInfo(userNo);
    if (!signPassword) {
      throw new TRPCError({ code: "FORBIDDEN", message: "未设置签署密码" });
    }

    const { success } = await ttdService.verifySignPassword({
      userNo,
      password,
    });
    if (!success) {
      throw new TRPCError({ code: "FORBIDDEN", message: "签署密码错误" });
    }

    return userNo;
  }
}

export default new TtdUrlService();
