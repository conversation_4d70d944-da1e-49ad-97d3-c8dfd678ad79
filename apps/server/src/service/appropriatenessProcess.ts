import { DocumentType } from "@portfolio-service/schema/document";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { TRPCError } from "@trpc/server";
import AdmZip from "adm-zip";
import dayjs from "dayjs";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import path from "path";
import { z } from "zod";
import env from "../config/env";
import textLabel from "../config/textLabel";
import typeCode from "../config/typeCode";
import {
  classificationDocumentsInputSchema,
  classificationInputSchema,
  classificationReviewInputSchema,
  createInputSchema,
  documentsReviewInputSchema,
  documentsSignInputSchema,
  listInputSchema,
  NotificationType,
  riskTestInputSchema,
  riskTestReviewinputSchema,
  setSignPasswordInputSchema,
  userIdSchema,
} from "../model/appropriatenessProcess";
import { DataStr } from "../model/ttd";
import {
  appropriatenessProcessDocument as documentsRelation,
  model,
  select,
} from "../schema/appropriatenessProcess";
import * as document from "../schema/document";
import * as identityValidation from "../schema/identityValidation";
import * as riskTest from "../schema/riskTest";
import * as user from "../schema/user";
import * as workflowTask from "../schema/workflowTask";
import ttdService from "../service/ttd";
import { timestampEqDate } from "../utils/drizzle";
import { withMutex } from "../utils/lock";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import { appropriatenessMachine as machine } from "../workflow/appropriateness";
import baseInfoService from "./baseInfo";
import documentService from "./document";
import fileUploadService from "./fileUpload";
import notificationService from "./notification";
import riskTestService from "./riskTest";
import questionaireService from "./riskTestQuestionaire";
import signPasswordService from "./signPassword";
import taxDeclarationService from "./taxDeclaration";
import userService from "./user";
import workflowService from "./workflow";

class AppropriatenessProcessService extends Service {
  private select = {
    ...select,
    state: workflowTask.select.state,
    status: workflowTask.select.status,
    assigneeId: workflowTask.select.assigneeId,
    data: workflowTask.select.data,
    updateTime: workflowTask.select.updateTime,
    questionaireId: riskTest.select.questionaireId,
    score: riskTest.select.score,
    level: riskTest.select.level,
    username: user.model.name,
    userType: user.model.type,
    identityNumber: user.model.identityNumber,
    identityType: user.model.identityType,
    identityVerificationStatus: identityValidation.model.status,
    identityVerificationType: identityValidation.model.type,
  };

  private byIdentityNumberSelect = {
    ...select,
    level: riskTest.select.level,
  };

  async getByIdSafe(id: number, assigneeId?: number) {
    const predicate: SQL[] = [eq(model.id, id)];
    if (assigneeId != null) {
      predicate.push(eq(workflowTask.model.assigneeId, assigneeId));
    }

    let query = this.db.select(this.select).from(model).$dynamic();
    query = this.withJoin(query);
    query = query.where(and(...predicate));

    return first(await query);
  }

  async getById(id: number, assigneeId?: number) {
    const record = await this.getByIdSafe(id, assigneeId);
    if (!record) throw new TRPCError({ code: "NOT_FOUND" });

    return record;
  }

  async getByWorkflowId(id: string) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.workflowId, id))
      .$dynamic();
    query = this.withJoin(query);

    const [record] = await query;

    if (!record) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return record;
  }

  async getListByIdentityNumber(identityNumber: string) {
    let query = this.db
      .select(this.byIdentityNumberSelect)
      .from(model)
      .$dynamic();
    query = query
      .leftJoin(user.model, eq(model.userId, user.model.id))
      .leftJoin(riskTest.model, eq(model.riskTestId, riskTest.model.id));
    query = query.where(eq(user.model.identityNumber, identityNumber));

    const result = await query;

    const documentPromises = result.map(async (it) => {
      const documents = await this.getDocumentsById(it.id);
      return { id: it.id, documents };
    });

    const documentsList = await Promise.all(documentPromises);

    return result.map((it) => ({
      ...it,
      documents: documentsList.find(({ id }) => id === it.id)?.documents,
    }));
  }

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input);
    query = this.withOrder(query);

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getOwn(userId: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(eq(model.userId, userId))
      .orderBy(desc(model.createTime))
      .limit(1)
      .$dynamic();
    query = this.withJoin(query);

    const result = await query;
    return { item: first(result) };
  }

  async getOwnActive(userId: number) {
    let query = this.db
      .select(this.select)
      .from(model)
      .where(
        and(eq(model.userId, userId), eq(workflowTask.model.status, "active"))
      )
      .orderBy(desc(model.createTime))
      .limit(1)
      .$dynamic();
    query = this.withJoin(query);

    const result = await query;
    return { item: first(result) };
  }

  async getDocumentsById(id: number) {
    const result = await this.db
      .select()
      .from(documentsRelation)
      .where(eq(documentsRelation.processId, id));

    return await documentService.getByIds(result.map((it) => it.documentId));
  }

  async getDocumentsDetailById(id: number) {
    const documents = await this.getDocumentsById(id);

    const ttdFileIds = documents
      .map((it) => it.ttdFileId)
      .filter(Boolean) as string[];

    const ttdFiles = await Promise.all(
      ttdFileIds.map((id) => ttdService.querySignedDocument(id))
    );

    return documents.map((document) => ({
      ...document,
      ttdFile: ttdFiles.find((file) => file.fileId === document.ttdFileId),
    }));
  }

  async getTtdFileIdsById(id: number) {
    const documents = await this.getDocumentsById(id);

    const fileIds: string[] = [];
    for (const { ttdFileId, obsolete } of documents) {
      if (!!ttdFileId && !obsolete) {
        fileIds.push(ttdFileId);
      }
    }

    return fileIds;
  }

  async create(input: z.infer<typeof createInputSchema>) {
    return await withMutex(
      ["appropriatenessProcess.create", input.userId],
      async () => {
        const record = await this.getOwn(input.userId);
        if (!!record?.item && record.item.status === "active") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "存在进行中的认定流程",
          });
        }

        return await transaction.run(async (tx) => {
          const actor = await workflowService
            .createActor(machine)
            .start(input.operatorId, input.userId);
          const [result] = await tx.insert(model).values({
            workflowId: actor.id,
            userId: input.userId,
          });
          return result.insertId;
        });
      }
    );
  }

  async classification(
    userId: number,
    input: z.infer<typeof classificationInputSchema>
  ) {
    const { classification } = input;

    await withMutex(
      ["appropriatenessProcess.classification", userId],
      async () => {
        let id = input.id;

        if (id == null) {
          const record = await this.getOwnActive(userId);
          id = record?.item?.id;
        }

        await transaction.run(async (tx) => {
          if (id == null) {
            id = await this.create({ operatorId: userId, userId });
          }

          const record = await this.getById(id, userId);

          const actor = await workflowService.getActor(machine, {
            id: record.workflowId,
            state: ["classification", "classificationRedo"],
          });

          for (const { type, fileUploadId } of input.documents) {
            const [result] = await tx
              .insert(document.model)
              .values({ userId, type, fileUploadId });
            await tx
              .insert(documentsRelation)
              .values({ documentId: result.insertId, processId: id });
          }

          await tx
            .update(model)
            .set({ classification })
            .where(eq(model.id, id));
          await actor.send({ type: "event.next" }, userId, null);
        });
      }
    );
  }

  async classificationDocuments(
    input: z.infer<typeof classificationDocumentsInputSchema>
  ) {
    await withMutex(
      ["appropriatenessProcess.classificationDocuments", input],
      async () => {
        const record = await this.getById(input.id);

        await transaction.run(async (tx) => {
          for (const { type, fileUploadId } of input.documents) {
            const [result] = await tx
              .insert(document.model)
              .values({ userId: record.userId, type, fileUploadId });
            await tx
              .insert(documentsRelation)
              .values({ documentId: result.insertId, processId: record.id });
          }
        });
      }
    );
  }

  async classificationReview(
    input: z.infer<typeof classificationReviewInputSchema>
  ) {
    const { id, operatorId, pass } = input;
    const record = await this.getById(id);
    const classification = record.classification;
    if (!classification) {
      throw new TRPCError({ code: "FORBIDDEN", message: "还未选择投资者类型" });
    }

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["classificationReview"],
      });

      if (!pass) {
        await actor.send({ type: "event.redo" }, operatorId, record.userId);
        return;
      }

      await actor.send(
        { type: "event.classificationReviewPass", value: classification },
        operatorId,
        record.userId
      );

      if (classification === "专业投资者") {
        await this.generateDocumentsForSign(id);
      }
    });
  }

  async riskTest(userId: number, input: z.infer<typeof riskTestInputSchema>) {
    const { id, answers, questionaireId } = input;

    const record = await this.getById(id, userId);

    const { score, level } = await questionaireService.getScoreAndLevel(
      questionaireId,
      answers
    );

    await transaction.run(async (tx) => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["riskTest", "riskTestRedo"],
      });

      const [result] = await riskTestService.create({
        answers: JSON.stringify(answers),
        level,
        questionaireId,
        score,
        userId,
      });

      await tx
        .update(model)
        .set({ riskTestId: result.insertId })
        .where(eq(model.workflowId, record.workflowId));

      await actor.send({ type: "event.next" }, userId, null);
    });
  }

  async riskTestReview(input: z.infer<typeof riskTestReviewinputSchema>) {
    const { id, operatorId, pass } = input;
    const record = await this.getById(id);
    const { riskTestId } = record;
    if (!riskTestId) {
      throw new TRPCError({ code: "FORBIDDEN", message: "风测未完成" });
    }
    const riskTest = await riskTestService.getById(riskTestId);

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["riskTestReview"],
      });
      if (!pass) {
        await actor.send({ type: "event.redo" }, operatorId, record.userId);
        return;
      }

      await this.db
        .update(user.model)
        .set({ riskLevel: riskTest.level })
        .where(eq(user.model.id, record.userId));
      await actor.send({ type: "event.next" }, operatorId, record.userId);
      await this.generateDocumentsForSign(id);
    });
  }

  async setSignPassword(
    userId: number,
    input: z.infer<typeof setSignPasswordInputSchema>
  ) {
    const record = await this.getById(input.id, userId);

    await workflowService.getActor(machine, {
      id: record.workflowId,
      state: ["documentsSign", "documentsSignRedo"],
    });

    await signPasswordService.create(userId, input.password);
  }

  async documentsSign(
    userId: number,
    input: z.infer<typeof documentsSignInputSchema>
  ) {
    const record = await this.getById(input.id, userId);
    await this.validateDocumentsSign(input.id);

    await transaction.run(async () => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["documentsSign", "documentsSignRedo"],
      });
      await actor.send({ type: "event.next" }, userId, null);
    });
  }

  async documentsReview(input: z.infer<typeof documentsReviewInputSchema>) {
    const { id, operatorId, pass } = input;
    const record = await this.getById(id);

    await transaction.run(async (tx) => {
      const actor = await workflowService.getActor(machine, {
        id: record.workflowId,
        state: ["documentsReview"],
      });

      if (pass) {
        await tx
          .update(user.model)
          .set({ classification: record.classification })
          .where(eq(user.model.id, record.userId));

        await actor.send({ type: "event.next" }, operatorId, null);
      } else {
        const documents = await this.getDocumentsById(id);
        const documentIds = documents.map((it) => it.id);

        if (!!documentIds.length) {
          await documentService.makeObsolete(documentIds, [
            "合格投资者承诺",
            "投资者基本信息表",
            "税收声明",
            "风险测评问卷",
          ]);
        }

        await this.generateDocumentsForSign(id);
        await actor.send({ type: "event.redo" }, operatorId, record.userId);
      }
    });
  }

  async sendNotification(
    workflowId: string | undefined,
    type: NotificationType
  ) {
    if (!workflowId) return;

    const record = await this.getByWorkflowId(workflowId);

    await notificationService.create([
      {
        type: "reminder",
        userId: record.userId,
        title: "投资者类型告知函",
        content: this.getNotificationContent(record, type),
        key: "适当性提醒",
      },
    ]);
  }

  async getRiskTestQuestionnaireDocument(riskTestId: number) {
    const result = await this.db
      .select({ id: model.id })
      .from(model)
      .where(eq(model.riskTestId, riskTestId));
    const record = first(result);
    if (!record) return;

    const documentQuery = this.db
      .select(document.select)
      .from(documentsRelation)
      .$dynamic();

    documentQuery
      .leftJoin(
        document.model,
        eq(documentsRelation.documentId, document.model.id)
      )
      .where(
        and(
          eq(documentsRelation.processId, record.id),
          eq(document.model.type, "风险测评问卷"),
          eq(document.model.obsolete, false)
        )
      );
    const documentResult = await documentQuery;
    return first(documentResult);
  }

  async getDocumentsZipById(id: number) {
    const record = await this.getById(id);
    const zip = new AdmZip();

    const documents = await this.getDocumentsById(id);
    for (const document of documents) {
      if (document.obsolete) continue;

      if (document.fileUploadId) {
        const file = await fileUploadService.getByIdSafe(document.fileUploadId);
        zip.addLocalFile(
          path.resolve(env.FILE_UPLOAD_DIR, document.fileUploadId),
          undefined,
          `${document.type}_${file?.name}`
        );
        continue;
      }

      if (document.ttdFileId) {
        const { url } = await ttdService.querySignedDocument(
          document.ttdFileId
        );
        const arrayBuffer = await fetch(url).then((response) =>
          response.arrayBuffer()
        );
        zip.addFile(`${document.type}.pdf`, Buffer.from(arrayBuffer));
      }
    }

    return {
      filename: `适当性流程_${record.identityNumber}_${record.username}.zip`,
      data: zip.toBuffer(),
    };
  }

  private async generateDocumentsForSign(id: number) {
    const record = await this.getById(id);
    const user = await userService.getById(record.userId);

    const { item: baseInfo } =
      (await baseInfoService.getByUserId(user.id)) || {};
    const { item: taxDeclaration } = await taxDeclarationService.getByUserId(
      user.id
    );

    const riskTest = record.riskTestId
      ? await riskTestService.getById(record.riskTestId)
      : undefined;

    const riskDataStr: DataStr = {};

    if (riskTest) {
      for (const answer of riskTest.answers) {
        const key =
          textLabel[`ans${answer.questionId}` as keyof typeof textLabel];
        const value = answer.optionId;

        if (!key || !value) {
          continue;
        }

        riskDataStr[key] = { value };
      }
    }

    const level = riskTest?.level;
    const levelKey = level ? textLabel[level] : undefined;
    if (levelKey) {
      riskDataStr[levelKey] = { value: "√" };
    }

    const portfolioLevel = riskTest?.level
      ?.slice()
      ?.replace("C", "R") as keyof typeof textLabel;
    const portfolioLevelKey = portfolioLevel
      ? textLabel[portfolioLevel]
      : undefined;

    if (portfolioLevelKey) {
      riskDataStr[portfolioLevelKey] = { value: "√" };
    }

    const data: { typeCode: string; type: DocumentType; dataStr?: DataStr }[] =
      [];

    if (user.type === "个人") {
      data.push(
        {
          typeCode: typeCode.合格投资者承诺函,
          type: "合格投资者承诺",
        },
        {
          typeCode: typeCode.投资者信息表自然人,
          type: "投资者基本信息表",
          dataStr: {
            [textLabel.投资者姓名]: { value: user.name },
            [textLabel.证件类型]: { value: user.identityType },
            [textLabel.证件号码]: { value: user.identityNumber },
            [textLabel.性别]: {
              value: first(baseInfo?.formData?.gender) || "",
            },
            [textLabel.年龄]: {
              value: baseInfo?.formData?.age?.toString() || "",
            },
            [textLabel.国籍]: { value: baseInfo?.formData?.nationality || "" },
            [textLabel.职业]: { value: baseInfo?.formData?.profession || "" },
            [textLabel.职务]: { value: baseInfo?.formData?.job || "" },
            [textLabel.座机]: { value: baseInfo?.formData?.landline || "" },
            [textLabel.移动电话]: { value: baseInfo?.formData?.phone || "" },
            [textLabel.邮编]: { value: baseInfo?.formData?.postCode || "" },
            [textLabel.电子邮箱]: { value: baseInfo?.formData?.email || "" },
            [textLabel.住址]: { value: baseInfo?.formData?.residency || "" },
            [textLabel.满足条件是]: {
              value: record.classification === "专业投资者" ? "√" : "",
            },
            [textLabel.资产规模是]: { value: "√" },
            [textLabel.控制关系否]: {
              value: first(baseInfo?.formData?.controller) ? "" : "√",
            },
            [textLabel.控制关系是]: {
              value: first(baseInfo?.formData?.controller) ? "√" : "",
            },
            [textLabel.控制关系原因]: {
              value: baseInfo?.formData?.controllerReason || "",
            },
            [textLabel.交易人本人是]: {
              value: first(baseInfo?.formData?.targetOfInterest) ? "√" : "",
            },
            [textLabel.交易人他人]: {
              value: first(baseInfo?.formData?.targetOfInterest) ? "" : "√",
            },
            [textLabel.交易人说明原因]: {
              value: baseInfo?.formData?.targetOfInterestReason || "",
            },
            [textLabel.不良诚信是]: {
              value: first(baseInfo?.formData?.creditDishonour) ? "√" : "",
            },
            [textLabel.不良诚信否]: {
              value: first(baseInfo?.formData?.creditDishonour) ? "" : "√",
            },
            [textLabel.不良诚信原因]: {
              value: baseInfo?.formData?.creditDishonourReason || "",
            },
          },
        },
        {
          typeCode: typeCode.个人税收居民身份声明文件,
          type: "税收声明",
          dataStr: {
            [textLabel.姓名]: { value: user.name },
            [textLabel.仅为中国税收居民]: {
              value:
                first(taxDeclaration?.formData?.type) === "chineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.仅为非居民]: {
              value:
                first(taxDeclaration?.formData?.type) ===
                "nonChineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.中国和其他国家税收居民]: {
              value:
                first(taxDeclaration?.formData?.type) === "both" ? "√" : "",
            },
            [textLabel.姓]: {
              value: taxDeclaration?.formData?.familyName || "",
            },
            [textLabel.名]: {
              value: taxDeclaration?.formData?.givenName || "",
            },
            [textLabel.出生日期]: {
              value: taxDeclaration?.formData?.birthday
                ? dayjs(taxDeclaration?.formData?.birthday).format(
                    "YYYY年MM月DD日"
                  )
                : "",
            },
            [textLabel.现居地址中文]: {
              value: taxDeclaration?.formData?.addressCN || "",
            },
            [textLabel.现居地址英文]: {
              value: taxDeclaration?.formData?.addressEN || "",
            },
            [textLabel.出生地中文]: {
              value: taxDeclaration?.formData?.placeOfBirthCN || "",
            },
            [textLabel.出生地英文]: {
              value: taxDeclaration?.formData?.placeOfBirthEN || "",
            },
            [textLabel.纳税人识别号A]: {
              value: taxDeclaration?.formData?.taxIdentityNumber || "",
            },
            [textLabel.纳税人识别号B]: {
              value: taxDeclaration?.formData?.taxIdentityNumber2 || "",
            },
            [textLabel.纳税人识别号C]: {
              value: taxDeclaration?.formData?.taxIdentityNumber3 || "",
            },
            [textLabel.居民国不发放纳税号]: {
              value:
                first(taxDeclaration?.formData?.noTaxIdentityNumberCause) ===
                "distribution"
                  ? "√"
                  : "",
            },
            [textLabel.持有人未取得纳税号]: {
              value:
                first(taxDeclaration?.formData?.noTaxIdentityNumberCause) ===
                "distribution"
                  ? ""
                  : "√",
            },
            [textLabel.未取得纳税号解释原因]: {
              value:
                taxDeclaration?.formData?.noTaxIdentityNumberCauseReason || "",
            },
            [textLabel.日期]: { value: dayjs().format("YYYY-MM-DD") },
          },
        }
      );
      if (record.classification == "专业投资者") {
        data.push({
          typeCode: typeCode.专业投资者告知及确认书,
          type: "专业投资者告知及确认书",
          dataStr: {
            [textLabel.投资者全称]: { value: user.name },
            [textLabel.证件类型]: { value: user.identityType },
            [textLabel.证件号码]: { value: user.identityNumber },
            [textLabel.日期]: { value: dayjs().format("YYYY-MM-DD") },
          },
        });
      }

      if (riskTest) {
        data.push({
          typeCode: typeCode.个人风险测评问卷,
          type: "风险测评问卷",
          dataStr: {
            [textLabel.投资者姓名]: { value: user.name },
            [textLabel.移动电话]: { value: user.phone || "" },
            [textLabel.证件类型]: { value: user.identityType },
            [textLabel.证件号码]: { value: user.identityNumber },
            [textLabel.总分]: { value: riskTest?.score?.toString() || "" },
            [textLabel.投资者风险等级]: { value: level || "" },
            [textLabel.产品风险等级]: { value: portfolioLevel || "" },
            ...riskDataStr,
          },
        });
      }
    } else if (user.type === "机构") {
      data.push(
        {
          typeCode: typeCode.合格投资者承诺函,
          type: "合格投资者承诺",
        },
        {
          typeCode: typeCode.投资者信息表机构,
          type: "投资者基本信息表",
          dataStr: {
            [textLabel.机构名称]: { value: user.name },
            [textLabel.机构证件类型]: { value: user.identityType },
            [textLabel.机构证件编号]: { value: user.identityNumber },
            [textLabel.机构类型]: { value: baseInfo?.formData?.type || "" },
            [textLabel.有效期]: {
              value: baseInfo?.formData?.identityExpireDate
                ? dayjs(baseInfo?.formData?.identityExpireDate).format(
                    "YYYY年MM月DD日"
                  )
                : "",
            },
            [textLabel.机构资质证明]: {
              value: baseInfo?.formData?.endowment || "",
            },
            [textLabel.资质证明编号]: {
              value: baseInfo?.formData?.endowmentNumber || "",
            },
            [textLabel.经营范围]: {
              value: baseInfo?.formData?.businessRange || "",
            },
            [textLabel.注册地址]: {
              value: baseInfo?.formData?.registerAddress || "",
            },
            [textLabel.办公地址]: {
              value: baseInfo?.formData?.operationAddress || "",
            },
            [textLabel.注册资本]: {
              value: baseInfo?.formData?.registerAsset || "",
            },
            [textLabel.股东或者实际控制人]: {
              value: baseInfo?.formData?.controller || "",
            },
            [textLabel.法人姓名]: { value: user.representativeName || "" },
            [textLabel.法人证件类型]: {
              value: user.representativeIdentityType || "",
            },
            [textLabel.法人证件号码]: {
              value: user.representativeIdentityNumber || "",
            },
            [textLabel.法人证件有效期]: {
              value: baseInfo?.formData?.representativeIdentityExpireDate
                ? dayjs(
                    baseInfo?.formData?.representativeIdentityExpireDate
                  ).format("YYYY年MM月DD日")
                : "",
            },
            [textLabel.法人性别]: {
              value: first(baseInfo?.formData?.representativeGender) || "",
            },
            [textLabel.法人年龄]: {
              value: baseInfo?.formData?.representativeAge || "",
            },
            [textLabel.法人职务]: {
              value: baseInfo?.formData?.representativeJob || "",
            },
            [textLabel.法人电子邮箱]: {
              value: baseInfo?.formData?.representativeEmail || "",
            },
            [textLabel.法人座机]: {
              value: baseInfo?.formData?.representativeLandline || "",
            },
            [textLabel.法人移动电话]: {
              value: baseInfo?.formData?.representativePhone || "",
            },
            [textLabel.法人邮编]: {
              value: baseInfo?.formData?.representativePostCode || "",
            },
            [textLabel.法人地址]: {
              value: baseInfo?.formData?.representativeAddress || "",
            },

            [textLabel.经办人姓名]: {
              value: baseInfo?.formData?.assigneeName || "",
            },
            [textLabel.经办人证件类型]: {
              value: baseInfo?.formData?.assigneeIdentityType || "",
            },
            [textLabel.经办人证件号码]: {
              value: baseInfo?.formData?.assigneeIdentityNumber || "",
            },
            [textLabel.经办人证件有效期]: {
              value: baseInfo?.formData?.assigneeIdentityExpireDate
                ? dayjs(baseInfo?.formData?.assigneeIdentityExpireDate).format(
                    "YYYY年MM月DD日"
                  )
                : "",
            },
            [textLabel.经办人性别]: {
              value: first(baseInfo?.formData?.assigneeGender) || "",
            },
            [textLabel.经办人年龄]: {
              value: baseInfo?.formData?.assigneeAge || "",
            },
            [textLabel.经办人职务]: {
              value: baseInfo?.formData?.assigneeJob || "",
            },
            [textLabel.经办人邮箱]: {
              value: baseInfo?.formData?.assigneeEmail || "",
            },
            [textLabel.经办人座机]: {
              value: baseInfo?.formData?.assigneeLandline || "",
            },
            [textLabel.经办人移动电话]: {
              value: baseInfo?.formData?.assigneePhone || "",
            },
            [textLabel.经办人办公邮编]: {
              value: baseInfo?.formData?.assigneePostCode || "",
            },
            [textLabel.经办人办公地址]: {
              value: baseInfo?.formData?.assigneeAddress || "",
            },
            [textLabel.经办人与机构关系]: {
              value: baseInfo?.formData?.assigneeRelation || "",
            },
            [textLabel.满足条件是]: {
              value: record.classification === "专业投资者" ? "√" : "",
            },
            [textLabel.资产规模是]: { value: "√" },
            [textLabel.控制关系否]: {
              value: first(baseInfo?.formData?.controller) ? "" : "√",
            },
            [textLabel.控制关系是]: {
              value: first(baseInfo?.formData?.controller) ? "√" : "",
            },
            [textLabel.控制关系原因]: {
              value: baseInfo?.formData?.controllerReason || "",
            },
            [textLabel.交易人本人是]: {
              value: first(baseInfo?.formData?.targetOfInterest) ? "√" : "",
            },
            [textLabel.交易人他人]: {
              value: first(baseInfo?.formData?.targetOfInterest) ? "" : "√",
            },
            [textLabel.交易人说明原因]: {
              value: baseInfo?.formData?.targetOfInterestReason || "",
            },
            [textLabel.不良诚信是]: {
              value: first(baseInfo?.formData?.creditDishonour) ? "√" : "",
            },
            [textLabel.不良诚信否]: {
              value: first(baseInfo?.formData?.creditDishonour) ? "" : "√",
            },
            [textLabel.不良诚信原因]: {
              value: baseInfo?.formData?.creditDishonourReason || "",
            },
          },
        },
        {
          typeCode: typeCode.机构税收文件,
          type: "税收声明",
          dataStr: {
            [textLabel.机构名称]: { value: user.name },
            [textLabel.消极非金融机构]: {
              value:
                first(taxDeclaration?.formData?.organizationType) ===
                "pessimistic"
                  ? "√"
                  : "",
            },
            [textLabel.其他非金融机构]: {
              value:
                first(taxDeclaration?.formData?.organizationType) === "other"
                  ? "√"
                  : "",
            },
            [textLabel.仅为中国税收居民]: {
              value:
                first(taxDeclaration?.formData?.organizationTaxType) ===
                "chineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.仅为非居民]: {
              value:
                first(taxDeclaration?.formData?.organizationTaxType) ===
                "nonChineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.中国和其他国家税收居民]: {
              value:
                first(taxDeclaration?.formData?.organizationTaxType) === "both"
                  ? "√"
                  : "",
            },
            [textLabel.机构名称英文]: {
              value: taxDeclaration?.formData?.organizaitonNameEN || "",
            },
            [textLabel.机构地址英文]: {
              value: taxDeclaration?.formData?.organizationAddressEN || "",
            },
            [textLabel.机构地址中文]: {
              value: taxDeclaration?.formData?.organizationAddressCN || "",
            },
            [textLabel.纳税人识别号A]: {
              value:
                taxDeclaration?.formData?.organizationTaxIdentityNumber || "",
            },
            [textLabel.纳税人识别号B]: {
              value:
                taxDeclaration?.formData?.organizationTaxIdentityNumber2 || "",
            },
            [textLabel.纳税人识别号C]: {
              value:
                taxDeclaration?.formData?.organizationTaxIdentityNumber3 || "",
            },
            [textLabel.居民国不发放纳税号]: {
              value:
                first(
                  taxDeclaration?.formData?.organizationNoTaxIdentityNumberCause
                ) === "distribution"
                  ? "√"
                  : "",
            },
            [textLabel.持有人未取得纳税号]: {
              value:
                first(taxDeclaration?.formData?.noTaxIdentityNumberCause) ===
                "obtain"
                  ? "√"
                  : "",
            },
            [textLabel.未取得纳税号解释原因]: {
              value:
                taxDeclaration?.formData
                  ?.organizationNoTaxIdentityNumberCauseReason || "",
            },
            [textLabel.日期]: { value: dayjs().format("YYYY-MM-DD") },
          },
        }
      );

      if (first(taxDeclaration?.formData?.organizationType) === "pessimistic") {
        data.push({
          typeCode: typeCode.个人税收居民身份声明文件,
          type: "税收声明",
          dataStr: {
            [textLabel.姓名]: { value: user.name },
            [textLabel.仅为中国税收居民]: {
              value:
                first(taxDeclaration?.formData?.type) === "chineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.仅为非居民]: {
              value:
                first(taxDeclaration?.formData?.type) ===
                "nonChineseTaxResident"
                  ? "√"
                  : "",
            },
            [textLabel.中国和其他国家税收居民]: {
              value:
                first(taxDeclaration?.formData?.type) === "both" ? "√" : "",
            },
            [textLabel.姓]: {
              value: taxDeclaration?.formData?.familyName || "",
            },
            [textLabel.名]: {
              value: taxDeclaration?.formData?.givenName || "",
            },
            [textLabel.出生日期]: {
              value: taxDeclaration?.formData?.birthday
                ? dayjs(taxDeclaration?.formData?.birthday).format(
                    "YYYY年MM月DD日"
                  )
                : "",
            },
            [textLabel.现居地址中文]: {
              value: taxDeclaration?.formData?.addressCN || "",
            },
            [textLabel.现居地址英文]: {
              value: taxDeclaration?.formData?.addressEN || "",
            },
            [textLabel.出生地中文]: {
              value: taxDeclaration?.formData?.placeOfBirthCN || "",
            },
            [textLabel.出生地英文]: {
              value: taxDeclaration?.formData?.placeOfBirthEN || "",
            },
            [textLabel.纳税人识别号A]: {
              value: taxDeclaration?.formData?.taxIdentityNumber || "",
            },
            [textLabel.纳税人识别号B]: {
              value: taxDeclaration?.formData?.taxIdentityNumber2 || "",
            },
            [textLabel.纳税人识别号C]: {
              value: taxDeclaration?.formData?.taxIdentityNumber3 || "",
            },
            [textLabel.居民国不发放纳税号]: {
              value:
                first(taxDeclaration?.formData?.noTaxIdentityNumberCause) ===
                "distribution"
                  ? "√"
                  : "",
            },
            [textLabel.持有人未取得纳税号]: {
              value:
                first(taxDeclaration?.formData?.noTaxIdentityNumberCause) ===
                "distribution"
                  ? ""
                  : "√",
            },
            [textLabel.未取得纳税号解释原因]: {
              value:
                taxDeclaration?.formData?.noTaxIdentityNumberCauseReason || "",
            },
            [textLabel.日期]: { value: dayjs().format("YYYY-MM-DD") },
          },
        });
      }
    } else if (user.type === "产品") {
      data.push(
        {
          typeCode: typeCode.合格投资者承诺函,
          type: "合格投资者承诺",
          dataStr: {},
        },
        {
          typeCode: typeCode.投资者基本信息表产品,
          type: "投资者基本信息表",
          dataStr: {},
        },
        {
          typeCode: typeCode.机构税收文件,
          type: "税收声明",
          dataStr: {},
        }
      );
    }

    const promises = data.map(async ({ typeCode, type, dataStr }) => {
      const { fileId } = await ttdService.createDocumentForSign(
        typeCode,
        undefined,
        dataStr
      );
      return await documentService.create({
        userId: record.userId,
        type,
        ttdFileId: fileId,
      });
    });

    const documentIds = await Promise.all(promises);
    await this.db.insert(documentsRelation).values(
      documentIds.map((documentId) => ({
        documentId,
        processId: record.id,
      }))
    );
  }

  private async validateDocumentsSign(id: number) {
    const relations = await this.db
      .select()
      .from(documentsRelation)
      .where(eq(documentsRelation.processId, id));

    if (!relations.length) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const promises = relations.map(async ({ documentId }) => {
      const { ttdFileId } = await documentService.getById(documentId);
      if (!ttdFileId) {
        return;
      }

      const ttdDocument = await ttdService.querySignedDocument(ttdFileId);
      if (!ttdDocument) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      if (ttdDocument.investorState !== 1) {
        throw new TRPCError({ code: "FORBIDDEN", message: "文件未签署" });
      }
    });

    await Promise.all(promises);
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(
        workflowTask.model,
        eq(model.workflowId, workflowTask.model.workflowId)
      )
      .leftJoin(riskTest.model, eq(model.riskTestId, riskTest.model.id))
      .leftJoin(user.model, eq(model.userId, user.model.id))
      .leftJoin(
        identityValidation.model,
        eq(model.identityValidationId, identityValidation.model.id)
      );

    return query;
  }

  private withOrder<T extends MySqlSelect>(query: T) {
    query.orderBy(desc(model.createTime));
    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { userId, status, state, date } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    const statusParsed = statusSchema.safeParse(status);
    if (statusParsed.success) {
      conditions.push(eq(workflowTask.model.status, statusParsed.data));
    }

    const stateParsed = z.string().safeParse(state);
    if (stateParsed.success) {
      conditions.push(eq(workflowTask.model.state, stateParsed.data));
    }

    const dateParsed = z.date().safeParse(date);
    if (dateParsed.success) {
      const predicate = timestampEqDate(model.createTime, dateParsed.data);
      if (predicate) {
        conditions.push(predicate);
      }
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }

  private getNotificationContent(
    record: Awaited<ReturnType<typeof this.getByWorkflowId>>,
    type: NotificationType
  ) {
    const { createTime, classification } = record;
    const date = dayjs(createTime).format("YYYY-MM-DD");

    switch (type) {
      case "complete":
        return `我司依据相关规定，对您于 ${date} 提交的合格投资者认定材料进行了审核确认，最终认定您的合格投资者类型为${classification}。`;
      case "reject":
        return `我司依据相关规定，对您于 ${date} 提交的合格投资者认定材料进行了审核确认。很遗憾您的认定材料未能通过，如有疑问请联系相关负责人。`;
      case "terminate":
        return `您于 ${date} 提交的合格投资者认定已中止，如有疑问请联系相关负责人。`;
    }
  }
}

export default new AppropriatenessProcessService();
