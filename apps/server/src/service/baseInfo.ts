import { eq, inArray } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import { updateInputSchema } from "../model/baseInfo";
import { model, select } from "../schema/baseInfo";
import * as user from "../schema/user";
import { Service } from "../utils/service";

class BaseInfoService extends Service {
  private select = {
    ...select,
    name: user.model.name,
    identityType: user.model.identityType,
    identityNumber: user.model.identityNumber,
    userType: user.model.type,
  };

  async getByUserId(userId: number) {
    let query = this.db.select(this.select).from(model).$dynamic();
    query = this.withJoin(query);
    query = query.where(eq(model.userId, userId));

    const [record] = await query;

    return { item: record };
  }

  async getByUserIds(userIds: number[]) {
    if (!userIds.length) return { items: [] };

    let query = this.db.select(this.select).from(model).$dynamic();
    query = this.withJoin(query);
    query = query.where(inArray(model.userId, userIds));

    return { items: await query };
  }

  async update(userId: number, input: z.infer<typeof updateInputSchema>) {
    const updateData = { formData: JSON.stringify(input) };
    const insertData = { ...updateData, userId };

    await this.db
      .insert(model)
      .values(insertData)
      .onDuplicateKeyUpdate({ set: updateData });
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query.leftJoin(user.model, eq(model.userId, user.model.id));

    return query;
  }
}

export default new BaseInfoService();
