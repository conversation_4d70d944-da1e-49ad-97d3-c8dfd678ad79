import { dividendMethodSchema } from "@portfolio-service/schema/dividend";
import { SQL, and, count, desc, eq, like, or, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import * as dividend from "../schema/dividend";
import { model, select } from "../schema/dividend";
import * as profitRewardRate from "../schema/profitRewardRate";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import { mode } from "crypto-js";

class DividendService extends Service {
  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    let query = this.db.select(select).from(model).$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input, identityNumber);
    query = query.orderBy(desc(model.dividendDate));

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withCondition(amountQuery, input, identityNumber);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async getDatesByPortfolioId(portfolioId: number) {
    const result = await this.db
      .selectDistinct({ dividendDate: model.dividendDate })
      .from(model)
      .where(eq(model.portfolioId, portfolioId));

    return result.map((it) => it.dividendDate).filter(Boolean) as string[];
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>,
    identityNumber: string
  ) {
    const conditions: SQL[] = [
      eq(dividend.model.clientIdentityNumber, identityNumber),
    ];

    const portfolioParsed = z.string().safeParse(input?.portfolio);
    if (portfolioParsed.success) {
      const value = `%${portfolioParsed.data}%`;
      const predicate = or(
        like(dividend.model.portfolioName, value),
        like(dividend.model.portfolioNo, value)
      );

      if (predicate) {
        conditions.push(predicate);
      }
    }

    const dividendMethodParsed = dividendMethodSchema.safeParse(
      input?.dividendMethod
    );
    if (dividendMethodParsed.success) {
      conditions.push(
        eq(dividend.model.dividendMethod, dividendMethodParsed.data)
      );
    }

    query.where(and(...conditions));

    return query;
  }

  async getDataByDividedCalculator(
    input: z.infer<typeof dividendCalculatorFilter>,
    identityNumber: string
  ) {
    const conditions: SQL[] = [eq(model.clientIdentityNumber, identityNumber)];

    if (input.reinvestmentDistributionDate) {
      conditions.push(
        eq(sql`DATE(${model.dividendDate})`,sql`DATE(${input.reinvestmentDistributionDate})`),
      );
    }

    if (input.agency) {
      conditions.push(eq(model.agency, input.agency));
    }

    if (input.portfolioName) {
      conditions.push(eq(model.portfolioName, input.portfolioName));
    }
  
    return this.db
      .select({
        ...select,
        profitRewardRate: sql<string>`COALESCE(
            JSON_UNQUOTE(JSON_EXTRACT(${profitRewardRate.model.divideacosts}, '$.profitRewardRate')),
            JSON_UNQUOTE(JSON_EXTRACT(${profitRewardRate.model.dividebcosts}, '$.profitRewardRate'))
        )`.as('profitRewardRate')
    })
      .from(model).leftJoin(profitRewardRate.model,eq(model.portfolioName,profitRewardRate.model.name))
      .where(and(...conditions))
      .orderBy(desc(model.dividendDate));
  }
}

export default new DividendService();
