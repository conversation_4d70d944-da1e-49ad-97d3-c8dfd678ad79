import { dividendMethodSchema } from "@portfolio-service/schema/dividend";
import { SQL, and, count, desc, eq, like, or, sql } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { z } from "zod";
import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import * as aindex from "../schema/IndexDailyData";
import { model, select } from "../schema/dividend";
import * as profitRewardRate from "../schema/profitRewardRate";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import { mode } from "crypto-js";

class AIndexService extends Service {
  async getLastDayData() {
    const maxDateQuery = this.db
      .select({ maxDate: sql<string>`MAX(${aindex.model.TRADE_DT})` })
      .from(aindex.model);

    const result = await this.db
      .select()
      .from(aindex.model)
      .where(eq(aindex.model.TRADE_DT, maxDateQuery));

    return result;
  }

  async getTradeDayData(tradeDate: string) {
    
    const dbDateFormat = tradeDate.replace(/-/g, "");

    return this.db
      .select()
      .from(aindex.model)
      .where(eq(aindex.model.TRADE_DT, dbDateFormat));
  }
}

export default new AIndexService();
