import dayjs from "dayjs";
import { keyBy } from "lodash";
import { model } from "../schema/tradeConfirm";
import { Service } from "../utils/service";
import transaction from "../utils/transaction";
import notificationService from "./notification";
import tradeService from "./trade";
import userService from "./user";

class TradeConfirmService extends Service {
  async sendNotifications() {
    const trades = await tradeService.getNew();
    if (!trades.length) return;

    const identityNumbers = trades
      .map((it) => it.clientIdentityNumber)
      .filter(Boolean) as string[];

    const users = await userService.getListByIdentityNumbers(identityNumbers);
    const userDict = keyBy(users, (user) => user.identityNumber);

    const data: Parameters<typeof notificationService.create>[0] = [];

    for (const trade of trades) {
      const {
        clientIdentityNumber,
        applyDate,
        portfolioName,
        businessType,
        confStatus,
      } = trade;
      if (!clientIdentityNumber) continue;

      const user = userDict[clientIdentityNumber];
      if (!user) continue;

      const userId = user.id;
      const title = `${portfolioName}${confStatus}通知`;
      const content = `您申请于${dayjs(applyDate).format("YYYY-MM-DD")}${businessType}《${portfolioName}》的订单，交易${confStatus}。`;
      const key = "交易提醒";

      data.push({ userId, title, content, key, type: "reminder" });
    }

    await transaction.run(async () => {
      await notificationService.create(data);
      await this.create(
        trades.map((it) => it.confNumber).filter(Boolean) as string[]
      );
    });
  }

  async create(input: string[]) {
    if (!input.length) return;

    await this.db
      .insert(model)
      .values(input.map((it) => ({ confirmNumber: it })));
  }
}

export default new TradeConfirmService();
