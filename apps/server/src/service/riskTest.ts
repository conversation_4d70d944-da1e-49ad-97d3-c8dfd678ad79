import { TRPCError } from "@trpc/server";
import { and, count, desc, eq, SQL } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { first } from "lodash";
import { z } from "zod";
import { listInputSchema, userIdSchema } from "../model/riskTest";
import { answersSelect, insertSchema, model, select } from "../schema/riskTest";
import * as questionaire from "../schema/riskTestQuestionaire";
import * as user from "../schema/user";
import { paginationResult, withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import appropriatenessProcessService from "./appropriatenessProcess";
import riskTestProcessService from "./riskTestProcess";
import ttdService from "./ttd";

class RiskTestService extends Service {
  private select = {
    ...select,
    answers: answersSelect,
    type: questionaire.select.type,
    config: questionaire.select.config,
  };

  async getList(input: z.infer<typeof listInputSchema>) {
    let query = this.db
      .select({ ...select, username: user.model.name })
      .from(model)
      .$dynamic();
    query = withPagination(query, input);
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = this.withCondition(query, input);
    query = query.orderBy(desc(model.createTime));

    let amountQuery = this.db.select({ value: count() }).from(model).$dynamic();
    amountQuery = this.withCondition(amountQuery, input);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return paginationResult(queryResult, amountQueryResult[0].value);
  }

  async getById(id: number, userId?: number) {
    const predicate: SQL[] = [eq(model.id, id)];
    if (userId != null) {
      predicate.push(eq(model.userId, userId));
    }

    let query = this.db.select(this.select).from(model).$dynamic();
    query = query.leftJoin(
      questionaire.model,
      eq(model.questionaireId, questionaire.model.id)
    );
    query = query.where(and(...predicate));

    const [result] = await query;
    if (!result) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return result;
  }

  async getLastestByUserId(userId: number) {
    const result = await this.db
      .select()
      .from(model)
      .where(eq(model.userId, userId))
      .orderBy(desc(model.createTime));

    return first(result);
  }

  async getListByIdentityNumber(identityNumber: string) {
    let query = this.db.select(select).from(model).$dynamic();
    query = query.leftJoin(user.model, eq(model.userId, user.model.id));
    query = query.where(eq(user.model.identityNumber, identityNumber));

    return await query;
  }

  async getQuestionnaireLink(id: number) {
    let document =
      await appropriatenessProcessService.getRiskTestQuestionnaireDocument(id);
    if (!document?.ttdFileId) {
      document =
        await riskTestProcessService.getRiskTestQuestionnaireDocument(id);
    }
    if (!document?.ttdFileId) return { url: undefined };

    const ttdFile = await ttdService.querySignedDocumentSafe(
      document.ttdFileId
    );

    return { url: ttdFile?.url };
  }

  async create(input: z.infer<typeof insertSchema>) {
    return await this.db.insert(model).values(input);
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    filter: z.infer<typeof listInputSchema>
  ) {
    if (!filter) return query;

    const { userId } = filter;
    const conditions: SQL[] = [];

    const userIdParsed = userIdSchema.safeParse(userId);
    if (userIdParsed.success) {
      conditions.push(eq(model.userId, userIdParsed.data));
    }

    if (!!conditions.length) {
      query.where(and(...conditions));
    }

    return query;
  }
}

export default new RiskTestService();
