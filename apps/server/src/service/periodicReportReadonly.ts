import { SQL, and, count, desc, eq } from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import portfolioService from "./portfolio";
import { z } from "zod";
import { inputSchema } from "../model/periodicReportReadonly";
import * as fileUpload from "../schema/fileUpload";
import * as periodicReport from "../schema/periodicReport";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";

class PeriodicReportReadonlyService extends Service {
  private select = {
    id: periodicReport.model.id,
    date: periodicReport.model.date,
    type: periodicReport.model.type,
    fileUploadId: periodicReport.model.fileUploadId,
    name: fileUpload.model.name,
  };

  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    const access = await portfolioService.getPortfolioAccess({
      id: input.portfolioId,
      identityNumber,
    });
    if (!access?.holding) {
      return { items: [], amount: 0 };
    }

    let query = this.db
      .select(this.select)
      .from(periodicReport.model)
      .$dynamic();
    query = withPagination(query, input);
    query = this.withCondition(query, input);
    query = query.orderBy(desc(periodicReport.model.date));

    let amountQuery = this.db
      .select({ value: count() })
      .from(periodicReport.model)
      .$dynamic();
    amountQuery = this.withCondition(amountQuery, input);
    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>
  ) {
    query.leftJoin(
      fileUpload.model,
      eq(periodicReport.model.fileUploadId, fileUpload.model.id)
    );

    const conditions: SQL[] = [
      eq(periodicReport.model.portfolioId, input.portfolioId),
      eq(periodicReport.model.published, true),
    ];
    query.where(and(...conditions));

    return query;
  }
}

export default new PeriodicReportReadonlyService();
