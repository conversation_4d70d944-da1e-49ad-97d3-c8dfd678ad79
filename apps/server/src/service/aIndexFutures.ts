import { eq, sql, and, desc, inArray } from "drizzle-orm";
import { Service } from "../utils/service";
import * as indexFuturesDaily from "../schema/IndexFuturesDailyData";
import * as indexFuturesDescriptions from "../schema/indexFuturesDescription";
import * as indexDaily from "../schema/IndexDailyData";
import * as tradeDate from "../schema/tradeDate";
import dayjs from "dayjs";
import { get } from "lodash";

type FuturesType = "IC" | "IM" | "IF" | "IH";

interface FuturesConfig {
  spotWindCode: string;
  codePrefix: string;
}

interface ProcessedFuturesData {
  S_INFO_WINDCODE: string | null;
  TRADE_DT: string | null;
  S_DQ_CLOSE: string | null;
  S_DQ_CHANGE: string | null;
  S_DQ_OI: string | null;
  delistDate: string;
  basis: number;
  annualizedBasis: number;
  remainingDays: number;
  priceChangeRate: number;
}

class AIndexFuturesService extends Service {
  private sortedTradeDates: string[] = [];

  async initialize() {
    const tradeDates = await this.db
      .select({ date: tradeDate.model.date })
      .from(tradeDate.model);
    this.sortedTradeDates = tradeDates
      .map((d) => dayjs(d.date).format("YYYYMMDD"))
      .sort(); //
  }

  private readonly futuresConfig: Record<FuturesType, FuturesConfig> = {
    IC: { spotWindCode: "399905.SZ", codePrefix: "IC" },
    IM: { spotWindCode: "000852.SH", codePrefix: "IM" },
    IF: { spotWindCode: "000300.SH", codePrefix: "IF" },
    IH: { spotWindCode: "000016.SH", codePrefix: "IH" },
  };

  // 通用基础数据获取
  // private async getBaseData(whereCondition?: any,tradeDate:string) {
  //   const maxDateSubQuery = this.db
  //     .select({
  //       maxDate: sql<string>`MAX(${indexFuturesDaily.model.TRADE_DT})`,
  //     })
  //     .from(indexFuturesDaily.model);

  //   return this.db
  //     .select()
  //     .from(indexFuturesDaily.model)
  //     .where(
  //       whereCondition
  //         ? and(
  //             eq(indexFuturesDaily.model.TRADE_DT, maxDateSubQuery),
  //             whereCondition
  //           )
  //         : eq(indexFuturesDaily.model.TRADE_DT, maxDateSubQuery)
  //     )
  //     .prepare()
  //     .execute();
  // }
  private async getBaseData(tradeDate?: string) {
    const dbDateFormat = tradeDate?.replace(/-/g, "");

    return this.db
      .select()
      .from(indexFuturesDaily.model)
      .where(
        dbDateFormat
          ? eq(indexFuturesDaily.model.TRADE_DT, dbDateFormat)
          : undefined
      )
      .prepare()
      .execute();
  }

  //这里也改为日期只需要修改传入日期逻辑baseData即可
  async getMonthPlusContractsData(type: FuturesType, tradeDate: string) {
    // const currentDate = new Date();
    // const currentYear = currentDate.getFullYear();
    // const currentYearSuffix = currentYear.toString().slice(-2);
    // const currentMonth = currentDate.getMonth() + 1;
    const futuresData = await this.processFuturesData(type, tradeDate);

    const contractCodes = [
      ...new Set(futuresData.map((i) => i.S_INFO_WINDCODE).filter(Boolean)),
    ] as string[];

    if (contractCodes.length === 0) return [];

    const targetDate = dayjs(tradeDate).format("YYYYMMDD");

    return (
      this.db
        .select()
        .from(indexFuturesDaily.model)
        .where(
          and(
            inArray(indexFuturesDaily.model.S_INFO_WINDCODE, contractCodes),
            sql`${indexFuturesDaily.model.TRADE_DT} <= ${targetDate}`
          )
        )
        // .orderBy(desc(indexFuturesDaily.model.TRADE_DT))
        .prepare()
        .execute()
    );
  }

  // 通用期货数据处理器
  //这里改为日期只需要修改传入日期逻辑baseData即可
  private async processFuturesData(type: FuturesType, tradeDate: string) {
    const config = this.futuresConfig[type];
    const spotPriceMap = await this.getSpotPriceMap(config.spotWindCode);
    const [spotData] = await this.db
      .select()
      .from(indexDaily.model)
      .where(eq(indexDaily.model.S_INFO_WINDCODE, config.spotWindCode))
      .orderBy(desc(indexDaily.model.TRADE_DT))
      .limit(1);
    const allContractDescriptonsMap = await this.getAllContractDescriptions();
    // const workingDays = await this.getWorkingDays();
    const baseData = await this.getBaseData(tradeDate);
    // const currentDateStr = dayjs(tradeDate).format("YYYYMMDD");
    const tradeDateStr = dayjs(tradeDate).format("YYYYMMDD");
    const validData = await Promise.all(
      baseData
        .filter((item) => item.S_INFO_WINDCODE?.startsWith(config.codePrefix))
        .map(async (item) =>
          this.processContract(item, tradeDateStr, allContractDescriptonsMap)
        )
    );

    return validData
      .filter(Boolean)
      .map((item) => ({
        ...item,
        basis:
          Number(item.S_DQ_CLOSE) -
          Number(spotPriceMap.get(item.TRADE_DT) || 0),
        annualizedBasis: this.calculateAnnualizedBasis(
          Number(item.S_DQ_CLOSE),
          Number(spotPriceMap.get(item.TRADE_DT)),
          item.delistDate,
          252,
          false,
          item.TRADE_DT
        ),
        remainingDays: this.getWorkingDaysBetween(
          item.TRADE_DT,
          item.delistDate
        ),
        priceChangeRate: this.calculatePriceChangeRate(
          item.S_DQ_CHANGE,
          item.S_DQ_CLOSE
        ),
      }))
      .sort((a, b) => {
        const aCode = a.S_INFO_WINDCODE?.match(/(\D+)(\d+)/) || [];
        const bCode = b.S_INFO_WINDCODE?.match(/(\D+)(\d+)/) || [];

        const typeOrder = { IC: 1, IH: 2, IF: 3, IM: 4 };
        const aType = aCode[1] || "";
        const bType = bCode[1] || "";
        if (aType !== bType)
          return (
            (typeOrder[aType as keyof typeof typeOrder] || 5) -
            (typeOrder[aType as keyof typeof typeOrder] || 6)
          );

        const aNum = parseInt(aCode[2] || "0", 10);
        const bNum = parseInt(bCode[2] || "0", 10);
        return aNum - bNum;
      }) as ProcessedFuturesData[];
  }

  private async getFutures3YearsData(type: FuturesType, tradeDate: string) {
    const config = this.futuresConfig[type];
    const spotPriceMap = await this.getSpotPriceMap(config.spotWindCode);
    const allContractDescriptonsMap = await this.getAllContractDescriptions();

    const baseYear = dayjs(tradeDate).year();
    const yearSuffixes = Array.from(
      { length: 3 },
      (_, i) => (baseYear - i).toString().slice(-2) // 改为从参数日期计算
    );
    // const yearSuffixes = Array.from({ length: 3 }, (_, i) =>
    //   (new Date().getFullYear() - i).toString().slice(-2)
    // );
    const targetDate = dayjs(tradeDate).format("YYYYMMDD");

    const regexPattern = `^${config.codePrefix}(${yearSuffixes.join("|")})`;
    const codeRegex = new RegExp(regexPattern);

    const threeYearsData = await this.db
      .select()
      .from(indexFuturesDaily.model)
      .where(
        and(
          sql`${indexFuturesDaily.model.S_INFO_WINDCODE} REGEXP ${regexPattern}`,
          sql`${indexFuturesDaily.model.TRADE_DT} <= ${targetDate}`
        )
      )
      .orderBy(desc(indexFuturesDaily.model.TRADE_DT));

    return threeYearsData
      .filter(
        (item) =>
          codeRegex.test(item.S_INFO_WINDCODE as string) &&
          dayjs(item.TRADE_DT).isAfter(dayjs(tradeDate).subtract(3, "year"))
      )
      .map((item) => ({
        ...item,
        annualizedBasis: this.calculateAnnualizedBasis(
          Number(item.S_DQ_CLOSE),
          Number(spotPriceMap.get(item.TRADE_DT) || 0),
          allContractDescriptonsMap.get(
            item.S_INFO_WINDCODE?.slice(0, 6) as string
          ) as string,
          252,
          true,
          item.TRADE_DT as string
        ),
        remainingDays: this.getWorkingDaysBetween(
          allContractDescriptonsMap.get(
            item.S_INFO_WINDCODE?.slice(0, 6) as string
          ) as string,
          item.TRADE_DT as string
        ),
      }));
  }

  private async getFuturesDataByDates(type: FuturesType, tradeDates: string[]) {
    const config = this.futuresConfig[type];
    const spotPriceMap = await this.getSpotPriceMap(config.spotWindCode);
    const allContractDescriptonsMap = await this.getAllContractDescriptions();

    const yearSuffixes = Array.from(
      new Set(tradeDates.map((d) => dayjs(d).format("YY")))
    );
    const regexPattern = `^${config.codePrefix}(${yearSuffixes.join("|")})`;

    const dbFormattedDates = tradeDates.map((d) => d.replace(/-/g, ""));

    if (dbFormattedDates.length === 0) return [];

    const data = await this.db
      .select()
      .from(indexFuturesDaily.model)
      .where(
        and(
          sql`${indexFuturesDaily.model.S_INFO_WINDCODE} REGEXP ${regexPattern}`,
          dbFormattedDates.length > 0
            ? inArray(indexFuturesDaily.model.TRADE_DT, dbFormattedDates)
            : sql`1=0`
        )
      );

    return data.map((item) => ({
      ...item,
      annualizedBasis: this.calculateAnnualizedBasis(
        Number(item.S_DQ_CLOSE),
        Number(spotPriceMap.get(item.TRADE_DT) || 0),
        allContractDescriptonsMap.get(
          item.S_INFO_WINDCODE?.slice(0, 6) as string
        ) as string,
        252,
        true,
        item.TRADE_DT as string
      ),
      remainingDays: this.getWorkingDaysBetween(
        allContractDescriptonsMap.get(
          item.S_INFO_WINDCODE?.slice(0, 6) as string
        ) as string,
        item.TRADE_DT as string
      ),
    }));
  }

  // 通用合约处理
  //   private async processContract(item: any, currentDateStr: string) {
  //     const contractCode = item.S_INFO_WINDCODE?.slice(0, 6);
  //     const [description] = await this.db
  //       .select()
  //       .from(indexFuturesDescriptions.model)
  //       .where(eq(indexFuturesDescriptions.model.S_INFO_CODE, contractCode));

  //     // 添加双重空值检查
  //     return description?.S_INFO_DELISTDATE &&
  //       description.S_INFO_DELISTDATE >= currentDateStr
  //       ? { ...item, delistDate: description.S_INFO_DELISTDATE }
  //       : null;
  //   }

  // ... existing code ...

  private async processContract(
    item: any,
    currentDateStr: string,
    delistDateMap: any
  ) {
    const contractCode = item.S_INFO_WINDCODE?.slice(0, 6);
    const delistDate = contractCode ? delistDateMap.get(contractCode) : null;

    return delistDate && delistDate >= currentDateStr
      ? { ...item, delistDate }
      : null;
  }

  private getWorkingDaysBetween(startDate: string, endDate: string): number {
    const start = dayjs(startDate).format("YYYYMMDD");
    const end = dayjs(endDate).format("YYYYMMDD");

    const leftIdx = this.findFirstGreaterOrEqual(start);
    const rightIdx = this.findLastLessOrEqual(end);

    return rightIdx - leftIdx + 1;
  }

  private findFirstGreaterOrEqual(target: string): number {
    let left = 0,
      right = this.sortedTradeDates.length - 1;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (this.sortedTradeDates[mid] < target) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
    return left;
  }

  private findLastLessOrEqual(target: string): number {
    let left = 0,
      right = this.sortedTradeDates.length - 1;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (this.sortedTradeDates[mid] > target) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }
    return right;
  }

  private calculateAnnualizedBasis(
    futurePrice: number,
    spotPrice: number,
    delistDate: string,
    workingDays: number,
    skipDayCheck: boolean = false,
    tradeDay?: string
  ) {
    const startDate = tradeDay
      ? dayjs(tradeDay).format("YYYYMMDD")
      : dayjs().format("YYYYMMDD");

    const remainDays = this.getWorkingDaysBetween(startDate, delistDate);

    const basisValue =
      ((futurePrice - spotPrice) / spotPrice / remainDays) * 252 * 100;

    return skipDayCheck ? basisValue : remainDays > 0 ? basisValue : 0;
  }

  // 通用价格变化率计算
  private calculatePriceChangeRate(change: number, close: number) {
    return close - change != 0 ? (change / (close - change)) * 100 : 0;
  }

  // 通用趋势分析
  private async getAnnualizedBasisTrend(type: FuturesType, tradeDate: string) {
    const config = this.futuresConfig[type];
    const spotPriceMap = await this.getSpotPriceMap(config.spotWindCode);
    const baseData = await this.getMonthPlusContractsData(type, tradeDate);
    const allContractDescriptonsMap = await this.getAllContractDescriptions();
    const contractCodes = [
      ...new Set(
        baseData
          .filter((item) => item.S_INFO_WINDCODE?.startsWith(config.codePrefix))
          .map((item) => item.S_INFO_WINDCODE?.slice(0, 6))
      ),
    ].filter(Boolean) as string[];

    const descriptionMap = await this.getContractDescriptionMap(
      baseData,
      config.codePrefix
    );
    // const workingDays = await this.getWorkingDays();
    const currentDateStr = new Date()
      .toISOString()
      .slice(0, 10)
      .replace(/-/g, "");

    const processedData = await Promise.all(
      baseData
        .filter((item) => item.S_INFO_WINDCODE?.startsWith(config.codePrefix))
        .map(async (item) => {
          const contractCode = item.S_INFO_WINDCODE?.slice(0, 6);
          const description = descriptionMap.get(contractCode as string);
          const isValid =
            description?.S_INFO_DELISTDATE &&
            description.S_INFO_DELISTDATE >=
              dayjs(tradeDate).format("YYYYMMDD");

          return isValid
            ? {
                ...item,
                spotPrice: spotPriceMap.get(item.TRADE_DT) || 0,
                ...(await this.processContract(
                  item,
                  dayjs(tradeDate).format("YYYY-MM-DD"),
                  allContractDescriptonsMap
                )),
              }
            : null;
        })
    );

    const validData = processedData.filter((item) => item && item.delistDate);
    const xAxisData = this.getSortedUniqueDates(validData);

    return {
      xAxisData,
      seriesData: this.generateSeriesData(validData, 252, xAxisData),
    };
  }

  private async getSpotPriceMap(spotWindCode: string) {
    const spotData = await this.db
      .select({
        TRADE_DT: indexDaily.model.TRADE_DT,
        S_DQ_CLOSE: indexDaily.model.S_DQ_CLOSE,
      })
      .from(indexDaily.model)
      .where(eq(indexDaily.model.S_INFO_WINDCODE, spotWindCode));

    return new Map(spotData.map((s) => [s.TRADE_DT, Number(s.S_DQ_CLOSE)]));
  }

  // 获取合约描述映射表
  private async getContractDescriptionMap(baseData: any[], codePrefix: string) {
    const contractCodes = this.extractValidContractCodes(baseData, codePrefix);

    if (contractCodes.length === 0) {
      return new Map();
    }

    const descriptions = await this.db
      .select()
      .from(indexFuturesDescriptions.model)
      .where(
        inArray(indexFuturesDescriptions.model.S_INFO_CODE, contractCodes)
      );

    return new Map(descriptions.map((d) => [d.S_INFO_CODE, d]));
  }

  private extractValidContractCodes(
    baseData: any[],
    codePrefix: string
  ): string[] {
    return [
      ...new Set(
        baseData
          .filter((item) => item.S_INFO_WINDCODE?.startsWith(codePrefix))
          .map((item) => item.S_INFO_WINDCODE?.slice(0, 6))
          .filter(Boolean)
      ),
    ] as string[];
  }

  private async getAllContractDescriptions() {
    const descriptions = await this.db
      .select({
        S_INFO_CODE: indexFuturesDescriptions.model.S_INFO_CODE,
        S_INFO_DELISTDATE: indexFuturesDescriptions.model.S_INFO_DELISTDATE,
      })
      .from(indexFuturesDescriptions.model);

    return new Map(
      descriptions.map((d) => [d.S_INFO_CODE, d.S_INFO_DELISTDATE])
    );
  }

  async getICFuturesData(tradeDate: string) {
    return this.processFuturesData("IC", tradeDate);
  }
  async getIMFuturesData(tradeDate: string) {
    return this.processFuturesData("IM", tradeDate);
  }
  async getIFFuturesData(tradeDate: string) {
    return this.processFuturesData("IF", tradeDate);
  }
  async getIHFuturesData(tradeDate: string) {
    return this.processFuturesData("IH", tradeDate);
  }

  async getICAnnualizedBasisTrend(tradeDate: string) {
    return this.getAnnualizedBasisTrend("IC", tradeDate);
  }
  async getIMAnnualizedBasisTrend(tradeDate: string) {
    return this.getAnnualizedBasisTrend("IM", tradeDate);
  }
  async getIFAnnualizedBasisTrend(tradeDate: string) {
    return this.getAnnualizedBasisTrend("IF", tradeDate);
  }
  async getIHAnnualizedBasisTrend(tradeDate: string) {
    return this.getAnnualizedBasisTrend("IH", tradeDate);
  }

  async getICHedgeBasisTrend3Y(tradeDate: string) {
    const rawData = await this.getFutures3YearsData("IC", tradeDate);
    const tradeDates = this.getSortedUniqueDates(rawData);

    const dateMap = new Map<string, typeof rawData>();
    rawData.forEach((item) => {
      const dateKey = dayjs(item.TRADE_DT).format("YYYY-MM-DD");
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, []);
      }
      dateMap.get(dateKey)!.push(item);
    });

    const yData = tradeDates.map((tradeDate) => {
      const dailyData = dateMap.get(tradeDate) || [];

      if (dailyData.length === 0) return null;

      let sum = 0;
      let totalOI = 0;

      for (const cur of dailyData) {
        const oi = Number(cur.S_DQ_OI) || 0;
        const basis = Number(cur.annualizedBasis) || 0;
        sum += basis * oi;
        totalOI += oi;
      }

      return totalOI > 0 ? Number((sum / totalOI).toFixed(2)) : null;
    });

    return { xAxisData: tradeDates, yAxisData: yData };
  }

  async getIMHedgeBasisTrend3Y(tradeDate: string) {
    const rawData = await this.getFutures3YearsData("IM", tradeDate);
    const tradeDates = this.getSortedUniqueDates(rawData);

    const dateMap = new Map<string, typeof rawData>();
    rawData.forEach((item) => {
      const dateKey = dayjs(item.TRADE_DT).format("YYYY-MM-DD");
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, []);
      }
      dateMap.get(dateKey)!.push(item);
    });

    const yData = tradeDates.map((tradeDate) => {
      const dailyData = dateMap.get(tradeDate) || [];

      if (dailyData.length === 0) return null;

      let sum = 0;
      let totalOI = 0;

      for (const cur of dailyData) {
        const oi = Number(cur.S_DQ_OI) || 0;
        const basis = Number(cur.annualizedBasis) || 0;
        sum += basis * oi;
        totalOI += oi;
      }

      return totalOI > 0 ? Number((sum / totalOI).toFixed(2)) : null;
    });

    return { xAxisData: tradeDates, yAxisData: yData };
  }

  async getIFHedgeBasisTrend3Y(tradeDate: string) {
    const rawData = await this.getFutures3YearsData("IF", tradeDate);
    const tradeDates = this.getSortedUniqueDates(rawData);

    const dateMap = new Map<string, typeof rawData>();
    rawData.forEach((item) => {
      const dateKey = dayjs(item.TRADE_DT).format("YYYY-MM-DD");
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, []);
      }
      dateMap.get(dateKey)!.push(item);
    });

    const yData = tradeDates.map((tradeDate) => {
      const dailyData = dateMap.get(tradeDate) || [];

      if (dailyData.length === 0) return null;

      let sum = 0;
      let totalOI = 0;

      for (const cur of dailyData) {
        const oi = Number(cur.S_DQ_OI) || 0;
        const basis = Number(cur.annualizedBasis) || 0;
        sum += basis * oi;
        totalOI += oi;
      }

      return totalOI > 0 ? Number((sum / totalOI).toFixed(2)) : null;
    });

    return { xAxisData: tradeDates, yAxisData: yData };
  }

  async getIHHedgeBasisTrend3Y(tradeDate: string) {
    const rawData = await this.getFutures3YearsData("IH", tradeDate);
    const tradeDates = this.getSortedUniqueDates(rawData);

    const dateMap = new Map<string, typeof rawData>();
    rawData.forEach((item) => {
      const dateKey = dayjs(item.TRADE_DT).format("YYYY-MM-DD");
      if (!dateMap.has(dateKey)) {
        dateMap.set(dateKey, []);
      }
      dateMap.get(dateKey)!.push(item);
    });

    const yData = tradeDates.map((tradeDate) => {
      const dailyData = dateMap.get(tradeDate) || [];

      if (dailyData.length === 0) return null;

      let sum = 0;
      let totalOI = 0;

      for (const cur of dailyData) {
        const oi = Number(cur.S_DQ_OI) || 0;
        const basis = Number(cur.annualizedBasis) || 0;
        sum += basis * oi;
        totalOI += oi;
      }

      return totalOI > 0 ? Number((sum / totalOI).toFixed(2)) : null;
    });

    return { xAxisData: tradeDates, yAxisData: yData };
  }
  getCurrentWeekTradeDates(targetDate: string): string[] {
    const targetDayjs = dayjs(targetDate);
    const targetFormat = targetDayjs.format("YYYYMMDD");

    const targetIndex = this.sortedTradeDates.findIndex(
      (d) => d === targetFormat
    );
    if (targetIndex === -1) return [];

    const results: string[] = [];
    let currentIndex = targetIndex;

    const weekStart = targetDayjs.startOf("week").add(1, "day");

    while (currentIndex >= 0) {
      const currentDate = dayjs(
        this.sortedTradeDates[currentIndex],
        "YYYYMMDD"
      );

      if (currentDate.isBefore(weekStart)) break;

      results.push(currentDate.format("YYYY-MM-DD"));
      currentIndex--;
    }

    return results.reverse();
  }

  getCurrentMonthTradeDates(targetDate: string): string[] {
    const targetDayjs = dayjs(targetDate);
    const targetFormat = targetDayjs.format("YYYYMMDD");
    const targetMonth = targetDayjs.format("YYYYMM");

    const targetIndex = this.sortedTradeDates.findIndex(
      (d) => d === targetFormat
    );
    if (targetIndex === -1) return [];

    const results: string[] = [];
    let currentIndex = targetIndex;

    while (currentIndex >= 0) {
      const currentDateStr = this.sortedTradeDates[currentIndex];
      if (!currentDateStr.startsWith(targetMonth)) break;

      results.push(dayjs(currentDateStr).format("YYYY-MM-DD"));
      currentIndex--;
    }

    return results.reverse();
  }

  getCurrentYearTradeDates(targetDate: string): string[] {
    const targetDayjs = dayjs(targetDate);
    const targetYear = targetDayjs.format("YYYY");
    const targetFormat = targetDayjs.format("YYYYMMDD");

    const targetIndex = this.sortedTradeDates.findIndex(
      (d) => d === targetFormat
    );
    if (targetIndex === -1) return [];

    return this.sortedTradeDates
      .slice(0, targetIndex + 1)
      .filter((d) => d.startsWith(targetYear))
      .map((d) => dayjs(d).format("YYYY-MM-DD"));
  }

  async getWeeklyBasisAverageData(type: FuturesType, tradeDate: string) {}

  async getMonthlyBasisAverageData(type: FuturesType, tradeDate: string) {}

  async getYearsBasisAverageData(type: FuturesType, tradeDate: string) {}

  private async calculateBasisAverage(
    tradeDates: string[],
    type: FuturesType
  ): Promise<number | null> {
    const rawData = await this.getFuturesDataByDates(type, tradeDates);
    const dateMap = new Map<string, typeof rawData>();

    rawData.forEach((item) => {
      const dateKey = dayjs(item.TRADE_DT).format("YYYY-MM-DD");
      dateMap.has(dateKey) || dateMap.set(dateKey, []);
      dateMap.get(dateKey)!.push(item);
    });

    const yData = Array.from(dateMap.keys()).map((date) => {
      const dailyData = dateMap.get(date) || [];
      if (dailyData.length === 0) return null;

      let sum = 0,
        totalOI = 0;
      for (const cur of dailyData) {
        const oi = Number(cur.S_DQ_OI) || 0;
        const basis = Number(cur.annualizedBasis) || 0;
        sum += basis * oi;
        totalOI += oi;
      }
      return totalOI > 0 ? Number((sum / totalOI).toFixed(2)) : null;
    });

    const validValues = yData.filter((v) => v !== null) as number[];
    return validValues.length > 0
      ? Number(
          (validValues.reduce((a, b) => a + b, 0) / validValues.length).toFixed(
            2
          )
        )
      : null;
  }
  async getICWeekBasisAverage(tradeDate: string) {
    return this.calculateBasisAverage(
      this.getCurrentWeekTradeDates(tradeDate),
      "IC"
    );
  }

  async getICMonthBasisAverage(tradeDate: string) {
    return this.calculateBasisAverage(
      this.getCurrentMonthTradeDates(tradeDate),
      "IC"
    );
  }

  async getICYearBasisAverage(tradeDate: string) {
    return this.calculateBasisAverage(
      this.getCurrentYearTradeDates(tradeDate),
      "IC"
    );
  }

  async getIHWeekBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentWeekTradeDates(tradeData),
      "IH"
    );
  }

  async getIHMonthBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentMonthTradeDates(tradeData),
      "IH"
    );
  }

  async getIHYearBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentYearTradeDates(tradeData),
      "IH"
    );
  }

  async getIFWeekBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentWeekTradeDates(tradeData),
      "IF"
    );
  }

  async getIFMonthBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentMonthTradeDates(tradeData),
      "IF"
    );
  }

  async getIFYearBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentYearTradeDates(tradeData),
      "IF"
    );
  }

  async getIMWeekBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentWeekTradeDates(tradeData),
      "IM"
    );
  }

  async getIMMonthBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentMonthTradeDates(tradeData),
      "IM"
    );
  }

  async getIMYearBasisAverage(tradeData: string) {
    return this.calculateBasisAverage(
      this.getCurrentYearTradeDates(tradeData),
      "IM"
    );
  }

  async getIMBasisAverage(tradeDate: string) {}

  async getIFBasisAverage(tradeDate: string) {}

  async getIHBasisAverage(tradeDate: string) {}

  private async getWorkingDays() {
    const currentYear = new Date().getFullYear();
    const [tradeDays] = await this.db
      .select({ count: sql<number>`COUNT(*)` })
      .from(tradeDate.model)
      .where(sql`YEAR(${tradeDate.model.date}) = ${currentYear}`);
    return tradeDays?.count || 365;
  }

  private getSortedUniqueDates(data: any[]) {
    return [
      ...new Set(data.map((i) => dayjs(i.TRADE_DT).format("YYYY-MM-DD"))),
    ].sort((a, b) => dayjs(a).valueOf() - dayjs(b).valueOf());
  }

  private generateSeriesData(
    data: any[],
    workingDays: number,
    xAxisData: string[]
  ) {
    const seriesMap = new Map<string, any>();

    data.forEach((item) => {
      const series = seriesMap.get(item.S_INFO_WINDCODE) || {
        name: item.S_INFO_WINDCODE,
        type: "line",
        smooth: true,
        data: [],
      };
      seriesMap.set(item.S_INFO_WINDCODE, series);
    });

    return Array.from(seriesMap.values())
      .map((series) => ({
        ...series,
        data: this.generateSeriesPoints(data, series.name, 252, xAxisData),
      }))
      .sort((a, b) => {
        const aCode = a.name.match(/(\D+)(\d+)/) || [];
        const bCode = b.name.match(/(\D+)(\d+)/) || [];

        const typeOrder = { IC: 1, IH: 2, IF: 3, IM: 4 };
        const aType = aCode[1] || "";
        const bType = bCode[1] || "";

        if (aType !== bType)
          return (
            (typeOrder[aType as keyof typeof typeOrder] || 5) -
            (typeOrder[bType as keyof typeof typeOrder] || 6)
          );

        const aNum = parseInt(aCode[2] || "0", 10);
        const bNum = parseInt(bCode[2] || "0", 10);
        return aNum - bNum;
      });
  }

  private generateSeriesPoints(
    data: any[],
    seriesName: string,
    workingDays: number,
    xAxisData: string[]
  ) {
    const valueMap = new Map(
      data
        .filter((item) => item.S_INFO_WINDCODE == seriesName)
        .map((item) => [
          dayjs(item.TRADE_DT).format("YYYY-MM-DD"),
          this.calculateAnnualizedBasis(
            Number(item.S_DQ_CLOSE),
            item.spotPrice,
            item.delistDate,
            252,
            false,
            item.TRADE_DT
          ).toFixed(2),
        ])
    );

    return xAxisData.map((date) => {
      return valueMap.has(date) ? valueMap.get(date) : null;
    });
  }

  private buildMonthPattern(currentMonth: number): string {
    const paddedMonth = currentMonth.toString().padStart(2, "0");
    const firstDigit = paddedMonth[0];
    const secondDigit = paddedMonth[1];

    if (currentMonth >= 10) {
      return `1[${secondDigit}-2]`;
    } else if (currentMonth >= 1 && currentMonth <= 9) {
      const endDigit = firstDigit === "0" ? "9" : secondDigit;
      return `${firstDigit}[${secondDigit}-9]|1[0-2]`;
    }
    throw new Error("无效的月份数值");
  }
}

const serviceInstance = new AIndexFuturesService();
serviceInstance.initialize().catch((error) => {
  console.error("Failed to initialize trade dates:", error);
  process.exit(1);
});

export default serviceInstance;
