import Dysmsapi, * as $Dysmsapi from "@alicloud/dysmsapi20170525";
import * as $OpenApi from "@alicloud/openapi-client";
import { range } from "lodash";
import env from "../config/env";

class SmsService {
  private createClient() {
    const config = new $OpenApi.Config({
      accessKeyId: env.SMS_APP_ID,
      accessKeySecret: env.SMS_APP_SECRET,
    });
    config.endpoint = env.SMS_END_POINT;
    return new Dysmsapi(config);
  }

  async getVerifier(phone: string) {
    const code = range(0, 6)
      .map(() => Math.floor(Math.random() * 10))
      .reduce((acc, curr) => `${acc}${curr}`, "");

    const client = this.createClient();
    const smsRequest = new $Dysmsapi.SendSmsRequest({
      templateCode: env.SMS_VERIFIER_TEMPLATE_CODE,
      signName: env.SMS_SIGN_NAME,
      phoneNumbers: phone,
      templateParam: JSON.stringify({ code }),
    });

    await client.sendSms(smsRequest);
    return code;
  }
}

export default new SmsService();
