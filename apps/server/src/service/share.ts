import {
  SQL,
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  inArray,
  like,
  or,
  sql,
  sum,
} from "drizzle-orm";
import { MySqlSelect } from "drizzle-orm/mysql-core";
import { intersection, sumBy, uniq } from "lodash";
import { z } from "zod";
import { inputSchema } from "../model/share";
import * as dividend from "../schema/dividend";
import * as netValue from "../schema/netValue";
import * as disclosure from "../schema/netValueDisclosure";
import * as complianceConf from "../schema/portfolioComplianceConf";
import * as share from "../schema/share";
import * as trade from "../schema/trade";
import * as fileUpload from "../schema/fileUpload";
import * as portfolioOpenDayContent from "../schema/portfolioOpenDayContent";
import { convertDouble } from "../utils/db";
import { withPagination } from "../utils/pagniation";
import { Service } from "../utils/service";
import path from "path";
import env from "../config/env";
import Excel from "exceljs";
import { PassThrough } from "stream";
import crypto from "crypto";
import fs from "fs";
import dayjs from "dayjs";
import { createClient } from "webdav";

class ShareService extends Service {
  private holding = sql<
    number | null
  >`${share.model.realShares} * ${netValue.model.netValue}`;

  private select = {
    portfolioId: share.model.portfolioId,
    portfolioName: share.model.portfolioName,
    agency: share.model.agency,
    realShares: share.model.realShares,
    lastModify: share.model.lastModify,
    netValue: convertDouble(netValue.model.netValue),
    cumNetValue: convertDouble(netValue.model.cumNetValue),
    netValueDate: netValue.model.date,
    currentEarnings: share.model.currentEarnings,
    holding: this.holding,
  };

  async getList(input: z.infer<typeof inputSchema>, identityNumber: string) {
    let query = this.db
      .select({
        ...this.select,
        currentEarnings: sql<
          number | null
        >`if(${complianceConf.model.earningVisibility} like '%current%', ${this.select.currentEarnings}, null)`,
      })
      .from(share.model)
      .$dynamic();
    query = withPagination(query, input);
    query = this.withJoin(query);
    query = this.withCondition(query, input, identityNumber);
    query = this.withOrder(query, input);

    let amountQuery = this.db
      .select({ value: count() })
      .from(share.model)
      .$dynamic();
    amountQuery = this.withJoin(amountQuery);
    amountQuery = this.withCondition(amountQuery, input, identityNumber);

    const [queryResult, amountQueryResult] = await Promise.all([
      query,
      amountQuery,
    ]);

    return { items: queryResult, amount: amountQueryResult[0].value };
  }

  async getTotalAsset(identityNumber: string) {
    const select = {
      portfolioId: share.model.portfolioId,
      realShares: share.model.realShares,
      netValue: convertDouble(netValue.model.netValue),
    };

    let query = this.db.select(select).from(share.model).$dynamic();
    query = this.withJoin(query);
    query = this.withCondition(query, undefined, identityNumber);

    const result = await query;

    const asset = sumBy(result, ({ realShares, netValue }) => {
      if (!realShares || !netValue) return 0;
      return netValue * realShares;
    });

    const portfolioAmount = uniq(result.map((it) => it.portfolioId)).length;

    return { asset, portfolioAmount };
  }

  async getTotalEarnings(identityNumber: string) {
    // const [currentEarnings, cumulativeEarnings] = await Promise.all([
    //   this.getCurrentEarnings(identityNumber),
    //   this.getCumulativeEarnings(identityNumber),
    // ]);
    const currentEarnings = await this.getCurrentEarnings(identityNumber);
    return { currentEarnings, cumulativeEarnings: undefined };
  }

  private async getCurrentEarnings(identityNumber: string) {
    let query = this.db
      .select({
        value:
          sql`sum(if(${complianceConf.model.earningVisibility} like '%current%', ${share.model.currentEarnings}, null))`.mapWith(
            Number
          ),
      })
      .from(share.model)
      .$dynamic();

    query = this.withJoin(query);
    query = this.withCondition(query, undefined, identityNumber);

    const [result] = await query;
    return result.value;
  }

  private async getHolding(identityNumber: string) {
    let query = this.db
      .select({
        portfolioId: share.model.portfolioId,
        cumulativeEarnings:
          sql`if(${complianceConf.model.earningVisibility} like '%sum%', true, false)`.mapWith(
            Boolean
          ),
      })
      .from(share.model)
      .$dynamic();

    query = this.withJoin(query);
    query = this.withCondition(query, undefined, identityNumber);

    const result = await query;
    const portfolioIds = result
      .filter((it) => !!it.portfolioId && !!it.cumulativeEarnings)
      .map((it) => it.portfolioId) as number[];

    if (!portfolioIds.length) return [{ value: 0 }];

    let holdingQuery = this.db
      .select({
        value:
          sql`sum(${share.model.realShares} * ${netValue.model.netValue})`.mapWith(
            Number
          ),
      })
      .from(share.model)
      .where(
        and(
          eq(share.model.clientIdentityNumber, identityNumber),
          inArray(share.model.portfolioId, portfolioIds)
        )
      )
      .$dynamic();
    holdingQuery = this.withJoin(holdingQuery);

    return await holdingQuery;
  }

  private async getCumulativeEarnings(identityNumber: string) {
    const portfolioIds =
      await this.getCumulativeEarningsPortfolioIds(identityNumber);
    if (!portfolioIds?.length) return;

    const holdingQuery = this.getHolding(identityNumber);

    const subscribeQuery = this.db
      .select({ value: sum(trade.model.confBalance).mapWith(Number) })
      .from(trade.model)
      .where(
        and(
          eq(trade.model.clientIdentityNumber, identityNumber),
          inArray(trade.model.businessType, [
            "申购",
            "基金成立",
            "基金转换入",
            "非交易过户入",
          ]),
          inArray(trade.model.portfolioId, portfolioIds)
        )
      );

    const redeemQuery = this.db
      .select({ value: sum(trade.model.confBalance).mapWith(Number) })
      .from(trade.model)
      .where(
        and(
          eq(trade.model.clientIdentityNumber, identityNumber),
          inArray(trade.model.businessType, [
            "赎回",
            "强制赎回",
            "基金清盘",
            "基金转换出",
          ]),
          inArray(trade.model.portfolioId, portfolioIds)
        )
      );

    // const forceIncreaseQuery = this.db
    //   .select({
    //     value:
    //       sql`sum(${trade.model.confShare} * ${trade.model.transactionPrice})`.mapWith(
    //         Number
    //       ),
    //   })
    //   .from(trade.model)
    //   .where(
    //     and(
    //       eq(trade.model.clientIdentityNumber, identityNumber),
    //       eq(trade.model.businessType, "强制调增"),
    //       inArray(trade.model.portfolioId, portfolioIds)
    //     )
    //   );

    const dividendQuery = this.db
      .select({ value: sum(dividend.model.dividendIndeed).mapWith(Number) })
      .from(dividend.model)
      .where(
        and(
          eq(dividend.model.clientIdentityNumber, identityNumber),
          eq(dividend.model.dividendMethod, "现金红利"),
          inArray(dividend.model.portfolioId, portfolioIds)
        )
      );

    const [
      [holdingQueryResult],
      [subscribeQueryResult],
      [redeemQueryResult],
      // [forceIncreaseQueryResult],
      [dividendQueryResult],
    ] = await Promise.all([
      holdingQuery,
      subscribeQuery,
      redeemQuery,
      // forceIncreaseQuery,
      dividendQuery,
    ]);

    return (
      holdingQueryResult.value +
      redeemQueryResult.value +
      dividendQueryResult.value -
      subscribeQueryResult.value
      // - forceIncreaseQueryResult.value
    );
  }

  private async getCumulativeEarningsPortfolioIds(identityNumber: string) {
    const tradeQuery = this.db
      .select({ portfolioId: trade.model.portfolioId })
      .from(trade.model)
      .where(eq(trade.model.clientIdentityNumber, identityNumber));

    const dividendQuery = this.db
      .select({ portfolioId: dividend.model.portfolioId })
      .from(dividend.model)
      .where(eq(dividend.model.clientIdentityNumber, identityNumber));

    const portfolioIdsQueryResult = await tradeQuery.union(dividendQuery);

    const portfolioIds = portfolioIdsQueryResult
      .map((it) => it.portfolioId)
      .filter(Boolean) as number[];

    if (!portfolioIds.length) return [];

    const complianceQuery = this.db
      .select({ portfolioId: complianceConf.model.portfolioId })
      .from(complianceConf.model)
      .where(
        and(
          inArray(complianceConf.model.portfolioId, portfolioIds),
          like(complianceConf.model.earningVisibility, "%sum%")
        )
      );

    const disclosureQuery = this.db
      .select({ portfolioId: disclosure.shareModel.portfolioId })
      .from(disclosure.shareModel)
      .where(inArray(disclosure.shareModel.portfolioId, portfolioIds));

    const [complianceQueryResult, disclosureQueryResult] = await Promise.all([
      complianceQuery,
      disclosureQuery,
    ]);

    return intersection(
      complianceQueryResult.map((it) => it.portfolioId),
      disclosureQueryResult.map((it) => it.portfolioId)
    );
  }

  private withJoin<T extends MySqlSelect>(query: T) {
    query
      .leftJoin(
        complianceConf.model,
        eq(share.model.portfolioId, complianceConf.model.portfolioId)
      )
      .leftJoin(
        disclosure.shareModel,
        eq(share.model.portfolioId, disclosure.shareModel.portfolioId)
      )
      .leftJoin(
        netValue.model,
        and(
          eq(disclosure.shareModel.portfolioId, netValue.model.portfolioId),
          eq(disclosure.shareModel.netValueDate, netValue.model.date)
        )
      );

    return query;
  }

  private withCondition<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>,
    identityNumber: string
  ) {
    const complianceCondition = or(
      inArray(complianceConf.model.visibility, ["all", "hold"]),
      and(
        eq(complianceConf.model.visibility, "holding"),
        gt(share.model.realShares, 0)
      )
    );
    if (!complianceCondition) {
      query.where(sql`false`);
      return query;
    }

    const conditions: SQL[] = [
      eq(share.model.clientIdentityNumber, identityNumber),
      complianceCondition,
    ];

    const portfolioParsed = z.string().safeParse(input?.portfolio);
    if (portfolioParsed.success) {
      const value = `%${portfolioParsed.data}%`;
      const predicate = or(
        like(share.model.portfolioName, value),
        like(share.model.portfolioNo, value)
      );

      if (predicate) {
        conditions.push(predicate);
      }
    }

    query.where(and(...conditions));

    return query;
  }

  private withOrder<T extends MySqlSelect>(
    query: T,
    input: z.infer<typeof inputSchema>
  ) {
    switch (input?.sortKey) {
      case "currentEarnings":
        return query.orderBy(desc(share.model.currentEarnings));
      case "holding":
        return query.orderBy(desc(this.holding));
      case "portfolioName":
        return query.orderBy(asc(share.model.portfolioName));

      default:
        return query.orderBy(desc(share.model.currentEarnings));
    }
  }

  async getAgencySharsByIdentityNumber(identityNumber: string) {
    const shares = await this.db
      .select()
      .from(share.model)
      .where(eq(share.model.clientIdentityNumber, identityNumber));
    return shares.filter((it) => !it.channelPerson && it.agency != "直销");
  }

  async getSharesByIdentityNumber(identityNumber: string) {
    const shares = await this.db
      .select()
      .from(share.model)
      .where(eq(share.model.clientIdentityNumber, identityNumber));
    return shares.filter((it) => !it.channelPerson);
  }

  async getSharesByIdentityNumberAddOpenDay(identityNumber: string) {
    const shares = await this.db
      .select({
        ...share.select,
        openDayContent: portfolioOpenDayContent.model.openDayContent,
      })
      .from(share.model)
      .leftJoin(
        portfolioOpenDayContent.model,
        eq(share.model.portfolioName, portfolioOpenDayContent.model.name)
      )
      .where(eq(share.model.clientIdentityNumber, identityNumber));

    return shares.filter((it) => !it.channelPerson);
  }

  // async downLoadDividendCalculator(
  //   productName: string,
  //   investorName: string
  // ) {
  //   const templatePath = path.resolve(
  //     env.FILE_UPLOAD_DIR,
  //     "衍复博裕沪深300指数增强一号A期私募证券投资基金分红试算.xlsx"
  //   );
  //   const workbook = new Excel.Workbook();
  //   await workbook.xlsx.readFile(templatePath);

  //   // 3. 填充数据到 Excel
  //   const worksheet = workbook.worksheets[0];
  //   if (!worksheet) {
  //     throw new Error("Excel文件中未找到任何工作表");
  //   }

  //   const clientNameCol = worksheet.getRow(1).values?.findIndex(
  //     (value:any) => value?.toString()?.includes("客户名称")
  //   );
  //   if (clientNameCol == -1) {
  //    throw new Error("客户名称列缺少了");
  //   }

  //   const rowsToKeep = [worksheet.getRow(1)];
  //   for (let i = 2;i<worksheet.rowCount;i++){
  //     const row = worksheet.getRow(i);
  //     if(row.getCell(clientNameCol).text == investorName){
  //       rowsToKeep.push(row);
  //     }
  //   }
  //   worksheet.getCell("B2").value = "testData";
  //   worksheet.getCell("B3").value = "testData1";
  //   // 添加更多数据填充逻辑...

  //   // 4. 创建内存流
  //   const stream = new PassThrough();
  //   await workbook.xlsx.write(stream);
  //   stream.end();

  //   const hash = crypto.createHash("sha256");
  //   const fileBuffer = await workbook.xlsx.writeBuffer();
  //   hash.update(fileBuffer);
  //   const fileHash = hash.digest("hex");

  //   // 确保存储目录存在
  //   fs.mkdirSync(env.FILE_UPLOAD_DIR, { recursive: true });

  //   // 保存文件到指定路径
  //   const filePath = path.resolve(env.FILE_UPLOAD_DIR, fileHash);
  //   await fs.promises.writeFile(filePath, fileBuffer);

  //   // 生成唯一文件名
  //   const uniqueName = `分红计算器-查询模式明细模板_${Date.now()}.xlsx`;

  //   // 写入数据库
  //   await this.db.insert(fileUpload.model).values({
  //     id: fileHash,
  //     name: uniqueName,
  //     createTime: new Date(),
  //   });

  //   return fileHash;
  //   // return productName
  // }

  async downLoadDividendCalculator(productName: string, investorName: string) {
    try {
      const client = createClient("https://dav.jianguoyun.com/dav", {
        username: env.WEBDAV_USERNAME,
        password: env.WEBDAV_PASSWORD,
      });

      const response = await client.getDirectoryContents(
        "/市场小小组/分红计算器-分红明细/本次分红"
      );
      const remoteFiles = "data" in response ? response.data : response;

      const templateFile = remoteFiles.find((f) =>
        f.filename.includes(productName)
      );

      if (!templateFile) {
        console.log("出现新的错误？");

        throw new Error(`未找到产品【${productName}】对应的分红试算模板文件`);
      }

      const rawFileBuffer = await client.getFileContents(
        templateFile.filename,
        { format: "binary" }
      );

      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(rawFileBuffer as Buffer);

      // const worksheet = workbook.getWorksheet("Sheet1");
      const worksheetNames = ["份额明细-高水位", "份额明细-超额计提"];
      const worksheet = worksheetNames
        .map((name) => workbook.getWorksheet(name))
        .find((ws) => !!ws);
      if (!worksheet) {
        throw new Error("Excel文件中未找到任何工作表");
      }

      const headerRow = worksheet.getRow(1);
      const clientNameCol = this.findClientNameColumn(headerRow);

      const columnsToRemove = new Set(["减仓金额"]);

      const filteredRows = this.filterRows(
        worksheet,
        clientNameCol,
        investorName
      );

      const newWorkbook = new Excel.Workbook();
      const newWorksheet = newWorkbook.addWorksheet("筛选数据");

      this.cloneHeaderStyle(headerRow, newWorksheet);

      filteredRows.forEach((row, rowIndex) => {
        if (rowIndex === 0) return;

        // const rowValues = row.values as Excel.CellValue[];

        // const filteredValues = rowValues.reduce((acc, value, index) => {
        //   if (index === 0) return acc;
        //   const headerText = headerRow.getCell(index).text?.trim();
        //   if (!columnsToRemove.has(headerText)) {
        //     acc.push(value);
        //   }
        //   return acc;
        // }, [] as Excel.CellValue[]);

        // newWorksheet.addRow(filteredValues);

        const newRow = newWorksheet.addRow([]);

        let validColIndex = 1;
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const headerText = headerRow.getCell(colNumber).text?.trim();
          if (!columnsToRemove.has(headerText)) {
            const newCell = newRow.getCell(validColIndex);

            if (
              [
                "红利再投份额",
                "每单位分红金额",
                "计提业绩报酬金额",
                "现金分红或红利再投金额",
              ].includes(headerText)
            ) {
              const numericValue = parseFloat(cell.text || "0").toFixed(2);
              newCell.value = numericValue;
              newCell.numFmt = "0.00";
            } else if (headerText === "业绩报酬计提比例") {
              const rateValue = parseFloat(cell.text || "0") * 100 + "%";
              newCell.value = rateValue;
              newCell.numFmt = "0.00%";
            } else if (headerText?.includes("率")) {
              // const numericValue = parseFloat(
              //   cell.text?.replace("%", "") || "0"
              // );
              // newCell.value = `${(numericValue * 100).toFixed(2)}%`;

              const rawValue = parseFloat(cell.text?.replace("%", "") || "0");
              newCell.value = rawValue;
              newCell.numFmt = "0.00%";
            } else if (headerText == "本次业绩报酬计提基准日") {
              const formatDate = dayjs(cell.text).format("YYYY-MM-DD");
              newCell.value = formatDate;
            } else {
              newCell.value = cell.text;
            }

            newCell.style = { ...cell.style };
            validColIndex++;
          }
        });
      });

      const { fileBuffer, fileHash } = await this.generateFileInfo(newWorkbook);

      const newFileName = this.generateFileName(templateFile.basename);
      const savedPath = await this.saveFile(fileBuffer as Buffer, fileHash);

      await this.recordFileMetadata({
        fileHash,
        fileName: newFileName,
        filePath: savedPath,
      });

      return fileHash;
    } catch (error) {
      console.error("[EXCEL生成错误]", error);
      throw new Error(`文件生成失败`);
    }
  }

  private findClientNameColumn(headerRow: Excel.Row): number {
    const nameVariations = ["客户名称", "客户名", "Client Name", "Investor"];
    for (let col = 1; col <= headerRow.cellCount; col++) {
      const cellValue = headerRow.getCell(col).text?.toLowerCase();
      if (nameVariations.some((v) => cellValue?.includes(v.toLowerCase()))) {
        return col;
      }
    }
    throw new Error("未找到客户名称列，请检查模板格式");
  }

  private filterRows(
    worksheet: Excel.Worksheet,
    column: number,
    investorName: string
  ): Excel.Row[] {
    const filtered = [worksheet.getRow(1)]; // 保留表头
    for (let rowNum = 2; rowNum <= worksheet.rowCount; rowNum++) {
      const row = worksheet.getRow(rowNum);
      const cell = row.getCell(column);
      if (cell.text?.trim() === investorName.trim()) {
        filtered.push(row);
      }
    }
    if (filtered.length === 1) {
      throw new Error("未找到匹配的投资者数据");
    }
    return filtered;
  }

  private cloneHeaderStyle(sourceRow: Excel.Row, targetSheet: Excel.Worksheet) {
    const columnsToRemove = new Set(["减仓金额"]);
    let validColIndex = 1;
    sourceRow.eachCell((sourceCell, colNumber) => {
      const headerText = sourceCell.text?.trim();
      if (!columnsToRemove.has(headerText)) {
        const targetCell = targetSheet.getRow(1).getCell(validColIndex);

        // if (sourceCell.formula) {
        //   targetCell.value = {
        //     formula: sourceCell.formula,
        //     sharedFormula: false, // 禁用共享公式
        //   };
        // } else {
        //   targetCell.value = sourceCell.value;
        // }

        // targetCell.numFmt = sourceCell.numFmt;

        // targetCell.numFmt = sourceCell.numFmt
        targetCell.value = sourceCell.value;
        targetCell.style = { ...sourceCell.style };
        validColIndex++;
      }
    });
  }

  private async generateFileInfo(workbook: Excel.Workbook) {
    const fileBuffer = await workbook.xlsx.writeBuffer();
    const hash = crypto
      .createHash("sha256")
      .update(fileBuffer as Buffer)
      .digest("hex");
    return { fileBuffer, fileHash: hash };
  }

  private generateFileName(templatePath: string): string {
    const originalName = path.basename(templatePath, ".xlsx");
    const timestamp = dayjs().format("YYYYMMDD-HHmmss");
    const randomStr = Math.random().toString(36).substr(2, 6);
    return `${originalName}_${timestamp}_${randomStr}.xlsx`;
  }

  private async saveFile(buffer: Buffer, fileHash: string) {
    const savePath = path.resolve(env.FILE_UPLOAD_DIR, fileHash);
    await fs.promises.writeFile(savePath, buffer);
    return savePath;
  }

  private async recordFileMetadata(record: {
    fileHash: string;
    fileName: string;
    filePath: string;
  }) {
    await this.db.insert(fileUpload.model).values({
      id: record.fileHash,
      name: record.fileName,
    });
  }

  async updateShare(params: {
    shareId: number;
    portfolioName: string;
    agency: string;
    region?: string | null;
    department?: string | null;
    channelPerson: string;
  }) {
    const res = await this.db
      .update(share.model)
      .set({
        region: params.region ?? null,
        department: params.department ?? null,
        channelPerson: params.channelPerson,
      })
      .where(eq(share.model.id, params.shareId));
  }

  async getDividedDataByProductName(
    productName: string,
    identityNumber: string
  ) {
    const records = await this.db
      .select({
        dividendDate: dividend.model.dividendDate,
        agency: dividend.model.agency,
      })
      .from(dividend.model)
      .where(
        and(
          eq(dividend.model.clientIdentityNumber, identityNumber),
          eq(dividend.model.portfolioName, productName)
        )
      );

    const dates = [...new Set(records.map((r) => r.dividendDate))].filter(
      Boolean
    );
    const agencies = [...new Set(records.map((r) => r.agency))].filter(Boolean);

    return {
      dates: dates.sort((a, b) => (dayjs(a).isBefore(b) ? 1 : -1)),
      agencies,
    };
  }

  async checkDividendRecordExistence(
    productName: string,
    investorName: string
  ) {
    try {
      const client = createClient("https://dav.jianguoyun.com/dav", {
        username: env.WEBDAV_USERNAME,
        password: env.WEBDAV_PASSWORD,
      });

      const response = await client.getDirectoryContents(
        "/市场小小组/分红计算器-分红明细/本次分红"
      );
      const remoteFiles = "data" in response ? response.data : response;

      const templateFile = remoteFiles.find(
        (f) => f.filename.includes(productName) && f.filename.endsWith(".xlsx")
      );

      if (!templateFile) return false;

      const fileBuffer = (await client.getFileContents(templateFile.filename, {
        format: "binary",
      })) as Buffer;

      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(fileBuffer);

      // const worksheet = workbook.getWorksheet("份额明细-高水位") || workbook.getWorksheet("份额明细-超额计提");
      const worksheetNames = ["份额明细-高水位", "份额明细-超额计提"];
      const worksheet = worksheetNames
        .map((name) => workbook.getWorksheet(name))
        .find((ws) => !!ws);
      if (!worksheet) return false;

      const headerRow = worksheet.getRow(1);
      const clientNameCol = this.findClientNameColumn(headerRow);

      for (let rowNum = 2; rowNum <= worksheet.rowCount; rowNum++) {
        const row = worksheet.getRow(rowNum);
        const cellValue = row.getCell(clientNameCol).text?.trim();
        if (cellValue === investorName.trim()) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("[分红记录检查失败]", error);
      return false;
    }
  }
}

export default new ShareService();
