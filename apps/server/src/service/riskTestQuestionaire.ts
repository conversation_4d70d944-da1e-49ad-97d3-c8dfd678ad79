import {
  Answers,
  Level,
  Type,
  answersSchema,
  typeSchema,
} from "@portfolio-service/schema/riskTestQuestionaire";
import { TRPCError } from "@trpc/server";
import { desc, eq } from "drizzle-orm";
import { first, sum } from "lodash";
import { model, select } from "../schema/riskTestQuestionaire";
import { Service } from "../utils/service";
import userService from "./user";

class RiskTestQuestionaireService extends Service {
  async getById(id: number) {
    const predicate = eq(model.id, id);
    const [questionaire] = await this.db
      .select(select)
      .from(model)
      .where(predicate);

    if (!questionaire) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }

    return questionaire;
  }

  async getLatest(userId: number) {
    const user = await userService.getById(userId);
    const typeParsed = typeSchema.safeParse(user.type);
    if (!typeParsed.success) {
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    return this.getLatestByType(typeParsed.data);
  }

  async getLatestByType(type: Type) {
    const result = await this.db
      .select(select)
      .from(model)
      .where(eq(model.type, type))
      .orderBy(desc(model.createTime))
      .limit(1);

    return first(result);
  }

  async create() {}

  async getScoreAndLevel(id: number, answers: Answers) {
    const questionaire = await this.getById(id);
    const answersParsed = this.parseAnswers(answers);

    const scores = answersParsed.map((answer) => {
      const config = questionaire.config.find(
        ({ id }) => id === answer.questionId
      );
      if (!config) {
        throw new TRPCError({ code: "BAD_REQUEST", message: "问题不存在" });
      }

      const option = config?.options?.find(({ id }) => id === answer.optionId);
      if (!option) {
        throw new TRPCError({ code: "BAD_REQUEST", message: "答案不存在" });
      }

      return option.score;
    });

    const score = sum(scores);
    const level = this.getLevel(score);

    return { score, level };
  }

  private parseAnswers(value: unknown) {
    const answersParsed = answersSchema.safeParse(value);
    if (!answersParsed.success) {
      throw new TRPCError({ code: "BAD_REQUEST", message: "答案格式有误" });
    }

    return answersParsed.data;
  }

  private getLevel(score: number) {
    const scoreSanitized = Math.max(0, Math.min(100, score));

    let level = 1;
    if (scoreSanitized > 30 && scoreSanitized <= 45) {
      level = 2;
    } else if (scoreSanitized > 45 && scoreSanitized <= 60) {
      level = 3;
    } else if (scoreSanitized > 60 && scoreSanitized <= 75) {
      level = 4;
    } else if (scoreSanitized > 75) {
      level = 5;
    }

    return `C${level}` as Level;
  }
}

export default new RiskTestQuestionaireService();
