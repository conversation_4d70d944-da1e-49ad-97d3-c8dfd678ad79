import dayjs, { Dayjs } from "dayjs";
import service from "../service/tradeDate";

type TradeDateStorage = {
  value: Set<string>;
  dayjsList: Dayjs[];
};

export const tradeDateStorage: TradeDateStorage = {
  value: new Set(),
  dayjsList: [],
};

const refreshTradeDate = async () => {
  try {
    const response = await service.getList();
    tradeDateStorage.value = new Set(response);
    tradeDateStorage.dayjsList = response.map(dayjs);

    setTimeout(refreshTradeDate, 60 * 60 * 1000);
  } catch {
    setTimeout(refreshTradeDate, 30 * 60 * 1000);
  }
};

refreshTradeDate();
