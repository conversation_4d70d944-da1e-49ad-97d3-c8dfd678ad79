import env from "../config/env";
import axios from "axios";

type WechatJsapiTicketStorage = {
  value: string | undefined;
};

export const wechatJsapiTicketStorage: WechatJsapiTicketStorage = {
  value: undefined,
};

type WechatJsapiTicketResponse = {
  ticket: string;
  expires_in: number;
};

const refreshJsapiTicket = async () => {
  try {
    const response = await axios.get<WechatJsapiTicketResponse>(
      "https://api.weixin.qq.com/cgi-bin/ticket/getticket",
      {
        params: { access_token: wechatAccessTokenStorage.value, type: "jsapi" },
      }
    );

    wechatJsapiTicketStorage.value = response.data.ticket;

    setTimeout(refreshJsapiTicket, (response.data.expires_in - 900) * 1000);
  } catch {
    setTimeout(refreshJsapiTicket, 30 * 1000);
  }
};

type WechatAccessTokenStorage = {
  value: string | undefined;
};

export const wechatAccessTokenStorage: WechatAccessTokenStorage = {
  value: undefined,
};

type WechatAccessTokenResponse = {
  access_token: string;
  expires_in: number;
};

const refreshAccessToken = async () => {
  if (!env.WX_APP_ID || !env.WX_APP_SECRET) return;

  try {
    const response = await axios.get<WechatAccessTokenResponse>(
      "https://api.weixin.qq.com/cgi-bin/token",
      {
        params: {
          grant_type: "client_credential",
          appid: env.WX_APP_ID,
          secret: env.WX_APP_SECRET,
        },
      }
    );

    const initialized = !!wechatAccessTokenStorage.value;
    wechatAccessTokenStorage.value = response.data.access_token;

    if (!initialized) refreshJsapiTicket();

    setTimeout(refreshAccessToken, (response.data.expires_in - 900) * 1000);
  } catch {
    setTimeout(refreshAccessToken, 30 * 1000);
  }
};

refreshAccessToken();
