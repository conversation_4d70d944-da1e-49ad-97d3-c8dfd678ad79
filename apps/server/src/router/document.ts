import { z } from "zod";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";
import service from "../service/document";
import { listInputSchema } from "../model/document";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  byIdInternal: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getById(input.id)),

  byId: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ ctx, input }) => service.getById(input.id, ctx.user.id)),

  ttdFileIdById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ ctx, input }) => service.getTtdFileIdById(input.id, ctx.user.id)),

  ttdLinkById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ ctx, input }) => service.getTtdLinkById(input.id, ctx.user.id)),

  ttdLinkByIdPrivate: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdLinkById(input.id)),
});
