import { z } from "zod";
import { updateInputSchema } from "../model/taxDeclaration";
import service from "../service/taxDeclaration";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  own: protectedProcedure.query(({ ctx }) => service.getByUserId(ctx.user.id)),

  byUserId: privateProcedure
    .input(z.object({ userId: z.number() }))
    .query(({ input }) => service.getByUserId(input.userId)),

  update: protectedProcedure
    .input(updateInputSchema)
    .mutation(({ ctx, input }) => service.update(ctx.user.id, input)),
});
