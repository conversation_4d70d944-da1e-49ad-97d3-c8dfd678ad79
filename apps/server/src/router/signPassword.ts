import { z } from "zod";
import service from "../service/signPassword";
import {
  identityVerifiedProcedure,
  protectedProcedure,
  router,
} from "../utils/trpc";

export default router({
  exists: protectedProcedure.query(async ({ ctx }) =>
    service.exists(ctx.user.id)
  ),

  create: identityVerifiedProcedure
    .input(z.object({ password: z.string().trim() }))
    .mutation(async ({ ctx, input }) =>
      service.create(ctx.user.id, input.password)
    ),
});
