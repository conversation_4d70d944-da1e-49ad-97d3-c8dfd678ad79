import { eq } from "drizzle-orm";
import type { Request<PERSON>and<PERSON> } from "express";
import fs from "fs";
import path from "path";
import { db } from "../config/db";
import env from "../config/env";
import * as fileUpload from "../schema/fileUpload";
import { isDevEnv } from "../utils/dev";

const staticRoute: RequestHandler = (req, res, next) => {
  if (req.path != "/api/static") {
    return next();
  }

  if (!isDevEnv()) {
    const { secret } = req.headers;
    const isPrivateAccess = !!env.SECRET && secret === env.SECRET;
    const isProtectedAccess = !!req.session.user;
    if (!isPrivateAccess && !isProtectedAccess) {
      return res.status(404).send("Not Found");
    }
  }

  const id = req.query.id?.toString();
  if (!id) {
    return res.status(400).send('"id" is empty.');
  }

  const errorHandler = (err: unknown) => {
    console.error(err);
    res.status(500).send("Download failed: " + err);
  };

  db.select()
    .from(fileUpload.model)
    .where(eq(fileUpload.model.id, id))
    .then(([result]) => {
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${encodeURIComponent(result.name)}"`
      );
      const stream = fs
        .createReadStream(path.resolve(env.FILE_UPLOAD_DIR, result.id))
        .on("error", errorHandler);
      stream.pipe(res).on("error", errorHandler);
    })
    .catch(errorHandler);
};

export default staticRoute;
