import { z } from "zod";
import { inputSchema } from "../model/portfolio";
import service from "../service/portfolio";
import calPortfolioService from "../service/portfolioCalculator";
import {
  privateProcedure,
  protectedProcedure as procedure,
  router,
} from "../utils/trpc";
import dayjs from "dayjs";
import { CalcProps } from "../model/portfolio";

export default router({
  list: procedure
    .input(inputSchema)
    .query(({ input, ctx }) =>
      service.getDetailList(input, ctx.user.identityNumber)
    ),

  baseList: privateProcedure.query(() => service.getBaseList()),

  byId: procedure
    .input(z.number())
    .query(({ input, ctx }) => service.getById(input, ctx.user.identityNumber)),

  byIdNoAccess: procedure
    .input(z.number())
    .query(({ input }) => service.getByIdNoAccess(input)),

  calData: procedure
    .input(
      z.object({
        selectedProductId: z.number(),
        startDate: z.object({
          type: z.string(),
          date: z.string().optional(),
        }),
        endDate: z.string(),
        intervalType: z.string(),
        valueType: z.string(),
        indexType:z.string()
      })
    )
    .mutation(({ ctx, input }) => calPortfolioService.calculate(input)),

  
});
