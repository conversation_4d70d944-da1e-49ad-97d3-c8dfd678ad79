import { createInputSchema, listInputSchema } from "../model/bankCard";
import service from "../service/bankCard";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: protectedProcedure.query(({ ctx }) =>
    service.getListByUserId(ctx.user.id)
  ),

  listInternal: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  create: protectedProcedure
    .input(createInputSchema)
    .mutation(({ ctx, input }) => service.create(ctx.user.id, input)),
});
