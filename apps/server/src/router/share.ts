import { inputSchema } from "../model/share";
import service from "../service/share";
import {
  logedInProcedure,
  protectedProcedure as procedure,
  router,
} from "../utils/trpc";
import z from "zod";
export default router({
  list: procedure
    .input(inputSchema)
    .query(({ input, ctx }) => service.getList(input, ctx.user.identityNumber)),

  totalAsset: procedure.query(({ ctx }) =>
    service.getTotalAsset(ctx.user.identityNumber)
  ),

  totalEarnings: procedure.query(({ ctx }) =>
    service.getTotalEarnings(ctx.user.identityNumber)
  ),

  getAgencySharesByIdentityNumber: procedure.query(({ ctx }) =>
    service.getAgencySharsByIdentityNumber(ctx.user.identityNumber)
  ),

  getSharesByIdentityNumber: procedure.query(({ ctx }) =>
    service.getSharesByIdentityNumber(ctx.user.identityNumber)
  ),

  getSharesByIdentityNumberAddOpenDay:procedure.query(({ ctx })=>service.getSharesByIdentityNumberAddOpenDay(ctx.user.identityNumber)) ,

  downLoadDividendCalculator: procedure.input(z.string()).mutation(({ ctx,input}) => service.downLoadDividendCalculator(input,ctx.user.name)),

  getDividedDataByProductName: procedure.input(z.string()).mutation(({ ctx,input}) => service.getDividedDataByProductName(input,ctx.user.identityNumber)),

  updateShareList: procedure
    .input(
      z.object({
        shareId: z.number(),
        portfolioName: z.string(),
        agency: z.string(),
        region: z.string().nullish(),
        department: z.string().nullish(),
        channelPerson: z.string(),
      })
    )
    .mutation(({ input, ctx }) => service.updateShare(input)),

  checkDividendRecordExistence:procedure.input(z.string()).query(({ctx,input})=>service.checkDividendRecordExistence(input,ctx.user.name)),
});
