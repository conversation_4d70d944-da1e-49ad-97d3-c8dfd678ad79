import { z } from "zod";
import {
  createInputSchema,
  nextInputSchema,
  reviewInputSchema,
} from "../model/tradeApplyProcess";
import service from "../service/tradeApplyProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  listByIdentityNumber: privateProcedure
    .input(z.object({ identityNumber: z.string() }))
    .query(({ input }) =>
      service.getListByIdentityNumber(input.identityNumber)
    ),

  documentsById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsWithTtdFilesById(input.id)),

  ttdFileIdsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdFileIdsById(input.id)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  tradeApplySign: protectedProcedure
    .input(nextInputSchema)
    .mutation(({ ctx, input }) => service.documentsSign(ctx.user.id, input)),

  tradeApplyReview: privateProcedure
    .input(reviewInputSchema)
    .mutation(({ input }) => service.documentsReview(input)),
});
