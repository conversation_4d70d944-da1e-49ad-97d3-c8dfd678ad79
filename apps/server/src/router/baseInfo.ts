import { z } from "zod";
import { updateInputSchema } from "../model/baseInfo";
import service from "../service/baseInfo";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  own: protectedProcedure.query(({ ctx }) => service.getByUserId(ctx.user.id)),

  byUserId: privateProcedure
    .input(z.object({ userId: z.number() }))
    .query(({ input }) => service.getByUserId(input.userId)),

  byUserIds: privateProcedure
    .input(z.object({ userIds: z.array(z.number()) }))
    .mutation(({ input }) => service.getByUserIds(input.userIds)),

  update: protectedProcedure
    .input(updateInputSchema)
    .mutation(({ ctx, input }) => service.update(ctx.user.id, input)),
});
