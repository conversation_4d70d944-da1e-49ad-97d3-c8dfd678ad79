import { z } from "zod";
import {
  createInputSchema,
  createWordInputSchema,
  updateWordsInputSchema,
} from "../model/videoConfig";
import service from "../service/videoConfig";
import ttdService from "../service/ttd";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  linkByTargetCode: protectedProcedure
    .input(z.object({ targetCode: z.string() }))
    .query(({ input }) => service.getLinkByTargetCode(input.targetCode)),

  wordsList: privateProcedure.query(() => service.getWordsList()),

  videoList: privateProcedure
    .input(z.object({ targetCode: z.string().trim() }))
    .query(({ input }) => ttdService.queryVideo(input.targetCode)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  createWords: privateProcedure
    .input(createWordInputSchema)
    .mutation(({ input }) => service.createWords(input)),

  updateWords: privateProcedure
    .input(updateWordsInputSchema)
    .mutation(({ input }) => service.updateWords(input)),
});
