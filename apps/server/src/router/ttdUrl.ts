import {
  batchSignUrlInputSchema,
  faceRecognitionUrlInputSchema,
  orderSignUrlInputSchema,
  signUrlInputSchema,
} from "../model/ttdUrl";
import service from "../service/ttdUrl";
import { protectedProcedure as procedure, router } from "../utils/trpc";

export default router({
  signUrl: procedure
    .input(signUrlInputSchema)
    .query(async ({ ctx, input }) => service.getSignUrl(input, ctx.user.id)),

  batchSignUrl: procedure
    .input(batchSignUrlInputSchema)
    .query(async ({ ctx, input }) =>
      service.getBatchSignUrl(input, ctx.user.id)
    ),

  orderSignUrl: procedure
    .input(orderSignUrlInputSchema)
    .query(async ({ ctx, input }) =>
      service.getOrderSignUrl(input, ctx.user.id)
    ),

  faceRecognitionUrl: procedure
    .input(faceRecognitionUrlInputSchema)
    .query(async ({ ctx, input }) =>
      service.getFaceRecognitionUrl(input, ctx.user.id)
    ),
});
