import { z } from "zod";
import {
  listInputSchema,
  registerIdentityValidateInputSchema,
  registerInputSchema,
  sendCodeInputSchema,
} from "../model/register";
import service from "../service/register";
import {
  privateProcedure,
  publicProcedure,
  registerProcedure,
  router,
} from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  generateCode: privateProcedure
    .input(z.object({ id: z.number() }))
    .mutation(({ input }) => service.generateCode(input.id)),

  redoIdentityVerification: privateProcedure
    .input(z.object({ id: z.number() }))
    .mutation(({ input }) => service.redoIdentityVerification(input.id)),

  user: publicProcedure.query(async ({ ctx }) => {
    const { id } = ctx.req.session.registerUser || {};
    if (id == null) {
      return;
    }

    const user = await service.getByIdSafe(id);
    if (!user) {
      return;
    }

    const { validationCode, ...rest } = user;
    return rest;
  }),

  identityValidate: publicProcedure
    .input(registerIdentityValidateInputSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await service.registerIdentityValidate(input);
      await new Promise<void>((resolve) => {
        ctx.req.session.regenerate(() => {
          ctx.req.session.registerUser = result;
          ctx.req.session.save(resolve);
        });
      });
    }),

  codeValidate: registerProcedure
    .input(z.object({ code: z.string().min(8).trim() }))
    .mutation(async ({ input, ctx }) => {
      await service.registerCodeValidate(input, ctx.registerUser);
      await new Promise<void>((resolve) => {
        ctx.req.session.registerUser!.validationStatus = true;
        ctx.req.session.save(resolve);
      });
    }),

  sendCode: registerProcedure
    .input(sendCodeInputSchema)
    .mutation(async ({ ctx, input }) => {
      const code = await service.sendCode(input);
      await new Promise<void>((resolve) => {
        ctx.req.session.registerValidate = { ...input, code };
        ctx.req.session.save(resolve);
      });
    }),

  register: registerProcedure
    .input(registerInputSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await service.register(
        input,
        ctx.req.session.registerValidate,
        ctx.registerUser
      );
      await new Promise<void>((resolve) => {
        ctx.req.session.regenerate(() => {
          ctx.req.session.user = result;
          ctx.req.session.registerUser = undefined;
          ctx.req.session.registerValidate = undefined;
          ctx.req.session.save(resolve);
        });
      });
    }),
});
