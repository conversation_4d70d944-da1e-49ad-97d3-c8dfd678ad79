import { inputSchema, tradeCalculatorFilter } from "../model/trade";
import service from "../service/trade";
import { protectedProcedure as procedure, router } from "../utils/trpc";
import { z } from "zod";
export default router({
  list: procedure
    .input(inputSchema)
    .query(({ ctx, input }) => service.getList(input, ctx.user.identityNumber)),

  getTradeDays: procedure.query(({ ctx, input }) => service.getTradeDate()),

  getApplyDatesByProduct: procedure
    .input(z.string())
    .mutation(({ ctx, input }) =>
      service.getApplyDatesByProduct(ctx.user.identityNumber, input)
    ),

  search: procedure
    .input(tradeCalculatorFilter)
    .mutation(({ ctx, input }) =>
      service.search(ctx.user.identityNumber, input)
    ),

  getTradesByIdentityNumber: procedure.query(({ ctx }) =>
    service.getTradesByIdentityNumber(ctx.user.identityNumber)
  ),
});
