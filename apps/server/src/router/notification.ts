import { z } from "zod";
import { listInputScheam } from "../model/notification";
import service from "../service/notification";
import { protectedProcedure, router } from "../utils/trpc";

export default router({
  ownList: protectedProcedure
    .input(listInputScheam)
    .query(({ ctx, input }) => service.getOwnList(ctx.user.id, input)),

  read: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(({ ctx, input }) => service.read(ctx.user.id, input.id)),

  unreadAmount: protectedProcedure
    .input(listInputScheam)
    .query(({ ctx, input }) => service.getUnreadAmount(ctx.user.id, input)),
});
