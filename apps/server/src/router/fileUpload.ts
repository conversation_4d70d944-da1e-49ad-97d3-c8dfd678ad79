import type { Request<PERSON>and<PERSON> } from "express";
import env from "../config/env";
import service from "../service/fileUpload";
import { isDevEnv } from "../utils/dev";

const fileUploadRoute: RequestHandler = (req, res, next) => {
  if (req.path != "/api/fileUpload") {
    return next();
  }

  if (!req.session.user && !isDevEnv()) {
    const { secret } = req.headers;
    if (!env.SECRET || secret != env.SECRET) {
      return res.status(404).send("Not found");
    }
  }

  if (!req.files || !Object.keys(req.files).length) {
    return res.status(400).send("No files were uploaded.");
  }

  const body = req.files.file;
  const file = Array.isArray(body) ? body[0] : body;

  service
    .upload(file)
    .then((id) => res.json({ id }))
    .catch((err) => {
      console.error(err);
      return res
        .status(500)
        .send("File upload failed: " + err || "Unknown error.");
    });
};

export default fileUploadRoute;
