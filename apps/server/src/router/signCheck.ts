import { createInputSchema, filterSchema } from "../model/signCheck";
import service from "../service/signCheck";
import { protectedProcedure, router } from "../utils/trpc";

export default router({
  check: protectedProcedure
    .input(filterSchema)
    .mutation(({ ctx, input }) => service.check(ctx.user.id, input)),

  create: protectedProcedure
    .input(createInputSchema)
    .mutation(({ ctx, input }) => service.create(ctx.user.id, input)),
});
