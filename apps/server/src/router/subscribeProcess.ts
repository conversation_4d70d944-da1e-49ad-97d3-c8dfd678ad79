import { z } from "zod";
import {
  contractReviewInputSchema,
  contractSignInputSchema,
  createInputSchema,
  documentsSignInputSchema,
  listInputSchema,
  ownListInputSchema,
  payInputSchema,
  terminateInputSchema,
  ttdOrderReviewInputSchema,
  videoRecordInputSchema,
} from "../model/subscribeProcess";
import service from "../service/subscribeProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  byId: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getByIdSafe(input.id)),

  listByIdentityNumber: privateProcedure
    .input(z.object({ identityNumber: z.string() }))
    .query(({ input }) =>
      service.getListByIdentityNumber(input.identityNumber)
    ),

  ownList: protectedProcedure
    .input(ownListInputSchema)
    .query(({ ctx, input }) => service.getOwnList(ctx.user.id, input)),

  activeAmount: protectedProcedure.query(({ ctx }) =>
    service.getActiveAmount(ctx.user.id)
  ),

  documentsById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsWithTtdFilesById(input.id)),

  contractAndRiskRevealLinkById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getContractAndRiskRevealLinkById(input.id)),

  ttdFileIdsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdFileIdsById(input.id)),

  contractLinkById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getContractLinkById(input.id)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  videoRecord: protectedProcedure
    .input(videoRecordInputSchema)
    .mutation(({ ctx, input }) => service.videoRecord(ctx.user.id, input)),

  documentsSign: protectedProcedure
    .input(documentsSignInputSchema)
    .mutation(({ ctx, input }) => service.documentsSign(ctx.user.id, input)),

  contractSign: protectedProcedure
    .input(contractSignInputSchema)
    .mutation(({ ctx, input }) => service.contractSign(ctx.user.id, input)),

  ttdOrderReview: privateProcedure
    .input(ttdOrderReviewInputSchema)
    .mutation(({ input }) => service.ttdOrderReview(input)),

  contractReview: privateProcedure
    .input(contractReviewInputSchema)
    .mutation(({ input }) => service.contractReview(input)),

  pay: protectedProcedure
    .input(payInputSchema)
    .mutation(({ ctx, input }) => service.pay(ctx.user.id, input)),

  terminate: privateProcedure
    .input(terminateInputSchema)
    .mutation(({ input }) => service.terminate(input)),
});
