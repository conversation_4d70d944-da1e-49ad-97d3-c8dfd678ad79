import {
  createInputSchema,
  deleteInputSchema,
  inputSchema,
  publishInputSchema,
  updateInputSchema,
} from "../model/portfolioReport";
import service from "../service/portfolioReport";
import { privateProcedure as procedure, router } from "../utils/trpc";

export default router({
  list: procedure
    .input(inputSchema)
    .query(({ input }) => service.getList(input)),

  create: procedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  update: procedure
    .input(updateInputSchema)
    .mutation(({ input }) => service.update(input)),

  publish: procedure
    .input(publishInputSchema)
    .mutation(({ input }) => service.publish(input)),

  delete: procedure
    .input(deleteInputSchema)
    .mutation(({ input }) => service.delete(input)),
});
