import { z } from "zod";
import {
  productListInputSchema,
  verifyIdentityInputSchema,
} from "../model/ttd";
import service from "../service/ttd";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  productList: privateProcedure
    .input(productListInputSchema)
    .query(({ input }) =>
      (input?.name?.length || 0) > 1 ? service.queryProductList(input) : []
    ),

  orderState: protectedProcedure
    .input(z.object({ orderNumber: z.string() }))
    .query(({ input }) => service.queryOrder(input.orderNumber, true)),

  verifyIdentity: protectedProcedure
    .input(verifyIdentityInputSchema)
    .query(({ input }) => service.verifyIdentity(input.type, input.data)),
});
