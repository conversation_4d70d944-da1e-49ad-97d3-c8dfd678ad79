import dayjs from "dayjs";
import {
  faceRecognitionInputSchema,
  faceRecognitionUrlInputSchema,
  quartletInputSchema,
} from "../model/identityVerification";
import service from "../service/identityVerification";
import { logedInProcedure, router } from "../utils/trpc";

export default router({
  status: logedInProcedure.query(({ ctx }) => {
    const { identityVerificationTime } = ctx.req.session;
    const time = identityVerificationTime
      ? dayjs(identityVerificationTime)
      : undefined;

    const status = !!time?.isValid() && dayjs().diff(time, "minute") <= 15;
    return { status };
  }),

  faceRecognitionUrl: logedInProcedure
    .input(faceRecognitionUrlInputSchema)
    .query(({ ctx, input }) =>
      service.getFaceRecognitionUrl(ctx.user.id, input, ctx.req.sessionID)
    ),

  faceRecognition: logedInProcedure
    .input(faceRecognitionInputSchema)
    .mutation(async ({ ctx, input }) => {
      await service.faceRecognition(ctx.user.id, input, ctx.req.sessionID);
      await new Promise<void>((resolve) => {
        ctx.req.session.identityVerificationTime = dayjs().toISOString();
        ctx.req.session.save(resolve);
      });
    }),

  quartlet: logedInProcedure
    .input(quartletInputSchema)
    .mutation(({ ctx, input }) => service.quartlet(ctx.user.id, input)),
});
