import { z } from "zod";
import { listInputSchema } from "../model/user";
import identityVerificationService from "../service/identityVerification";
import service from "../service/user";
import {
  logedInProcedure,
  privateProcedure,
  protectedProcedure,
  router,
} from "../utils/trpc";
import { IndexBuilderOn } from "drizzle-orm/pg-core";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  listByNameFuzzy: privateProcedure
    .input(z.object({ name: z.string().trim() }))
    .query(({ input }) => service.getListByNameFuzzy(input.name)),

  user: logedInProcedure.query(async ({ ctx }) => {
    const [user, validated] = await Promise.all([
      await service.getSelf(ctx.user.id),
      await identityVerificationService.validatedByUserId(ctx.user.id),
    ]);

    return { ...user, validateIdentityUponLogin: !validated };
  }),

  userNo: protectedProcedure.query(({ ctx }) =>
    service.getTtdUserNoById(ctx.user.id)
  ),

  createUserTag: logedInProcedure
    .input(
      z.object({
        name: z.string().trim(),
        identityNumber: z.string().trim(),
      })
    )
    .mutation(({ input }) =>
      service.createUserTag(input.name, input.identityNumber)
    ),

  updatePassword: protectedProcedure
    .input(
      z.object({
        oldPassword: z.string().trim(),
        newPassword: z.string().trim(),
      })
    )
    .mutation(({ ctx, input }) =>
      service.updatePassword(ctx.user.id, input.oldPassword, input.newPassword)
    ),
});
