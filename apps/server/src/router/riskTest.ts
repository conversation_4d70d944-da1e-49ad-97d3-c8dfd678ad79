import { z } from "zod";
import { listInputSchema } from "../model/riskTest";
import service from "../service/riskTest";
import { privateProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  byId: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getById(input.id)),

  listByIdentityNumber: privateProcedure
    .input(z.object({ identityNumber: z.string() }))
    .query(({ input }) =>
      service.getListByIdentityNumber(input.identityNumber)
    ),

  questionnaireLinkById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getQuestionnaireLink(input.id)),
});
