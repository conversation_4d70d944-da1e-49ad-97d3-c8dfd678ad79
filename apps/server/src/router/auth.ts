import dayjs from "dayjs";
import { loginInputSchema, sendCodeInputSchema } from "../model/auth";
import {
  forgetPasswordFaceRecognitionInputSchema,
  forgetPasswordFaceRecognitionUrlInputSchema,
  forgetPasswordInputSchema,
} from "../model/user";
import { User } from "../schema/user";
import service from "../service/auth";
import {
  forgetPasswordIdentityVerifiedProcedure,
  forgetPasswordProcedure,
  logedInProcedure,
  publicProcedure,
  router,
} from "../utils/trpc";

export default router({
  sendCode: publicProcedure
    .input(sendCodeInputSchema)
    .mutation(async ({ ctx, input }) => {
      const code = await service.sendCode(input);
      await new Promise<void>((resolve) => {
        ctx.req.session.regenerate(() => {
          ctx.req.session.loginValidate = { ...input, code };
          ctx.req.session.save(resolve);
        });
      });
    }),

  login: publicProcedure
    .input(loginInputSchema)
    .mutation(async ({ input, ctx }) => {
      let user: User;

      if (input.type === "identityNumber") {
        user = await service.loginByIdentityNumber(
          input.username,
          input.password
        );
      } else {
        user = await service.loginByCode(input, ctx.req.session.loginValidate);
      }

      await new Promise<void>((resolve) => {
        ctx.req.session.regenerate(() => {
          ctx.req.session.user = user;
          ctx.req.session.loginValidate = undefined;
          ctx.req.session.save(resolve);
        });
      });
    }),

  logout: logedInProcedure.query(async ({ ctx }) => {
    return await new Promise<void>((resolve, reject) => {
      ctx.req.session.destroy((err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
  }),

  forgetPasswordStatus: publicProcedure.query(({ ctx }) => {
    const { forgetPasswordIdentityVerificationTime } = ctx.req.session;
    const time = forgetPasswordIdentityVerificationTime
      ? dayjs(forgetPasswordIdentityVerificationTime)
      : undefined;

    const status = !!time?.isValid() && dayjs().diff(time, "minute") <= 15;
    return { status };
  }),

  forgetPasswordFaceRecognitionUrl: publicProcedure
    .input(forgetPasswordFaceRecognitionUrlInputSchema)
    .mutation(async ({ ctx, input }) => {
      const { url, user } = await service.getForgetPasswordFaceRecognitionUrl(
        input,
        ctx.req.sessionID
      );

      await new Promise<void>((resolve) => {
        ctx.req.session.forgetPasswordUser = user;
        ctx.req.session.save(resolve);
      });

      return url;
    }),

  forgetPasswordFaceRecognition: forgetPasswordProcedure
    .input(forgetPasswordFaceRecognitionInputSchema)
    .mutation(async ({ ctx, input }) => {
      await service.forgetPasswordFaceRecognition(
        ctx.forgetPasswordUser.id,
        input,
        ctx.req.sessionID
      );

      await new Promise<void>((resolve) => {
        ctx.req.session.forgetPasswordIdentityVerificationTime =
          dayjs().toISOString();
        ctx.req.session.save(resolve);
      });
    }),

  forgetPassword: forgetPasswordIdentityVerifiedProcedure
    .input(forgetPasswordInputSchema)
    .mutation(async ({ ctx, input }) => {
      await service.forgetPassword(ctx.forgetPasswordUser.id, input);
      await new Promise<void>((resolve, reject) => {
        ctx.req.session.destroy((err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    }),
});
