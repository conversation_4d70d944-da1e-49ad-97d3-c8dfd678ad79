import { infoInputSchema, inputSchema } from "../model/netValue";
import service from "../service/netValue";
import { protectedProcedure as procedure, router } from "../utils/trpc";

export default router({
  infoByPortfolioId: procedure
    .input(infoInputSchema)
    .query(({ ctx, input }) => service.getInfo(input, ctx.user.identityNumber)),

  listByPortfolioId: procedure
    .input(inputSchema)
    .query(({ ctx, input }) => service.getList(input, ctx.user.identityNumber)),

  assetNetListByPortfolioId: procedure
    .input(inputSchema)
    .query(({ ctx, input }) =>
      service.getAssetNetList(input, ctx.user.identityNumber)
    ),
});
