import { z } from "zod";
import { updateInputSchema } from "../model/userConfig";
import service from "../service/userConfig";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  byId: privateProcedure
    .input(z.object({ userId: z.number() }))
    .query(({ input }) => service.getByUserId(input.userId)),

  own: protectedProcedure.query(({ ctx }) => service.getByUserId(ctx.user.id)),

  update: privateProcedure
    .input(updateInputSchema)
    .mutation(({ input }) => service.update(input)),
});
