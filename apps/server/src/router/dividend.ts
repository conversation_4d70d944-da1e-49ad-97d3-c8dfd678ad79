import { inputSchema,filterSchema,dividendCalculatorFilter } from "../model/dividend";
import service from "../service/dividend";
import { protectedProcedure as procedure, router,protectedProcedure } from "../utils/trpc";

export default router({
  list: procedure
    .input(inputSchema)
    .query(({ ctx, input }) => service.getList(input, ctx.user.identityNumber)),

  byDividendCalculator: procedure
   .input(dividendCalculatorFilter)
   .mutation(({ ctx,input }) => service.getDataByDividedCalculator(input,ctx.user.identityNumber)),
});
