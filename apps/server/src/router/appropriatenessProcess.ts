import { z } from "zod";
import {
  classificationDocumentsInputSchema,
  classificationInputSchema,
  classificationReviewInputSchema,
  createInputSchema,
  documentsReviewInputSchema,
  documentsSignInputSchema,
  listInputSchema,
  riskTestInputSchema,
  riskTestReviewinputSchema,
  setSignPasswordInputSchema,
} from "../model/appropriatenessProcess";
import service from "../service/appropriatenessProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  listByIdentityNumber: privateProcedure
    .input(z.object({ identityNumber: z.string() }))
    .query(({ input }) =>
      service.getListByIdentityNumber(input.identityNumber)
    ),

  own: protectedProcedure.query(({ ctx }) => service.getOwn(ctx.user.id)),

  documentsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsById(input.id)),

  documentsDetailById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsDetailById(input.id)),

  ttdFileIdsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdFileIdsById(input.id)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  userCreate: protectedProcedure.mutation(({ ctx }) =>
    service.create({ userId: ctx.user.id, operatorId: ctx.user.id })
  ),

  classification: protectedProcedure
    .input(classificationInputSchema)
    .mutation(({ ctx, input }) => service.classification(ctx.user.id, input)),

  classificationDocuments: privateProcedure
    .input(classificationDocumentsInputSchema)
    .mutation(({ input }) => service.classificationDocuments(input)),

  classificationReview: privateProcedure
    .input(classificationReviewInputSchema)
    .mutation(({ input }) => service.classificationReview(input)),

  riskTest: protectedProcedure
    .input(riskTestInputSchema)
    .mutation(({ ctx, input }) => service.riskTest(ctx.user.id, input)),

  riskTestReview: privateProcedure
    .input(riskTestReviewinputSchema)
    .mutation(({ input }) => service.riskTestReview(input)),

  setSignPassword: protectedProcedure
    .input(setSignPasswordInputSchema)
    .mutation(({ ctx, input }) => service.setSignPassword(ctx.user.id, input)),

  documentsSign: protectedProcedure
    .input(documentsSignInputSchema)
    .mutation(({ ctx, input }) => service.documentsSign(ctx.user.id, input)),

  documentsReview: privateProcedure
    .input(documentsReviewInputSchema)
    .mutation(({ input }) => service.documentsReview(input)),
});
