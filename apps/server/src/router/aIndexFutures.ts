import { getViewConfig } from "drizzle-orm/pg-core";
import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import service from "../service/aIndexFutures";
import {
  protectedProcedure as procedure,
  router,
  protectedProcedure,
} from "../utils/trpc";
import { get } from "lodash";
import { z } from "zod";
export default router({
  //   getLastDayData: procedure
  //     // .input(inputSchema)
  //     .query(({ ctx, input }) => service.getLastDayData()),

  getICDayData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICFuturesData(input)),

  getICAnnualizedBasisTrend: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICAnnualizedBasisTrend(input)),

  getIHDayData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIHFuturesData(input)),

  getIHAnnualizedBasisTrend: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIHAnnualizedBasisTrend(input)),

  getIFDayData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIFFuturesData(input)),

  getIFAnnualizedBasisTrend: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIFAnnualizedBasisTrend(input)),

  getIMDayData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIMFuturesData(input)),

  getIMAnnualizedBasisTrend: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIMAnnualizedBasisTrend(input)),

  getIC3YearsData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICHedgeBasisTrend3Y(input)),

  getIM3YearsData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIMHedgeBasisTrend3Y(input)),

  getIH3YearsData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIHHedgeBasisTrend3Y(input)),

  getIF3YearsData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getIFHedgeBasisTrend3Y(input)),

  getWeekData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getCurrentWeekTradeDates(input)),

  getMonthData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getCurrentMonthTradeDates(input)),

  getYearData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getCurrentYearTradeDates(input)),

  getICWeekBasisAverage: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICWeekBasisAverage(input)),

  getICMonthBasisAverage: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICMonthBasisAverage(input)),

  getICYearBasisAverage: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getICYearBasisAverage(input)),

  getIHWeekBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIHWeekBasisAverage(input)),

  getIHMonthBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIHMonthBasisAverage(input)),

  getIHYearBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIHYearBasisAverage(input)),

  getIFWeekBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIFWeekBasisAverage(input)),

  getIFMonthBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIFMonthBasisAverage(input)),

  getIFYearBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIFYearBasisAverage(input)),

  getIMWeekBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIMWeekBasisAverage(input)),

  getIMMonthBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIMMonthBasisAverage(input)),

  getIMYearBasisAverage:procedure.input(z.string()).query(({ctx,input})=>service.getIMYearBasisAverage(input)),
});
