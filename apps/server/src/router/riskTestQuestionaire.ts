import { typeSchema } from "@portfolio-service/schema/riskTestQuestionaire";
import { z } from "zod";
import service from "../service/riskTestQuestionaire";
import { protectedProcedure, router } from "../utils/trpc";

export default router({
  latest: protectedProcedure.query(({ ctx }) => service.getLatest(ctx.user.id)),

  latestByType: protectedProcedure
    .input(z.object({ type: typeSchema }))
    .query(({ input }) => service.getLatestByType(input.type)),
});
