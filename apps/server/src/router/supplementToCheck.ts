import {
  createSupplementToCheckAltInputSchema,
  createSupplementToCheckInputSchema,
} from "../model/supplementProcess";
import service from "../service/supplementToCheck";
import { privateProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure.query(() => service.getList()),

  altList: privateProcedure.query(() => service.getAltList()),

  create: privateProcedure
    .input(createSupplementToCheckInputSchema)
    .mutation(({ input }) => service.create(input)),

  createAlt: privateProcedure
    .input(createSupplementToCheckAltInputSchema)
    .mutation(({ input }) => service.createAlt(input)),
});
