import { inputSchema } from "../model/netValueDisclosureConf";
import { insertSchema } from "../schema/netValueDisclosureConf";
import service from "../service/netValueDisclosureConf";
import { privateProcedure as procedure, router } from "../utils/trpc";

export default router({
  list: procedure
    .input(inputSchema)
    .query(({ input }) => service.getList(input)),

  displayList: procedure
    .input(inputSchema)
    .query(({ input }) => service.getDisplayList(input)),

  update: procedure
    .input(insertSchema)
    .mutation(({ input }) => service.update(input)),

  delete: procedure
    .input(insertSchema.pick({ portfolioId: true }))
    .mutation(({ input }) => service.delete(input)),

  latestNetValueDates: procedure.query(() =>
    service.getLatestNetValueDatesCached()
  ),
});
