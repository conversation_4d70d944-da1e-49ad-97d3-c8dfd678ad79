import { z } from "zod";
import {
  createInputSchema,
  listInputSchema,
  managerCreateInputSchema,
  nextInputSchema,
  riskTestInputSchema,
} from "../model/riskTestProcess";
import service from "../service/riskTestProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  byId: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getById(input.id)),

  own: protectedProcedure.query(({ ctx }) => service.getOwn(ctx.user.id)),

  documentsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsById(input.id)),

  ttdFileIdsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdFileIdsById(input.id)),

  create: protectedProcedure
    .input(createInputSchema)
    .mutation(({ ctx, input }) => service.create(ctx.user.id, input)),

  managerCreate: privateProcedure
    .input(managerCreateInputSchema)
    .mutation(({ input }) => service.managerCreate(input)),

  riskTest: protectedProcedure
    .input(riskTestInputSchema)
    .mutation(({ ctx, input }) => service.riskTest(ctx.user.id, input)),

  documentsSign: protectedProcedure
    .input(nextInputSchema)
    .mutation(({ ctx, input }) => service.documentsSign(ctx.user.id, input)),

  questionnaireLinkById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getQuestionnaireLink(input.id)),
});
