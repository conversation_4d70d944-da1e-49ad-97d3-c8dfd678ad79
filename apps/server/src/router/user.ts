import dayjs from "dayjs";
import {
  sendCodeInputSchema,
  updateLoginInputSchema,
  updateLoginSendCodeInputSchema,
  verifyCodeInputSchema,
} from "../model/user";
import service from "../service/user";
import {
  protectedProcedure,
  router,
  updateLoginProcedure,
  protectedProcedure as procedure,
} from "../utils/trpc";
import { Procedure } from "@trpc/server/dist/deprecated/router";
import { procedureTypes } from "@trpc/server";

export default router({
  genericCodeVerificationStatus: protectedProcedure.query(async ({ ctx }) => {
    const { genericCodeVerificationTime } = ctx.req.session;
    const time = genericCodeVerificationTime
      ? dayjs(genericCodeVerificationTime)
      : undefined;

    const valid =
      !!time && time.isValid() && dayjs().diff(time, "minute") <= 15;

    return { valid };
  }),

  sendCode: protectedProcedure
    .input(sendCodeInputSchema)
    .mutation(async ({ ctx, input }) => {
      const code = await service.sendCode(ctx.user.id, input.type);
      await new Promise<void>((resolve) => {
        ctx.req.session.genericCodeValidate = { code };
        ctx.req.session.save(resolve);
      });
    }),

  verifyCode: protectedProcedure
    .input(verifyCodeInputSchema)
    .mutation(async ({ ctx, input }) => {
      const { genericCodeValidate } = ctx.req.session;
      await service.verifyCode(input.code, genericCodeValidate?.code);
      await new Promise<void>((resolve) => {
        ctx.req.session.genericCodeValidate = undefined;
        ctx.req.session.genericCodeVerificationTime = dayjs().toISOString();
        ctx.req.session.save(resolve);
      });
    }),

  updateLoginSendCode: updateLoginProcedure
    .input(updateLoginSendCodeInputSchema)
    .mutation(async ({ ctx, input }) => {
      let value: string | undefined = undefined;
      if (input.type === "phone") value = input.phone;
      if (input.type === "email") value = input.email;

      const code = await service.sendCode(ctx.user.id, input.type, value);
      await new Promise<void>((resolve) => {
        ctx.req.session.updateLoginValidate = { ...input, code };
        ctx.req.session.save(resolve);
      });
    }),

  updateLogin: updateLoginProcedure
    .input(updateLoginInputSchema)
    .mutation(async ({ ctx, input }) => {
      await service.updateLogin(
        input,
        ctx.req.session.updateLoginValidate,
        ctx.user.id
      );
      await new Promise<void>((resolve) => {
        ctx.req.session.genericCodeVerificationTime = undefined;
        ctx.req.session.updateLoginValidate = undefined;
        ctx.req.session.save(resolve);
      });
    }),

  getUserTags: procedure.query(({ ctx, input }) =>
    service.getUserTag(ctx.user.id)
  ),
});
