import {
  deleteInputSchema,
  inputSchema,
} from "../model/portfolioComplianceConf";
import { insertSchema } from "../schema/portfolioComplianceConf";
import { privateProcedure as procedure, router } from "../utils/trpc";
import service from "../service/portfolioComplianceConf";

export default router({
  list: procedure
    .input(inputSchema)
    .query(({ input }) => service.getList(input)),

  displayList: procedure
    .input(inputSchema)
    .query(({ input }) => service.getDisplayList(input)),

  update: procedure
    .input(insertSchema)
    .mutation(({ input }) => service.update(input)),

  delete: procedure
    .input(deleteInputSchema)
    .mutation(({ input }) => service.delete(input)),
});
