import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import service from "../service/aIndex";
import {
  protectedProcedure as procedure,
  router,
  protectedProcedure,
} from "../utils/trpc";
import { z } from "zod";

export default router({
  getLastDayData: procedure
    // .input(inputSchema)
    .query(({ ctx, input }) => service.getLastDayData()),

  getTradeDayData: procedure
    .input(z.string())
    .query(({ ctx, input }) => service.getTradeDayData(input)),
});
