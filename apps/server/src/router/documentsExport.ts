import { Request<PERSON><PERSON><PERSON> } from "express";
import { isDevEnv } from "../utils/dev";
import env from "../config/env";
import appropriatenessProcessService from "../service/appropriatenessProcess";
import redeemProcessService from "../service/redeemProcess";
import subscribeProcessService from "../service/subscribeProcess";
import supplementProcessService from "../service/supplementProcess";
import { z } from "zod";

const typeSchema = z.enum([
  "appropriateness",
  "subscribe",
  "redeem",
  "supplement",
]);

const documentsExportRoute: RequestHandler = (req, res, next) => {
  if (req.path != "/api/documents_export") {
    return next();
  }

  if (!isDevEnv()) {
    const { secret } = req.headers;
    if (!env.SECRET || secret != env.SECRET) {
      return res.status(404).send("Not found");
    }
  }

  const id = req.query.id?.toString();
  if (!id) {
    return res.status(400).send('"id" is empty.');
  }

  const typeParsed = typeSchema.safeParse(req.query.type?.toString());
  if (!typeParsed.success) {
    return res.status(400).send('Invalid "type".');
  }

  let request: (id: number) => Promise<{ filename: string; data: Buffer }>;
  switch (typeParsed.data) {
    case "appropriateness":
      request = (id) => appropriatenessProcessService.getDocumentsZipById(id);
      break;
    case "redeem":
      request = (id) => redeemProcessService.getDocumentsZipById(id);
      break;
    case "subscribe":
      request = (id) => subscribeProcessService.getDocumentsZipById(id);
      break;
    case "supplement":
      request = (id) => supplementProcessService.getDocumentsZipById(id);
  }

  const errorHandler = (err: unknown) => {
    console.error(err);
    res.status(500).send("Download failed: " + err);
  };

  request(parseInt(id))
    .then(({ data, filename }) => {
      res.writeHead(200, {
        "Content-Disposition": `attachment; filename="${encodeURIComponent(filename)}"`,
        "Content-Type": "application/zip",
        "Access-Control-Expose-Headers": "Content-Disposition",
      });
      return res.end(data);
    })
    .catch(errorHandler);
};

export default documentsExportRoute;
