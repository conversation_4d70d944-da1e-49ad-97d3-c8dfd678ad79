import { region } from "../schema/region";
import { router } from "../utils/trpc";
import accountRouter from "./account";
import appropriatenessProcessRouter from "./appropriatenessProcess";
import authRouter from "./auth";
import bankCardRouter from "./bankCard";
import baseInfoRouter from "./baseInfo";
import dividendRouter from "./dividend";
import documentRouter from "./document";
import identityVerificationRouter from "./identityVerification";
import netValueRouter from "./netValue";
import netValueDisclosureConfRouter from "./netValueDisclosureConf";
import notificationRouter from "./notification";
import periodicReportRouter from "./periodicReport";
import periodicReportReadonlyRouter from "./periodicReportReadonly";
import portfolioRouter from "./portfolio";
import portfolioComplianceConfRouter from "./portfolioComplianceConf";
import portfolioReportRouter from "./portfolioReport";
import portfolioReportReadonlyRouter from "./portfolioReportReadonly";
import redeemProcessRouter from "./redeemProcess";
import register from "./register";
import registerRouter from "./register";
import riskTestRouter from "./riskTest";
import riskTestProcessRouter from "./riskTestProcess";
import riskTestQuestionaireRouter from "./riskTestQuestionaire";
import shareRouter from "./share";
import signCheckRouter from "./signCheck";
import signPasswordRouter from "./signPassword";
import subscribeProcessRouter from "./subscribeProcess";
import supplementCheckLogRouter from "./supplementCheckLog";
import supplementProcessRouter from "./supplementProcess";
import supplementToCheckRouter from "./supplementToCheck";
import taxDeclarationRouter from "./taxDeclaration";
import tradeRouter from "./trade";
import tradeApplyProcessRouter from "./tradeApplyProcess";
import ttdRouter from "./ttd";
import ttdUrlRouter from "./ttdUrl";
import userRouter from "./user";
import userConfigRouter from "./userConfig";
import videoConfigRouter from "./videoConfig";
import videoRecordLegacyRouter from "./videoRecordLegacy";
import wechatRouter from "./wechat";
import workflowHistoryRouter from "./workflowHistory";
import workflowTaskRouter from "./workflowTask";
import regionRouter from "./region";
import userFollowedProductsRouter from "./userFollowedProducts";
import aIndexRouter from "./aIndex";
import aIndexFuturesRouter from "./aIndexFutures";
const appRouter = router({
  account: accountRouter,
  appropriatenessProcess: appropriatenessProcessRouter,
  auth: authRouter,
  bankCard: bankCardRouter,
  baseInfo: baseInfoRouter,
  dividend: dividendRouter,
  document: documentRouter,
  identityVerification: identityVerificationRouter,
  netValue: netValueRouter,
  netValueDisclosureConf: netValueDisclosureConfRouter,
  notification: notificationRouter,
  periodicReport: periodicReportRouter,
  periodicReportReadonly: periodicReportReadonlyRouter,
  portfolio: portfolioRouter,
  portfolioComplianceConf: portfolioComplianceConfRouter,
  portfolioReport: portfolioReportRouter,
  portfolioReportReadonly: portfolioReportReadonlyRouter,
  register: registerRouter,
  redeemProcess: redeemProcessRouter,
  riskTest: riskTestRouter,
  riskTestProcess: riskTestProcessRouter,
  riskTestQuestionaire: riskTestQuestionaireRouter,
  share: shareRouter,
  signCheck: signCheckRouter,
  signPassword: signPasswordRouter,
  subscribeProcess: subscribeProcessRouter,
  supplementCheckLog: supplementCheckLogRouter,
  supplementProcess: supplementProcessRouter,
  supplementToCheck: supplementToCheckRouter,
  taxDeclaration: taxDeclarationRouter,
  trade: tradeRouter,
  tradeApplyProcess: tradeApplyProcessRouter,
  ttd: ttdRouter,
  ttdUrl: ttdUrlRouter,
  user: userRouter,
  userConfig: userConfigRouter,
  videoConfig: videoConfigRouter,
  videoRecordLegacy: videoRecordLegacyRouter,
  wechat: wechatRouter,
  workflowHistory: workflowHistoryRouter,
  workflowTask: workflowTaskRouter,
  region: regionRouter,
  userFollowedProducts: userFollowedProductsRouter,
  aIndex: aIndexRouter,
  aIndexFutures: aIndexFuturesRouter,
});

export type AppRouter = typeof appRouter;
export default appRouter;
