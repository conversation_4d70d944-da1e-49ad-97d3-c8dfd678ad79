import { z } from "zod";
import {
  createInputSchema,
  listInputSchema,
  nextInputSchema,
  ownListInputSchema,
  reviewInputSchema,
  terminateInputSchema,
} from "../model/redeemProcess";
import service from "../service/redeemProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  byId: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getByIdSafe(input.id)),

  listByIdentityNumber: privateProcedure
    .input(z.object({ identityNumber: z.string() }))
    .query(({ input }) =>
      service.getListByIdentityNumber(input.identityNumber)
    ),

  ownList: protectedProcedure
    .input(ownListInputSchema)
    .query(({ ctx, input }) => service.getOwnList(ctx.user.id, input)),

  activeAmount: protectedProcedure.query(({ ctx }) =>
    service.getActiveAmount(ctx.user.id)
  ),

  documentsById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsWithTtdFilesById(input.id)),

  ttdFileIdsById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getTtdFileIdsById(input.id)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  documentsSign: protectedProcedure
    .input(nextInputSchema)
    .mutation(({ ctx, input }) => service.documentsSign(ctx.user.id, input)),

  documentsReview: privateProcedure
    .input(reviewInputSchema)
    .mutation(({ input }) => service.documentsReview(input)),

  terminate: privateProcedure
    .input(terminateInputSchema)
    .mutation(({ input }) => service.terminate(input)),
});
