import {
  inputSchema,
  filterSchema,
  dividendCalculatorFilter,
} from "../model/dividend";
import service from "../service/userFollowedProducts";
import {
  protectedProcedure as procedure,
  router,
  protectedProcedure,
} from "../utils/trpc";
import { z } from "zod";
export default router({
  addProudcts: procedure
    .input(z.array(z.string()))
    .mutation(({ ctx, input }) =>
      service.addProducts(input, ctx.user.identityNumber)
    ),

  getList: protectedProcedure
   .query(({ ctx, input }) => service.getProducts(ctx.user.identityNumber)),  
});
