import { z } from "zod";
import {
  createInputSchema,
  listInputSchema,
  nextInputSchema,
  ownListInputSchema,
  reviewInputSchema,
  terminateInputSchema,
} from "../model/supplementProcess";
import service from "../service/supplementProcess";
import { privateProcedure, protectedProcedure, router } from "../utils/trpc";

export default router({
  list: privateProcedure
    .input(listInputSchema)
    .query(({ input }) => service.getList(input)),

  ownList: protectedProcedure
    .input(ownListInputSchema)
    .query(({ ctx, input }) => service.getOwnList(ctx.user.id, input)),

  activeAmount: protectedProcedure.query(({ ctx }) =>
    service.getActiveAmount(ctx.user.id)
  ),

  documentsById: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getDocumentsById(input.id)),

  supplementLinksByIdPrivate: privateProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getSupplementLinksById(input.id)),

  supplementLinksById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(({ input }) => service.getSupplementLinksById(input.id)),

  create: privateProcedure
    .input(createInputSchema)
    .mutation(({ input }) => service.create(input)),

  supplementSign: protectedProcedure
    .input(nextInputSchema)
    .mutation(({ ctx, input }) => service.supplementSign(ctx.user.id, input)),

  supplementReview: privateProcedure
    .input(reviewInputSchema)
    .mutation(({ input }) => service.supplementReview(input)),

  terminate: privateProcedure
    .input(terminateInputSchema)
    .mutation(({ input }) => service.terminate(input)),
});
