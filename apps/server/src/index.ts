import * as trpcExpress from "@trpc/server/adapters/express";
import bodyParser from "body-parser";
import express from "express";
import fileUpload from "express-fileupload";
import session from "express-session";
import env from "./config/env";
import router from "./router";
import documentsExportRoute from "./router/documentsExport";
import fileUploadRoute from "./router/fileUpload";
import staticRoute from "./router/static";
import * as scheduler from "./scheduler";
import { isDevEnv } from "./utils/dev";
import { SessionStore } from "./utils/sessionStore";
import { createContext } from "./utils/trpc";

const app = express();

app.use(bodyParser.json());
app.use(fileUpload({ defCharset: "utf8", defParamCharset: "utf8" }));

app.use(
  session({
    secret: "secret",
    resave: false,
    saveUninitialized: false,
    store: new SessionStore(),
    cookie: { maxAge: 30 * 24 * 60 * 60 * 1000 },
  })
);

const passwordPaths = ["/trpc/auth.login", "/trpc/account.updatePassword"];

app.use((req, _, next) => {
  let input: unknown | undefined = undefined;
  if (!passwordPaths.includes(req.path) && !!Object.keys(req.body).length) {
    input = req.body;
  } else if (!!Object.keys(req.query).length) {
    input = req.query;
  }

  console.log(
    "<---",
    req.method,
    req.path,
    `user_id: ${req.session.user?.id || "null"}`,
    input || "{}"
  );
  next();
});

app.use(
  "/trpc",
  trpcExpress.createExpressMiddleware({
    router,
    createContext,
    onError: ({ error }) => {
      if (error.code === "INTERNAL_SERVER_ERROR") {
        console.error(error);
      } else {
        if (isDevEnv()) {
          console.error(error);
        }
      }
    },
  })
);

app.use(fileUploadRoute);
app.use(staticRoute);
app.use(documentsExportRoute);

app.listen(env.PORT || 3000);

scheduler.register();
