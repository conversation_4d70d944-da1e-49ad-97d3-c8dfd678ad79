import { classificationEnum } from "@portfolio-service/schema/userType";
import { sql } from "drizzle-orm";
import {
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("appropriateness_process"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" }).default(
      sql`current_timestamp`
    ),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),

    userId: int("user_id").notNull(),
    classification: varchar("classification", {
      enum: classificationEnum,
      length: 255,
    }),
    riskTestId: int("risk_test_id"),
    identityValidationId: int("identity_validation_id"),
  },
  (table) => ({
    index: index("appropriateness_process_user_id_create_time_index").on(
      table.userId,
      table.createTime
    ),
    userIdIndex: index("appropriateness_process_user_id_index").on(
      table.userId
    ),
    workflowIdIndex: index("appropriateness_process_workflow_id_index").on(
      table.workflowId
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  userId: model.userId,
  classification: model.classification,
  riskTestId: model.riskTestId,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export const appropriatenessProcessDocument = mysqlTable(
  withPrefix("appropriateness_process_document"),
  {
    processId: int("process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index("appropriateness_process_document_process_id_index").on(
      table.processId
    ),
  })
);
