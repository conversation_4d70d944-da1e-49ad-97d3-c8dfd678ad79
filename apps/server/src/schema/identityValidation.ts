import { identityVerificationTypeEnum } from "@portfolio-service/schema/identityValidation";
import { sql } from "drizzle-orm";
import {
  boolean,
  int,
  longtext,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema } from "drizzle-zod";
import { index, timestamp } from "drizzle-orm/mysql-core";
import {} from "drizzle-orm/pg-core";

export const model = mysqlTable(
  withPrefix("identity_validation"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time")
      .notNull()
      .default(sql`current_timestamp`),
    userId: int("user_id"),
    status: boolean("identity_verification_status").notNull().default(false),
    type: varchar("identity_verification_type", {
      enum: identityVerificationTypeEnum,
      length: 255,
    }).notNull(),
    quartletFormData: longtext("quartlet_form_data"),
  },
  (table) => ({
    index: index("identity_validation_index").on(table.userId, table.status),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  userId: model.userId,
  status: model.status,
  type: model.type,
};

export const insertSchema = createInsertSchema(model);
