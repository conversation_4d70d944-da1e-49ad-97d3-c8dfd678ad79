import { statusEnum } from "@portfolio-service/schema/workflow";
import { sql } from "drizzle-orm";
import {
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { z } from "zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const model = mysqlTable(
  withPrefix("workflow_task"),
  {
    workflowId: varchar("workflow_id", { length: 255 }).primaryKey(),
    machineId: varchar("machine_id", { length: 255 }).notNull(),
    createTime: timestamp("create_time", { mode: "string" }).default(
      sql`current_timestamp`
    ),
    updateTime: timestamp("update_time", { mode: "string" })
      .default(sql`current_timestamp`)
      .onUpdateNow(),
    state: varchar("state", { length: 255 }).notNull(),
    status: varchar("status", { enum: statusEnum, length: 255 }).notNull(),
    data: longtext("data"),
    assigneeId: int("assignee_id"),
  },
  (table) => ({
    machineIdIndex: index("workflow_task_machine_id_index").on(table.machineId),
    assigneeIdIndex: index("workflow_task_assignee_id_index").on(
      table.assigneeId
    ),
  })
);

export const select = {
  workflowId: model.workflowId,
  machineId: model.machineId,
  createTime: model.createTime,
  updateTime: model.updateTime,
  state: model.state,
  status: model.status,
  data: sql`${model.data}`.mapWith({
    mapFromDriverValue: (value) => parseJSON(value, z.any(), null),
  }),
  assigneeId: model.assigneeId,
};
