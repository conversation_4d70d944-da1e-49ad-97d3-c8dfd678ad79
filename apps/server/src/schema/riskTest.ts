import {
  answersSchema,
  levelEnum,
} from "@portfolio-service/schema/riskTestQuestionaire";
import { sql } from "drizzle-orm";
import {
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const model = mysqlTable(withPrefix("risk_test"), {
  id: int("id").primaryKey().autoincrement(),
  createTime: timestamp("create_time", { mode: "string" })
    .notNull()
    .default(sql`current_timestamp`),
  userId: int("user_id").notNull(),
  questionaireId: int("questionaire_id").notNull(),
  answers: longtext("answers").notNull(),
  score: int("score").notNull(),
  level: varchar("level", { enum: levelEnum, length: 255 }).notNull(),
  fileUploadId: varchar("file_upload_id", { length: 255 }),
});

export const select = {
  id: model.id,
  createTime: model.createTime,
  expireTime: sql`date_add(${model.createTime}, interval 3 year)`,
  userId: model.userId,
  questionaireId: model.questionaireId,
  score: model.score,
  level: model.level,
  fileUploadId: model.fileUploadId,
};

export const answersSelect = sql`${model.answers}`.mapWith({
  mapFromDriverValue: (value) => parseJSON(value, answersSchema, []),
});

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
