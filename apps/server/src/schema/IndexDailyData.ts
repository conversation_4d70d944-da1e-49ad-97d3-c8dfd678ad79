import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("A_lndex_Wind_Industries_EOD", {
  OBJECT_ID: varchar("OBJECT_ID", { length: 100 }).primaryKey(),
  S_INFO_WINDCODE: varchar("S_INFO_WINDCODE", { length: 40 }),
  TRADE_DT: varchar("TRADE_DT", { length: 8 }),
  S_DQ_PRECLOSE: decimal("S_DQ_PRECLOSE", { precision: 20, scale: 4 }),
  S_DQ_CLOSE:decimal("S_DQ_CLOSE", { precision: 20, scale: 4 }),
  S_DQ_CHANGE: decimal("S_DQ_CHANGE", { precision: 20, scale: 4 }),
  S_DQ_PCTCHANGE: decimal("S_DQ_PCTCHANGE", { precision: 20, scale: 4 }),
});

export const select = {
  OBJECT_ID: model.OBJECT_ID,
  S_INFO_WINDCODE: model.S_INFO_WINDCODE,
  TRADE_DT: model.TRADE_DT,
  S_DQ_PRECLOSE: model.S_DQ_PRECLOSE,
  S_DQ_CHANGE: model.S_DQ_CHANGE,
  S_DQ_PCTCHANGE: model.S_DQ_PCTCHANGE,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type IndexEODPrices = typeof model.$inferSelect;
