import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  index,
  int,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import * as fileUpload from "./fileUpload";
import * as portfolio from "./portfolio";

export const model = mysqlTable(
  withPrefix("portfolio_report"),
  {
    id: int("id").primaryKey().autoincrement(),
    portfolioId: int("portfolio_id").notNull(),
    date: date("date").notNull(),
    published: boolean("published").notNull(),
    publishedBy: int("published_by").notNull(),
    fileUploadId: varchar("file_upload_id", { length: 255 }).notNull(),
  },
  (table) => ({
    index: index("portfolio_report_date_index").on(table.date),
  })
);

export const select = {
  id: model.id,
  portfolioId: model.portfolioId,
  portfolioName: portfolio.model.name,
  portfolioNo: portfolio.model.no,
  date: model.date,
  published: model.published,
  publishedBy: model.publishedBy,
  fileUploadId: model.fileUploadId,
  name: fileUpload.model.name,
};

export const insertSchema = createInsertSchema(model, {
  date: () => stringToDate,
});
export const selectSchema = createSelectSchema(model);
