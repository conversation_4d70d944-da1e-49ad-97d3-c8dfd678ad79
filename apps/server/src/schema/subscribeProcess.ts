import { typeEnum } from "@portfolio-service/schema/subscribeProcess";
import { sql } from "drizzle-orm";
import {
  boolean,
  datetime,
  double,
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("subscribe_process"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),

    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),

    type: varchar("type", { enum: typeEnum, length: 255 }).notNull(),
    amount: double("amount").notNull(),
    videoConfigTargetCode: varchar("video_config_target_code", {
      length: 255,
    }),

    orderNumber: varchar("order_number", { length: 255 }),
    productNumber: varchar("product_number", { length: 255 }),
    riskMatch: boolean("risk_match"),

    time: datetime("time", { mode: "string" }).notNull(),

    videoRecordStatus: boolean("video_record_status").notNull().default(false),
    documentsStatus: boolean("documents_status").notNull().default(false),
    contractStatus: boolean("contract_status").notNull().default(false),

    contractCreateTime: datetime("contract_create_time", { mode: "string" }),
    contractSignTime: datetime("contract_sign_time", { mode: "string" }),

    terminated: boolean("terminated").notNull().default(false),

    tradeApplyProcessId: int("trade_apply_process_id"),
  },
  (table) => ({
    userPortfoio: index("subscribe_process_user_id_portfoio_id_index").on(
      table.userId,
      table.portfolioId
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  userId: model.userId,
  portfolioId: model.portfolioId,
  type: model.type,
  amount: model.amount,
  videoConfigTargetCode: model.videoConfigTargetCode,
  orderNumber: model.orderNumber,
  productNumber: model.productNumber,

  riskMatch: model.riskMatch,
  time: model.time,

  videoRecordStatus: model.videoRecordStatus,
  documentsStatus: model.documentsStatus,
  contractStatus: model.contractStatus,
  contractSignTime: model.contractSignTime,

  terminated: model.terminated,

  tradeApplyProcessId: model.tradeApplyProcessId,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
export const updateSchema = insertSchema.partial();

export const subscribeProcessDocument = mysqlTable(
  withPrefix("subscribe_process_document"),
  {
    subscribeProcessId: int("subscribe_process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index("subscribe_process_document_subscribe_process_id_index").on(
      table.subscribeProcessId
    ),
  })
);
