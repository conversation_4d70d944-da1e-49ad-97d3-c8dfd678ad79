import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import * as fileUpload from "./fileUpload";
import * as portfolio from "./portfolio";
import email from "../service/email";

export const model = mysqlTable(
  withPrefix("portfolio_client"),
  {
    id: int("id").primaryKey().notNull(),
    createTime: date("create_time"),
    updateTime: date("update_time"),
    comment: varchar("comment", { length: 255 }),
    label: varchar("label", { length: 255 }),
    name: varchar("name", { length: 255 }),
    email: text("email",{mode:"long"} as any),
    identityNumber: varchar("identity_number", { length: 255 }),
    classification: varchar("classification", { length: 255 })
  },
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  updateTime: model.updateTime,
  comment: model.comment,
  label: model.label,
 name: model.name,
 email: model.email,
  identityNumber: model.identityNumber,
  classification: model.classification,
};

// export const insertSchema = createInsertSchema(model, {
//   date: () => stringToDate,
// });
export const selectSchema = createSelectSchema(model);

export type PortfolioClient = typeof model.$inferSelect;
