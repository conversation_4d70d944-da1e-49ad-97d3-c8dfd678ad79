import { statusEnum } from "@portfolio-service/schema/workflow";
import { sql } from "drizzle-orm";
import {
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("workflow_history"),
  {
    id: int("id").primaryKey().autoincrement(),
    machineId: varchar("machine_id", { length: 255 }).notNull(),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),
    timestamp: timestamp("timestamp", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    state: varchar("state", { length: 255 }).notNull(),
    status: varchar("status", { enum: statusEnum, length: 255 }).notNull(),
    operatorId: int("operator_id").notNull(),
  },
  (table) => ({
    machineIdIndex: index("workflow_history_machine_id_index").on(
      table.machineId
    ),
    workflowIdIndex: index("workflow_history_workflow_id_index").on(
      table.workflowId
    ),
    operatorIdIndex: index("workflow_history_operator_id_index").on(
      table.operatorId
    ),
  })
);
