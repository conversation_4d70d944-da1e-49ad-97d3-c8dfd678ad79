import { sql } from "drizzle-orm";
import { int, longtext, mysqlTable, varchar } from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const visibilityEnum = ["all", "holding", "hold"] as const;
export const visibilitySchema = z.enum(visibilityEnum);
export const visibilityTransform: Record<
  z.infer<typeof visibilitySchema>,
  string
> = {
  all: "所有人可见",
  holding: "当前持有人可见",
  hold: "所有持有人可见",
};

export const earningVisibilityEnum = ["current", "sum"] as const;
export const earningVisibilitySchema = z.array(z.enum(earningVisibilityEnum));
export const earningVisibilityTransform: Record<
  z.infer<typeof earningVisibilitySchema>[number],
  string
> = {
  current: "持有收益",
  sum: "累计收益",
};

export const earningRateVisibilityEnum = [
  "since_establish",
  "since_this_year",
] as const;

export const earningRateVisibilitySchema = z.array(
  z.enum(earningRateVisibilityEnum)
);
export const earningRateVisibilityTransform: Record<
  z.infer<typeof earningRateVisibilitySchema>[number],
  string
> = {
  since_establish: "成立以来收益率",
  since_this_year: "今年以来收益率",
};

export const fieldVisibilityEnum = [
  "risk",
  "no",
  "establish",
  "custodian",
] as const;
export const fieldVisibilitySchema = z.array(z.enum(fieldVisibilityEnum));
export const fieldVisibilityTransform: Record<
  z.infer<typeof fieldVisibilitySchema>[number],
  string
> = {
  risk: "风险等级",
  no: "产品编码",
  establish: "成立日期",
  custodian: "托管机构",
};

export const model = mysqlTable(withPrefix("portfolio_compliance_conf"), {
  portfolioId: int("portfolio_id").primaryKey().notNull(),
  visibility: varchar("visibility", {
    length: 255,
    enum: visibilityEnum,
  }).notNull(),
  fieldVisibility: longtext("field_visibility").notNull(),
  earningVisibility: longtext("earning_visibility").notNull(),
  reportVisibility: varchar("report_visibility", {
    length: 255,
    enum: visibilityEnum,
  }).notNull(),
  earningRateVisibility: longtext("earning_rate_visibility").notNull(),
});

export const select = {
  portfolioId: model.portfolioId,
  status: sql`if (${model.portfolioId} is null, false, true)`,
  visibility: model.visibility,
  fieldVisibility: sql`${model.fieldVisibility}`.mapWith({
    mapFromDriverValue: (value) => parseJSON(value, fieldVisibilitySchema, []),
  }),
  earningVisibility: sql`${model.earningVisibility}`.mapWith({
    mapFromDriverValue: (value) =>
      parseJSON(value, earningVisibilitySchema, []),
  }),
  earningRateVisibility: sql`${model.earningRateVisibility}`.mapWith({
    mapFromDriverValue: (value) =>
      parseJSON(value, earningRateVisibilitySchema, []),
  }),
  reportVisibility: model.reportVisibility,
};

export const insertSchema = createInsertSchema(model, {
  fieldVisibility: () => fieldVisibilitySchema,
  earningVisibility: () => earningVisibilitySchema,
  earningRateVisibility: () => earningRateVisibilitySchema,
}).required();

export const selectSchema = createSelectSchema(model);
