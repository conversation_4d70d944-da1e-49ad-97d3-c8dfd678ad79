import {
  configSchema,
  typeEnum,
} from "@portfolio-service/schema/riskTestQuestionaire";
import { sql } from "drizzle-orm";
import {
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const model = mysqlTable(
  withPrefix("risk_test_questionaire"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    type: varchar("type", { enum: typeEnum, length: 255 }).notNull(),
    config: longtext("config").notNull(),
  },
  (table) => ({
    index: index("risk_test_questionaire_index").on(
      table.type,
      table.createTime
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  type: model.type,
  config: sql`${model.config}`.mapWith({
    mapFromDriverValue: (value) => parseJSON(value, configSchema, []),
  }),
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
