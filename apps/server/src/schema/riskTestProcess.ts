import {
  answersSchema,
  levelEnum,
} from "@portfolio-service/schema/riskTestQuestionaire";
import { sql } from "drizzle-orm";
import {
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  uniqueIndex,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const model = mysqlTable(
  withPrefix("risk_test_process"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),

    userId: int("user_id").notNull(),
    questionaireId: int("questionaire_id").notNull(),
    answers: longtext("answers"),
    score: int("score"),
    level: varchar("level", { enum: levelEnum, length: 255 }),

    riskTestId: int("risk_test_id"),
  },
  (table) => ({
    key: uniqueIndex("risk_test_process_key").on(table.riskTestId),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  userId: model.userId,
  riskTestId: model.riskTestId,
  questionaireId: model.questionaireId,
  answers: sql`${model.answers}`.mapWith({
    mapFromDriverValue: (value) => parseJSON(value, answersSchema, []),
  }),
  score: model.score,
  level: model.level,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export const riskTestProcessDocument = mysqlTable(
  withPrefix("risk_test_process_document"),
  {
    riskTestProcessId: int("risk_test_process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index("risk_test_process_document_risk_test_process_id_index").on(
      table.riskTestProcessId
    ),
  })
);
