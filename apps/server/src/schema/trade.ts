import { businessTypeEnum } from "@portfolio-service/schema/trade";
import { BuildColumns } from "drizzle-orm";
import {
  bigint,
  datetime,
  double,
  index,
  int,
  mysqlTable,
  MySqlTableExtraConfig,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  id: bigint("id", { mode: "number" }).primaryKey(),
  clientId: int("client_id"),
  clientIdentityNumber: varchar("client_identity_number", { length: 255 }),
  portfolioId: int("portfolio_id"),
  portfolioName: varchar("portfolio_name", { length: 255 }),
  portfolioNo: varchar("portfolio_no", { length: 255 }),
  agency: varchar("agency", { length: 255 }),
  businessType: varchar("business_type", {
    length: 255,
    enum: businessTypeEnum,
  }),
  confNumber: varchar("conf_number", { length: 255 }),
  source: varchar("source", { length: 255 }),
  applyDate: datetime("apply_date", { mode: "string" }),
  applyBalance: double("apply_balance"),
  applyShare: double("apply_share"),
  confDate: datetime("conf_date", { mode: "string" }),
  confBalance: double("conf_balance"),
  confShare: double("conf_share"),
  transactionPrice: double("transaction_price"),
  transactionFee: double("transaction_fee"),
  confStatus: varchar("conf_status", { length: 255 }),
  transferFee: double("transfer_fee"),
};

const getExtraConfig =
  (name: string) =>
  (
    table: BuildColumns<string, typeof structure, "mysql">
  ): MySqlTableExtraConfig => ({
    confNumberIndex: index(`${name}_conf_number_index`).on(table.confNumber),
  });

export const backupModel = mysqlTable(
  withPrefix("trade_backup"),
  structure,
  getExtraConfig("trade_backup")
);

export const model = mysqlTable(
  withPrefix("trade"),
  structure,
  getExtraConfig("trade")
);
