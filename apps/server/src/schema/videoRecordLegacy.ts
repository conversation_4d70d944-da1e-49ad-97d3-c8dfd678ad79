import { sql } from "drizzle-orm";
import {
  datetime,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("video_record_legacy"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: datetime("create_time"),
    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),
    fileUploadId: varchar("file_upload_id", { length: 255 }).notNull(),
    recordContent: longtext("record_content"),
  },
  (table) => ({
    index: index("video_record_legacy_index").on(
      table.userId,
      table.portfolioId
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  userId: model.userId,
  portfolioId: model.portfolioId,
  fileUploadId: model.fileUploadId,
  recordContent: sql`${model.recordContent}`.mapWith({
    mapFromDriverValue: (value) => {
      if (!value) return null;

      try {
        return JSON.parse(value);
      } catch {
        return null;
      }
    },
  }),
};

export const selectSchema = createSelectSchema(model);
export const insertSchema = createInsertSchema(model);
