import { BuildColumns } from "drizzle-orm";
import {
  bigint,
  datetime,
  double,
  index,
  int,
  mysqlTable,
  MySqlTableExtraConfig,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  id: bigint("id", { mode: "number" }).primaryKey(),
  clientId: int("client_id"),
  clientIdentityNumber: varchar("client_identity_number", { length: 255 }),
  clientName: varchar("client_name", { length: 255 }),
  clientIdentityType: varchar("client_identity_type", { length: 255 }),
  clientType: varchar("client_type", { length: 255 }),
  clientClassification: varchar("client_classification", { length: 255 }),
  portfolioId: int("portfolio_id"),
  portfolioName: varchar("portfolio_name", { length: 255 }),
  portfolioNo: varchar("portfolio_no", { length: 255 }),
  agency: varchar("agency", { length: 255 }),
  realShares: double("real_shares"),
  lastModify: datetime("last_modify", { mode: "string" }),
  dividendMethod: varchar("dividend_method", { length: 255 }),
  fundAccount: varchar("fund_account", { length: 255 }),
  fundCode: varchar("fund_code", { length: 255 }),
  currentEarnings: double("current_earnings"),
  region: varchar("region", { length: 255 }),
  department: varchar("department", { length: 255 }),
  channelPerson: varchar("channel_person", { length: 255 }),
};

const getExtraConfig =
  (name: string) =>
  (
    table: BuildColumns<string, typeof structure, "mysql">
  ): MySqlTableExtraConfig => ({
    index: index(`${name}_index`).on(
      table.portfolioId,
      table.clientIdentityNumber
    ),
  });

export const backupModel = mysqlTable(
  withPrefix("share_backup"),
  structure,
  getExtraConfig("share_backup")
);

export const model = mysqlTable(
  withPrefix("share"),
  structure,
  getExtraConfig("share")
);

export const select = {
  id: model.id,
  clientId: model.clientId,
  clientIdentityNumber: model.clientIdentityNumber,
  clientName: model.clientName,
  clientIdentityType: model.clientIdentityType,
  clientType: model.clientType,
  clientClassification: model.clientClassification,
  portfolioId: model.portfolioId,
  portfolioName: model.portfolioName,
  portfolioNo: model.portfolioNo,
  agency: model.agency,
  realShares: model.realShares,
  lastModify: model.lastModify,
  dividendMethod: model.dividendMethod,
  fundAccount: model.fundAccount,
  fundCode: model.fundCode,
  currentEarnings: model.currentEarnings,
  region: model.region,
  department: model.department,
  channelPerson: model.channelPerson,
};
