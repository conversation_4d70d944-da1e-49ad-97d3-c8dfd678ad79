import {
  double,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { videoConfigModelEnum } from "../model/ttd";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("video_config"),
  {
    id: int("id").primaryKey().autoincrement(),
    targetCode: varchar("target_code", { length: 255 }).unique().notNull(),
    recordModel: varchar("record_model", {
      enum: videoConfigModelEnum,
      length: 255,
    }).notNull(),
    words: longtext("words").notNull(),
    intervalTime: int("interval_time").notNull(),
    playSpeed: double("play_speed").notNull(),
    manageCode: varchar("manage_code", { length: 255 }).notNull(),
    openLink: varchar("open_link", { length: 255 }).notNull(),
    configId: int("config_id").notNull(),
    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),
    orderNumber: varchar("order_number", { length: 255 }),
  },
  (table) => ({
    userIdIndex: index("video_config_user_id_index").on(table.userId),
    portfolioIdIndex: index("video_config_portfolio_id_index").on(
      table.portfolioId
    ),
  })
);

export type VideoConfig = typeof model.$inferSelect;
