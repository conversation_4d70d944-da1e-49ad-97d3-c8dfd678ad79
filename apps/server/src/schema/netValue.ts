import { BuildColumns } from "drizzle-orm";
import {
  date,
  double,
  index,
  int,
  mysqlTable,
  MySqlTableExtraConfig,
  uniqueIndex,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  id: int("id").primaryKey(),
  portfolioId: int("portfolio_id").notNull(),
  date: date("date"),
  netValue: varchar("net_value", { length: 255 }),
  cumNetValue: varchar("cum_net_value", { length: 255 }),
  assetShares: varchar("asset_shares", { length: 45 }),
  assetNet: varchar("asset_net", { length: 45 }),
  dividend: varchar("dividend", { length: 45 }),
  cumDividend: varchar("cum_dividend", { length: 45 }),
  revNetValue: varchar("rev_net_value", { length: 45 }),
  dayReturn: double("day_return"),
  weekReturn: double("week_return"),
};

const getExtraConfig =
  (name: string) =>
  (
    table: BuildColumns<string, typeof structure, "mysql">
  ): MySqlTableExtraConfig => ({
    index: index(`${name}_index`).on(table.portfolioId, table.date),
  });

const getLatestExtraConfig =
  (name: string) =>
  (
    table: BuildColumns<string, typeof structure, "mysql">
  ): MySqlTableExtraConfig => ({
    key: uniqueIndex(`${name}_key`).on(table.portfolioId),
  });

export const backupModel = mysqlTable(
  withPrefix("net_value_backup"),
  structure,
  getExtraConfig("net_value_backup")
);

export const model = mysqlTable(
  withPrefix("net_value"),
  structure,
  getExtraConfig("net_value")
);

export const backupLatestModel = mysqlTable(
  withPrefix("net_value_latest_backup"),
  structure,
  getLatestExtraConfig("net_value_latest_backup")
);

export const latestModel = mysqlTable(
  withPrefix("net_value_latest"),
  structure,
  getLatestExtraConfig("net_value_latest")
);
