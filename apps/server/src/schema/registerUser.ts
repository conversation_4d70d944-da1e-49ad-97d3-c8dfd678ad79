import {
  identityTypeEnum,
  individualIdentityTypeEnum,
  userTypeEnum,
} from "@portfolio-service/schema/userType";
import { boolean, int, mysqlTable, varchar } from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable(withPrefix("register_user"), {
  id: int("id").primaryKey().autoincrement(),
  userType: varchar("user_type", { enum: userTypeEnum, length: 255 }).notNull(),

  name: varchar("name", { length: 255 }).notNull(),
  identityNumber: varchar("identity_number", { length: 255 })
    .notNull()
    .unique(),
  identityType: varchar("identity_type", {
    enum: identityTypeEnum,
    length: 255,
  }).notNull(),

  representativeName: varchar("representative_name", { length: 255 }),
  representativeIdentityNumber: varchar("representative_identity_number", {
    length: 255,
  }),
  representativeIdentityType: varchar("representative_identity_type", {
    enum: individualIdentityTypeEnum,
    length: 255,
  }),

  identityVerificationStatus: boolean("identity_verification_status")
    .notNull()
    .default(false),

  validationCode: varchar("validation_code", { length: 255 }),
  validationStatus: boolean("valiation_status").notNull().default(false),

  redoIdentityVerification: boolean("redo_identity_verification")
    .notNull()
    .default(false),
});

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type RegisterUser = typeof model.$inferSelect;
export type RegisterUserInsert = typeof model.$inferInsert;
