import { sql } from "drizzle-orm";
import {
  boolean,
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable(
  withPrefix("supplement_check_log"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" }).default(
      sql`current_timestamp()`
    ),
    userId: int("user_id"),
    portfolioId: int("portfolio_id"),
    log: longtext("log"),
    success: boolean("success"),
  },
  (table) => ({
    key: index("supplement_check_log_create_time_index").on(table.createTime),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  userId: model.userId,
  portfolioId: model.portfolioId,
  log: model.log,
  success: model.success,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
