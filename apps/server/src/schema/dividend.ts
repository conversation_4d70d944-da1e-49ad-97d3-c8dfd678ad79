import { dividendMethodEnum } from "@portfolio-service/schema/dividend";
import {
  bigint,
  datetime,
  double,
  int,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  id: bigint("id", { mode: "number" }).primaryKey(),
  clientId: int("client_id"),
  clientIdentityNumber: varchar("client_identity_number", { length: 255 }),
  portfolioId: int("portfolio_id"),
  portfolioName: varchar("portfolio_name", { length: 255 }),
  portfolioNo: varchar("portfolio_no", { length: 255 }),
  agency: varchar("agency", { length: 255 }),

  dividendMethod: varchar("dividend_method", {
    length: 255,
    enum: dividendMethodEnum,
  }),
  confNumber: varchar("conf_number", { length: 255 }),
  source: varchar("source", { length: 255 }),
  dividendDate: datetime("dividend_date", { mode: "string" }),
  reinvestmentDistributionDate: datetime("reinvestment_distribution_date", {
    mode: "string",
  }),
  dividendBasicShares: double("dividend_basic_shares"),
  dividendBalancePuf: double("dividend_balance_puf"),
  dividendBalance: double("dividend_balance"),
  dividendIndeed: double("dividend_indeed"),
  reinvestmentBalance: double("reinvestment_balance"),
  reinvestmentShares: double("reinvestment_shares"),
  reinvestmentNavpu: double("reinvestment_navpu"),
  performanceAmount: double("performance_amount"),
};

export const backupModel = mysqlTable(withPrefix("dividend_backup"), structure);

export const model = mysqlTable(withPrefix("dividend"), structure);

export const select = {
  portfolioId: model.portfolioId,
  portfolioName: model.portfolioName,
  portfolioNo: model.portfolioNo,
  agency: model.portfolioName,
  dividendMethod: model.dividendMethod,
  dividendDate: model.dividendDate,
  reinvestmentDistributionDate: model.reinvestmentDistributionDate,
  dividendBasicShares: model.dividendBasicShares,
  dividendBalancePuf: model.dividendBalancePuf,
  dividendBalance: model.dividendBalance,
  dividendIndeed: model.dividendIndeed,
  reinvestmentBalance: model.reinvestmentBalance,
  reinvestmentShares: model.reinvestmentShares,
  reinvestmentNavpu: model.reinvestmentNavpu,
  performanceAmount: model.performanceAmount,
};
