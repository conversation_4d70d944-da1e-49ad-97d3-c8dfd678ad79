import {
  boolean,
  index,
  int,
  mysqlTable,
  uniqueIndex,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable(
  withPrefix("bank_card"),
  {
    id: int("id").primaryKey().autoincrement(),
    userId: int("user_id").notNull(),
    bankNumber: varchar("bank_number", { length: 255 }).notNull(),
    bankName: varchar("bank_name", { length: 255 }),
    bankCode: varchar("bank_code", { length: 255 }),
    cardBin: varchar("card_bin", { length: 255 }),
    cardType: varchar("card_type", { length: 255 }),
    cardTypeName: varchar("card_type_name", { length: 255 }),
    len: int("len"),
    ttdValidationStatus: boolean("ttd_validation_status").notNull(),
    accountName: varchar("account_name", { length: 255 }),
    branch: varchar("branch", { length: 255 }),
    phone: varchar("phone", { length: 255 }),
  },
  (table) => ({
    userId: index("bank_card_user_id_index").on(table.userId),
    key: uniqueIndex("bank_card_key").on(table.userId, table.bankNumber),
  })
);

export const select = {
  id: model.id,
  userId: model.userId,
  bankNumber: model.bankNumber,
  bankName: model.bankName,
  bankCode: model.bankCode,
  cardBin: model.cardBin,
  cardType: model.cardType,
  cardTypeName: model.cardTypeName,
  len: model.len,
  ttdValidationStatus: model.ttdValidationStatus,
  accountName: model.accountName,
  branch: model.branch,
  phone: model.phone,
};

export const selectSchema = createSelectSchema(model);
export const insertSchema = createInsertSchema(model);
