import { sql } from "drizzle-orm";
import {
  datetime,
  double,
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import { State } from "../utils/xstate";

export const model = mysqlTable(withPrefix("redeem_process"), {
  id: int("id").primaryKey().autoincrement(),
  createTime: timestamp("create_time", { mode: "string" })
    .notNull()
    .default(sql`current_timestamp`),
  workflowId: varchar("workflow_id", { length: 255 }).notNull(),

  userId: int("user_id").notNull(),
  portfolioId: int("portfolio_id").notNull(),
  orderNumber: varchar("order_number", { length: 255 }),

  time: datetime("time", { mode: "string" }).notNull(),

  shareAmount: double("share_amount").notNull(),

  tradeApplyProcessId: int("trade_apply_process_id"),
});

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  userId: model.userId,
  portfolioId: model.portfolioId,
  orderNumber: model.orderNumber,
  time: model.time,
  shareAmount: model.shareAmount,
  tradeApplyProcessId: model.tradeApplyProcessId,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
export const updateSchema = insertSchema.partial();

export const redeemProcessDocument = mysqlTable(
  withPrefix("redeem_process_document"),
  {
    redeemProcessId: int("redeem_process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index("redeem_process_document_redeem_process_id_index").on(
      table.redeemProcessId
    ),
  })
);
