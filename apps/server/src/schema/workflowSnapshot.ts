import { statusEnum } from "@portfolio-service/schema/workflow";
import { sql } from "drizzle-orm";
import {
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { Snapshot } from "xstate";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(withPrefix("workflow_snapshot"), {
  id: varchar("id", { length: 255 }).primaryKey(),
  machineId: varchar("machine_id", { length: 255 }).notNull(),
  createTime: timestamp("create_time", { mode: "string" }).default(
    sql`current_timestamp`
  ),
  updateTime: timestamp("update_time", { mode: "string" })
    .default(sql`current_timestamp`)
    .onUpdateNow(),
  state: varchar("state", { length: 255 }).notNull(),
  status: varchar("status", { enum: statusEnum, length: 255 }).notNull(),
  snapshot: longtext("snapshot"),
});

export const select = {
  id: model.id,
  createTime: model.createTime,
  updateTime: model.updateTime,
  state: model.state,
  status: model.status,
  snapshot: sql`${model.snapshot}`.mapWith({
    mapFromDriverValue: (value) =>
      value ? (JSON.parse(value) as Snapshot<unknown>) : undefined,
  }),
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
