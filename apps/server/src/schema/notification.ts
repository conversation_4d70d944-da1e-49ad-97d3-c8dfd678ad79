import { typeEnum } from "@portfolio-service/schema/notification";
import { sql } from "drizzle-orm";
import {
  boolean,
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("notification"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    updateTime: timestamp("update_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`)
      .onUpdateNow(),
    title: varchar("title", { length: 255 }),
    content: longtext("content"),
    userId: int("user_id").notNull(),
    read: boolean("read").notNull().default(false),
    key: varchar("key", { length: 255 }),
    type: varchar("type", { enum: typeEnum, length: 255 }).notNull(),
    data: longtext("data"),
  },
  (table) => ({
    index: index("notification_index").on(
      table.userId,
      table.key,
      table.type,
      table.read,
      table.createTime
    ),
  })
);

export const selectSchema = createSelectSchema(model);
export const insertSchema = createInsertSchema(model);

export const select = {
  id: model.id,
  createTime: model.createTime,
  updateTime: model.updateTime,
  title: model.title,
  content: model.content,
  userId: model.userId,
  read: model.read,
  key: model.key,
  type: model.type,
  data: sql`${model.data}`.mapWith({
    mapFromDriverValue: (value) => {
      if (!value) return null;

      try {
        return JSON.parse(value);
      } catch {
        return null;
      }
    },
  }),
};
