import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("C_Futures_Description", {
  OBJECT_ID: varchar("OBJECT_ID", { length: 100 }).primaryKey(),
  S_INFO_DELISTDATE: varchar("S_INFO_DELISTDATE", { length: 8 }),
  S_INFO_CODE: varchar("S_INFO_CODE", { length: 40 }),
});

export const select = {
  OBJECT_ID: model.OBJECT_ID,
  S_INFO_DELISTDATE: model.S_INFO_DELISTDATE,
  S_INFO_CODE: model.S_INFO_CODE,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type IndexFuturesDescriptions = typeof model.$inferSelect;
