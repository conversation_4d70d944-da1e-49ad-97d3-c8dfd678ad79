import { sql } from "drizzle-orm";
import { int, longtext, mysqlTable, varchar } from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { withPrefix } from "../utils/db";
import { parseJSON } from "../utils/zod";

export const startDateEnum = ["establish", "invest"] as const;

export const model = mysqlTable(withPrefix("net_value_disclosure_conf"), {
  portfolioId: int("portfolio_id").primaryKey().notNull(),
  disclosePeriod: longtext("disclose_period").notNull(),
  startDate: varchar("start_date", {
    length: 255,
    enum: startDateEnum,
  }).notNull(),
});

export const disclosePeriodSchema = z.array(
  z.enum([
    "openday",
    "dividend",
    "liquidate",
    "workday",
    "friday",
    "establish",
    "invest",
  ])
);

export const disclosePeriodTransform: Record<
  z.infer<typeof disclosePeriodSchema>[number],
  string
> = {
  openday: "开放日净值",
  dividend: "分红日净值",
  liquidate: "清盘日净值",
  workday: "工作日净值",
  friday: "每周五净值",
  establish: "成立日净值",
  invest: "投资起始日净值",
};

export const startDateSchema = z.enum(startDateEnum);

export const startDateTransform: Record<
  z.infer<typeof startDateSchema>,
  string
> = {
  establish: "成立日",
  invest: "投资起始日",
};

export const select = {
  portfolioId: model.portfolioId,
  status: sql`if (${model.portfolioId} is null, false, true)`,
  disclosePeriod: sql`${model.disclosePeriod}`.mapWith({
    mapFromDriverValue: (value) => parseJSON(value, disclosePeriodSchema, []),
  }),
  startDate: model.startDate,
};

export const insertSchema = createInsertSchema(model, {
  disclosePeriod: () => disclosePeriodSchema,
}).required();

export const selectSchema = createSelectSchema(model);
