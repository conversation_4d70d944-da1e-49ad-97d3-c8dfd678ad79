import { dividendMethodEnum } from "@portfolio-service/schema/dividend";
import {
  bigint,
  datetime,
  double,
  int,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  id: bigint("id", { mode: "number" }).primaryKey(),
  code: varchar("code", { length: 255 }),
  price: double("price"),
  name: varchar("name", { length: 255 }),
  amount: double("amount"),
  date: datetime("date"),
};

export const backupModel = mysqlTable(
  withPrefix("index_price_backup"),
  structure
);

export const model = mysqlTable(withPrefix("index_price"), structure);

export const select = {
  code: model.code,
  price: model.price,
  name: model.name,
  amount: model.amount,
  date: model.date,
};
