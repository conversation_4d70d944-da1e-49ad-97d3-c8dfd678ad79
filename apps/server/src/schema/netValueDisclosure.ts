import { date, int, mysqlTable } from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

const structure = {
  portfolioId: int("portfolio_id").primaryKey(),
  netValueDate: date("net_value_date").notNull(),
};

export const model = mysqlTable(withPrefix("net_value_disclosure"), structure);
export const shareModel = mysqlTable(withPrefix("net_value_disclosure_share"), structure);

export const select = {
  portfolioId: model.portfolioId,
  netValueDate: model.netValueDate,
};
