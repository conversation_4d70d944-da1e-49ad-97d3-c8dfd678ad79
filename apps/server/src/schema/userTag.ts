import { levelEnum } from "@portfolio-service/schema/riskTestQuestionaire";
import {
  classificationEnum,
  identityTypeEnum,
  userTypeEnum,
} from "@portfolio-service/schema/userType";

import {
  boolean,
  index,
  int,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { model } from "./user";

export const tagModel = mysqlTable(
  withPrefix("user_tag"),
  {
    id: int("id").primaryKey().autoincrement(),
    // userId: int("user_id").references(() => model.id),
    userId: int("user_id"),
    tag: varchar("tag", { length: 50 }),
    // createdAt: varchar("created_at", { length: 20 }),
  }
  //   (table) => ({
  //     userIndex: index("user_tag_index").on(table.userId),
  //   })
);

export const select = {
    id: tagModel.id,
    userId: tagModel.userId,
    tag: tagModel.tag,
  };

export const tagSelectSchema = createSelectSchema(tagModel);
export const tagInsertSchema = createInsertSchema(tagModel);
export const tagUpdateSchema = tagInsertSchema.partial();
