import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";
import * as fileUpload from "./fileUpload";
import * as portfolio from "./portfolio";
import email from "../service/email";

export const model = mysqlTable(withPrefix("new_investor_basic"), {
  id: int("id").primaryKey().notNull(),
  assessment_time: date("assessment_time"),
  portfolio_client_id: int("portfolio_client_id"),
});

export const select = {
  id: model.id,
  assessment: model.assessment_time,
  portfolio_client_id: model.portfolio_client_id,
};

// export const insertSchema = createInsertSchema(model, {
//   date: () => stringToDate,
// });
export const selectSchema = createSelectSchema(model);

export type newInvestorBasic = typeof model.$inferSelect;
