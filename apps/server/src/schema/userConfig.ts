import { boolean, int, mysqlTable } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(withPrefix("user_config"), {
  userId: int("user_id").primaryKey(),
  disableRiskTest: boolean("disable_risk_test"),
});

export const select = {
  userId: model.userId,
  disableRiskTest: model.disableRiskTest,
};

export const insertSchema = createInsertSchema(model);
