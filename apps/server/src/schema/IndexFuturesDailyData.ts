import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("C_Index_Futures_EOD_Prices", {
  OBJECT_ID: varchar("OBJECT_ID", { length: 100 }).primaryKey(),
  S_INFO_WINDCODE: varchar("S_INFO_WINDCODE", { length: 40 }),
  TRADE_DT: varchar("TRADE_DT", { length: 8 }),
  S_DQ_CLOSE: decimal("S_DQ_CLOSE", { precision: 20, scale: 4 }),
  S_DQ_SETTLE: decimal("S_DQ_SETTLE", { precision: 20, scale: 4 }),
  S_DQ_CHANGE: decimal("S_DQ_CHANGE", { precision: 20, scale: 4 }),
  S_DQ_OI: decimal("S_DQ_OI", { precision: 20, scale: 4 }),
});

export const select = {
  OBJECT_ID: model.OBJECT_ID,
  S_INFO_WINDCODE: model.S_INFO_WINDCODE,
  TRADE_DT: model.TRADE_DT,
  S_DQ_CLOSE: model.S_DQ_CLOSE,
  S_DQ_SETTLE: model.S_DQ_SETTLE,
  S_DQ_CHANGE: model.S_DQ_CHANGE,
  S_DQ_OI: model.S_DQ_OI,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type IndexFuturesEODPrices = typeof model.$inferSelect;
