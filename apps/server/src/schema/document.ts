import { documentTypeEnum } from "@portfolio-service/schema/document";
import { sql } from "drizzle-orm";
import {
  boolean,
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("document"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" }).default(
      sql`current_timestamp`
    ),
    userId: int("user_id").notNull(),
    type: varchar("type", { enum: documentTypeEnum, length: 255 }),
    fileUploadId: varchar("file_upload_id", { length: 255 }),
    ttdFileId: varchar("ttd_file_id", { length: 255 }),
    comment: longtext("comment"),
    obsolete: boolean("obsolete").notNull().default(false),
  },
  (table) => ({
    index: index("document_index").on(table.userId, table.type, table.obsolete),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  userId: model.userId,
  type: model.type,
  fileUploadId: model.fileUploadId,
  ttdFileId: model.ttdFileId,
  comment: model.comment,
  obsolete: model.obsolete,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type Document = typeof model.$inferSelect;
