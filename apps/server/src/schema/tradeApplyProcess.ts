import { sql } from "drizzle-orm";
import {
  datetime,
  double,
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("trade_apply_process"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),

    type: varchar("type", {
      enum: ["subscribe", "redeem"],
      length: 255,
    }).notNull(),

    parentProcessId: int("parent_process_id").notNull(),
    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),
    orderNumber: varchar("order_number", { length: 255 }),

    time: datetime("time", { mode: "string" }).notNull(),
    amount: double("amount").notNull(),
  },
  (table) => ({
    index: index("trade_apply_process_index").on(
      table.type,
      table.parentProcessId
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  type: model.type,
  parentProcessId: model.parentProcessId,
  userId: model.userId,
  portfolioId: model.portfolioId,
  orderNumber: model.orderNumber,
  time: model.time,
  amount: model.amount,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
export const updateSchema = insertSchema.partial();

export const tradeApplyProcessDocument = mysqlTable(
  withPrefix("trade_apply_process_document"),
  {
    tradeApplyProcessId: int("trade_apply_process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index(
      "trade_apply_process_document_trade_apply_process_id_index"
    ).on(table.tradeApplyProcessId),
  })
);
