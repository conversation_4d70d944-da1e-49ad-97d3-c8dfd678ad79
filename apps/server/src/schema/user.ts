import { levelEnum } from "@portfolio-service/schema/riskTestQuestionaire";
import {
  classificationEnum,
  identityTypeEnum,
  userTypeEnum,
} from "@portfolio-service/schema/userType";

import {
  boolean,
  index,
  int,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable(
  withPrefix("user"),
  {
    id: int("id").primaryKey().autoincrement(),
    name: varchar("name", { length: 255 }).notNull(),
    type: varchar("type", { enum: userTypeEnum, length: 255 }),
    password: varchar("password", { length: 255 }),
    identityNumber: varchar("identity_number", { length: 255 })
      .notNull()
      .unique(),
    identityType: varchar("identity_type", {
      enum: identityTypeEnum,
      length: 255,
    }),
    phone: varchar("phone", { length: 255 }),
    email: varchar("email", { length: 255 }),
    wechatOpenId: varchar("wechat_open_id", { length: 255 }),
    classification: varchar("classification", {
      enum: classificationEnum,
      length: 255,
    }),
    identityVerificationStatus: boolean("identity_verification_status")
      .notNull()
      .default(false),
    ttdUserNumber: varchar("ttd_user_number", { length: 255 }),
    riskLevel: varchar("risk_level", { enum: levelEnum, length: 255 }),

    representativeName: varchar("representative_name", {
      length: 255,
    }),
    representativeIdentityNumber: varchar("representative_identity_number", {
      length: 255,
    }),
    representativeIdentityType: varchar("representative_identity_type", {
      length: 255,
    }),

    source: varchar("source", { length: 255 }),
  },
  (table) => ({
    index: index("user_index").on(table.name, table.identityNumber),
  })
);

export const select = {
  id: model.id,
  name: model.name,
  type: model.type,
  identityNumber: model.identityNumber,
  identityType: model.identityType,
  phone: model.phone,
  email: model.email,
  wechatOpenId: model.wechatOpenId,
  classification: model.classification,
  identityVerificationStatus: model.identityVerificationStatus,
  ttdUserNumber: model.ttdUserNumber,
  riskLevel: model.riskLevel,
  representativeName: model.representativeName,
  representativeIdentityNumber: model.representativeIdentityNumber,
  representativeIdentityType: model.representativeIdentityType,
};

export const selectSchema = createSelectSchema(model);
export const insertSchema = createInsertSchema(model);
export const updateSchema = insertSchema.partial();

export type User = typeof model.$inferSelect;
