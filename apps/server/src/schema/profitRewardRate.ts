import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("profit_reward_rate", {
  id: varchar("id", { length: 10 }).primaryKey(),
  name: varchar("name", { length: 45 }),
  profit_reward_rate: decimal("profit_reward_rate", { precision: 10, scale: 5 }),
  divideacosts: longtext("divideacosts"),
  dividebcosts: longtext("dividebcosts")
});

export const select = {
  id: model.id,
  name: model.name,
  profit_reward_rate: model.profit_reward_rate,
  divideacosts: model.divideacosts,
  dividebcosts: model.dividebcosts
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type ProfitRewardRate = typeof model.$inferSelect;
