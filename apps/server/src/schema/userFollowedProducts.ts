import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("user_followed_products", {
  id: int("id").primaryKey().autoincrement(),
  productName: varchar("product_name", { length: 255 }),
  clientIdentityNumber: varchar("client_identity_number", { length: 255 }),
  //   profit_reward_rate: decimal("profit_reward_rate", {
  //     precision: 10,
  //     scale: 5,
  //   }),
  //   divideacosts: longtext("divideacosts"),
  //   dividebcosts: longtext("dividebcosts"),
});

export const select = {
  id: model.id,
  productName: model.productName,
  clientIdentityNumber: model.clientIdentityNumber,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type UserFollowedProducts = typeof model.$inferSelect;
