import { sql } from "drizzle-orm";
import {
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(withPrefix("file_upload"), {
  id: varchar("id", { length: 255 }).primaryKey(),
  name: longtext("name").notNull(),
  createTime: timestamp("create_time", { mode: "string" })
    .notNull()
    .default(sql`current_timestamp`),
});

export const insertSchema = createInsertSchema(model);
