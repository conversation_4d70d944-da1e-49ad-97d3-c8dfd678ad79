import { typeEnum } from "@portfolio-service/schema/signCheck";
import {
  index,
  int,
  mysqlTable,
  uniqueIndex,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("sign_check"),
  {
    userId: int("user_id").notNull(),
    entityId: int("entity_id").notNull(),
    type: varchar("type", { length: 255, enum: typeEnum }).notNull(),
  },
  (table) => ({
    key: uniqueIndex("sign_check_key").on(table.entityId, table.type),
    index: index("sign_check_user_id").on(table.userId),
  })
);

export const selectSchema = createSelectSchema(model);
export const insertSchema = createInsertSchema(model);
