import { sql } from "drizzle-orm";
import { int, longtext, mysqlTable, timestamp } from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(withPrefix("tax_declaration"), {
  id: int("id").primaryKey().autoincrement(),
  updateTime: timestamp("update_time", { mode: "string" })
    .notNull()
    .default(sql`current_timestamp`)
    .onUpdateNow(),
  userId: int("user_id").unique().notNull(),
  formData: longtext("form_data").notNull(),
});

export const select = {
  id: model.id,
  updateTime: model.updateTime,
  userId: model.userId,
  formData: sql`${model.formData}`.mapWith({
    mapFromDriverValue: (value) => JSON.parse(value),
  }),
};
