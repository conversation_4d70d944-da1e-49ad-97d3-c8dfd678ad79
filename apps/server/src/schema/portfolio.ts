import { sql } from "drizzle-orm";
import {
  date,
  datetime,
  int,
  longtext,
  mysqlTable,
  varchar,
  double,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { OpenDay } from "open-day";
import { z } from "zod";
import { withPrefix } from "../utils/db";
import { mode } from "crypto-js";

export const stateEnum = [
  "待上线",
  "募集失败",
  "运行中",
  "清盘中",
  "已清盘",
] as const;

const structure = {
  id: int("id").primaryKey().notNull(),
  name: varchar("name", { length: 255 }),
  no: varchar("no", { length: 255 }),
  state: varchar("portfolio_state", { length: 255, enum: stateEnum }),
  publishDate: date("publish_date"),
  investDate: date("invest_date"),
  startDate: date("start_date"),
  riskLevel: varchar("risk_level", { length: 255 }),
  custodian: varchar("custodian", { length: 255 }),
  openDay: longtext("open_day_content"),

  portfolioStrategy: varchar("portfolio_strategy", { length: 255 }),
  fundManageBegin: datetime("fund_manage_begin"),
  fundManageEnd: datetime("fund_manage_end"),
  fundManager: varchar("fund_manager", { length: 255 }),

  classAShareId: int("class_a_share_id"),
  classBShareId: int("class_b_share_id"),
  classCShareId: int("class_c_share_id"),
  classDShareId: int("class_d_share_id"),

  sharpeRf: double("sharpe_rf").default(0.015),
  sortinoMar: double("sortino_mar").default(0.015),
  type: varchar("type", { length: 45 }),
  investmentAdviserType: varchar("investment_adviser_type", {
    enum: ["私募产品（非投顾）", "信托", "资管"],
    length: 255,
  }),
};

export const backupModel = mysqlTable(
  withPrefix("portfolio_backup"),
  structure
);

export const model = mysqlTable(withPrefix("portfolio"), structure);

export const select = {
  id: model.id,
  name: model.name,
  no: model.no,
  state: model.state,
  publishDate: model.publishDate,
  investDate: model.investDate,
  riskLevel: model.riskLevel,
  custodian: model.custodian,
  startDate: model.startDate,
  type: model.type,
  openDay: sql`${model.openDay}`.mapWith({
    mapFromDriverValue: (value): OpenDay[] | null => {
      try {
        return JSON.parse(value);
      } catch {
        return null;
      }
    },
  }),
  portfolioStrategy: model.portfolioStrategy,
  fundManageBegin: model.fundManageBegin,
  fundManageEnd: model.fundManageEnd,
  fundManager: model.fundManager,
  classAShareId: model.classAShareId,
  classBShareId: model.classBShareId,
  classCShareId: model.classCShareId,
  classDShareId: model.classDShareId,
  sharpeRf: model.sharpeRf,
  sortinoMar: model.sortinoMar,
};

export const stateSchema = z.enum(stateEnum);

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type Portfolio = typeof model.$inferSelect;
