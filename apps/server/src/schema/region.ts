import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
  datetime,
  decimal
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const region = mysqlTable(
    withPrefix("region"), 
    {
      id: varchar("id", { length: 255 }),
      name: varchar("name", { length: 255 }),
      parent_id: varchar("parent_id", { length: 255 }),
      lng: decimal("lng", { precision: 14, scale: 11 }),
      lat: decimal("lat", { precision: 14, scale: 11 }),
      created_at: datetime("created_at"),
      updated_at: datetime("updated_at"),
      level: int("level")
    }
  );
  export const select = {
    id: region.id,
    name: region.name,
    parent_id: region.parent_id,
    lng: region.lng,
    lat: region.lat,
    created_at: region.created_at,
    updated_at: region.updated_at,
    level: region.level
  };

// export const insertSchema = createInsertSchema(model, {
//   date: () => stringToDate,
// });
export const selectSchema = createSelectSchema(region);

export type PortfolioClient = typeof region.$inferSelect;
