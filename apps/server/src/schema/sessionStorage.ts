import {
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";

export const sessionStorage = mysqlTable(
  withPrefix("session_storage"),
  {
    sessionKey: varchar("session_key", { length: 255 }).primaryKey(),
    sessionValue: longtext("session_value"),
    principleId: int("principle_id"),
  },
  (model) => ({
    index: index("session_store_index").on(model.principleId),
  })
);
