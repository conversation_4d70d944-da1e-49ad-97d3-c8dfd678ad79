import { sql } from "drizzle-orm";
import {
  boolean,
  index,
  int,
  longtext,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { videoConfigModelEnum } from "../model/ttd";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("video_config_words"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" })
      .notNull()
      .default(sql`current_timestamp`),
    name: varchar("name", { length: 255 }),
    words: longtext("words").notNull(),
    isDefault: boolean("is_default").notNull().default(false),
    model: varchar("model", {
      enum: videoConfigModelEnum,
      length: 255,
    }).notNull(),
  },
  (table) => ({
    index: index("video_config_words_index").on(
      table.isDefault,
      table.createTime
    ),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  name: model.name,
  words: sql`${model.words}`.mapWith({
    mapFromDriverValue: (value: string) => value.split("@"),
  }),
  isDefault: model.isDefault,
  model: model.model,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type VideoConfigWords = typeof model.$inferSelect;
