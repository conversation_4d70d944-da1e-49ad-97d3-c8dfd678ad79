import { stringToDate } from "@portfolio-service/schema/utils";
import {
  boolean,
  date,
  decimal,
  index,
  int,
  longtext,
  mysqlTable,
  varchar,
  text,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const model = mysqlTable("portfolio_open_day_content", {
  id: varchar("id", { length: 10 }).primaryKey(),
  name: varchar("name", { length: 45 }),
  openDayContent: longtext("open_day_content"),
});

export const select = {
  id: model.id,
  name: model.name,
  openDayContent: model.openDayContent,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);

export type PortfolioOpenDayContent = typeof model.$inferSelect;
