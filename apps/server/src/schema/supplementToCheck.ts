import {
  varchar,
  index,
  int,
  mysqlTable,
  uniqueIndex,
} from "drizzle-orm/mysql-core";
import { withPrefix } from "../utils/db";
import { createInsertSchema } from "drizzle-zod";

export const model = mysqlTable(
  withPrefix("supplement_to_check"),
  {
    processId: int("process_id").notNull(),
    operatorId: int("operator_id").notNull(),
  },
  (table) => ({
    index: index("supplement_to_check_index").on(table.processId),
  })
);

export const altModel = mysqlTable(
  withPrefix("supplement_to_check_alt"),
  {
    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),
    productNumber: varchar("product_number", { length: 255 }).notNull(),
    operatorId: int("operator_id").notNull(),
  },
  (table) => ({
    key: uniqueIndex("supplement_to_check_alt_key").on(
      table.userId,
      table.portfolioId
    ),
  })
);

export const altInsertSchema = createInsertSchema(altModel);
