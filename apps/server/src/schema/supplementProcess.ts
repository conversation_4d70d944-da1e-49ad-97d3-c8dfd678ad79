import { sql } from "drizzle-orm";
import {
  index,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { withPrefix } from "../utils/db";

export const model = mysqlTable(
  withPrefix("supplement_process"),
  {
    id: int("id").primaryKey().autoincrement(),
    createTime: timestamp("create_time", { mode: "string" }).default(
      sql`current_timestamp`
    ),
    workflowId: varchar("workflow_id", { length: 255 }).notNull(),

    parentProcessId: int("parent_process_id"),
    userId: int("user_id").notNull(),
    portfolioId: int("portfolio_id").notNull(),
    orderNumber: varchar("order_number", { length: 255 }),
  },
  (table) => ({
    index: index("supplement_process_index").on(table.parentProcessId),
  })
);

export const select = {
  id: model.id,
  createTime: model.createTime,
  workflowId: model.workflowId,
  parentProcessId: model.parentProcessId,
  userId: model.userId,
  portfolioId: model.portfolioId,
  orderNumber: model.orderNumber,
};

export const insertSchema = createInsertSchema(model);
export const selectSchema = createSelectSchema(model);
export const updateSchema = insertSchema.partial();

export const supplementProcessDocument = mysqlTable(
  withPrefix("supplement_process_document"),
  {
    supplementProcessId: int("supplement_process_id").notNull(),
    documentId: int("document_id").primaryKey(),
  },
  (table) => ({
    index: index("supplement_process_document_supplement_process_id_index").on(
      table.supplementProcessId
    ),
  })
);
