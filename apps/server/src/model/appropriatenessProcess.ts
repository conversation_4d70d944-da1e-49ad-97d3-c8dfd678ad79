import { answersSchema } from "@portfolio-service/schema/riskTestQuestionaire";
import { classificationSchema } from "@portfolio-service/schema/userType";
import { stringToDate } from "@portfolio-service/schema/utils";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { z } from "zod";
import { insertSchema, selectSchema } from "../schema/appropriatenessProcess";
import * as document from "../schema/document";
import * as fileUpload from "../schema/fileUpload";
import * as riskTest from "../schema/riskTest";
import { paginationSchema } from "../utils/pagniation";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;
export const operatorIdSchema = z.number();

export const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  status: statusSchema.nullable(),
  state: z.string().nullable(),
  date: stringToDate.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  userId: userIdSchema,
  operatorId: operatorIdSchema,
});

export const classificationInputSchema = z.object({
  id: idSchema.optional(),
  classification: classificationSchema,
  documents: z.array(
    z.object({
      type: document.insertSchema.shape.type,
      fileUploadId: fileUpload.insertSchema.shape.id,
    })
  ),
});

export const classificationDocumentsInputSchema = z.object({
  id: idSchema,
  documents: z.array(
    z.object({
      type: document.insertSchema.shape.type,
      fileUploadId: fileUpload.insertSchema.shape.id,
    })
  ),
});

const reviewInputSchema = z.object({
  id: idSchema,
  operatorId: operatorIdSchema,
  pass: z.boolean(),
});

export const classificationReviewInputSchema = reviewInputSchema;

export const riskTestInputSchema = z
  .object({
    id: idSchema,
    questionaireId: riskTest.insertSchema.shape.questionaireId,
    answers: answersSchema,
  })
  .required();

export const riskTestReviewinputSchema = reviewInputSchema;

export const setSignPasswordInputSchema = z.object({
  id: idSchema,
  password: z.string().trim().min(6).max(6),
});

export const documentsSignInputSchema = z.object({ id: idSchema });

export const documentsReviewInputSchema = reviewInputSchema;

export type NotificationType = "complete" | "reject" | "terminate";
