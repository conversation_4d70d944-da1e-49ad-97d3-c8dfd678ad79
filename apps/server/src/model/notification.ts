import { z } from "zod";
import { insertSchema, selectSchema } from "../schema/notification";
import { paginationSchema } from "../utils/pagniation";

export const userIdSchema = selectSchema.shape.userId;
export const readSchema = selectSchema.shape.read;
export const keySchema = z.string();
export const typeSchema = selectSchema.shape.type;

const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  read: readSchema.nullable(),
  key: keySchema.nullable(),
  type: typeSchema.nullable(),
});

export const listInputScheam = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = insertSchema;
