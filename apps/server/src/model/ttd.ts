import { OrderState } from "@portfolio-service/schema/orderState";
import { IdentityType } from "@portfolio-service/schema/userType";
import { z } from "zod";

export type CreateIndividualInvestorBody = {
  name: string;
  identityType: IdentityType;
  identityNumber: string;
  valid: boolean;
};

export type CreateOrganizationInvestorBody = CreateIndividualInvestorBody & {
  representativeName: string;
  representativeIdentityType: IdentityType;
  representativeIdentityNumber: string;
};

export const verifyIdentityTypeEnum = ["duo", "trio", "quartlet"] as const;
export const verifyIdentityTypeSchema = z.enum(verifyIdentityTypeEnum);
export type VerifyIdentityType = z.infer<typeof verifyIdentityTypeSchema>;

export const verifyIdentityTypeTransform: Record<VerifyIdentityType, string> = {
  duo: "NAME_ID_CARD_VERIFICATION",
  trio: "NAME_ID_CARD_BANK_VERIFICATION",
  quartlet: "NAME_ID_CARD_BANK_MOBILE_VERIFICATION",
};

export const verifyIdentityBodySchema = z.object({
  name: z.string().trim(),
  identityNumber: z.string().trim(),
  identityType: z.string().trim().optional(),
  bankNumber: z.string().trim().optional(),
  phone: z.string().trim().optional(),
});
export type VerifyIdentityBody = z.infer<typeof verifyIdentityBodySchema>;

export const verifyIdentityInputSchema = z.object({
  type: verifyIdentityTypeSchema,
  data: verifyIdentityBodySchema,
});

export type VerifyIdentityResponse = {
  idCard: string;
  name: string;
  bankNo?: string;
  mobile?: string;
  authorResult: boolean;
  bankCardType?: "0" | "1";
  respRemark: string;
};

export const videoConfigModelEnum = [
  "QUESTION_ANSWER_MODELE",
  "FOLLOW_ANSWER_MODE",
] as const;
export const videoConfigModelSchema = z.enum(videoConfigModelEnum);
export type VideoConfigModel = z.infer<typeof videoConfigModelSchema>;

export type CreateVideoConfigBody = {
  recodeModel: VideoConfigModel;
  targetCode: string;
  words: string;
  miniAppId: string;
  backUrl: string;
  intervalTime: number;
  playSpeed: number;
  orderNo?: string;
  envVersion?: "develop" | "trial" | "release";
};

export type CreateVideoConfigResponse = CreateVideoConfigBody & {
  manageCode: string;
  openlink: string;
  configId: number;
};

export type Video = {
  bucketName: string;
  recordTime: number;
  objectKey: string;
  url: string;
};

export type DocumentForSignResponse = {
  pdfPage: number;
  bucketName: string;
  plannerInvesterId: number;
  typeName: string;
  plannerState: 0 | 1;
  signDate: number;
  url: string;
  typeCode: string;
  materialValue: string;
  objectKey: string;
  investorState: 0 | 1;
  agentSign: 0 | 1;
  backetName: string;
  manageState: 0 | 1;
  fileId: string;
};

export type CreateOrderBody = {
  productNumber: string;
  amount: number;
  userNo: string;
  time: Date;
  productDealId?: string;
  isOffline?: boolean;
};

/** 1: 待投资者签署; 2: 投资者已签署; 3: 有效; 4: 无效 */
export type FileState = 1 | 2 | 3 | 4;

export type OrderDocument = {
  materialValue: string;
  isManagerRevealBook: 0 | 1;
  fileState: FileState;
  contractNo: null;
  filePath: string;
  investorState: 0 | 1;
  plannerState: 0 | 1;
  originalValue: string;
  fileUrl: string;
  version: null;
  content: string;
  fileType: string;
};

export type OrderResponse = {
  orderNo: string;
  state: OrderState;
  contract: OrderDocument;
  risk: OrderDocument;
  supplements?: OrderDocument[];
};

export type ProductResponse = {
  bookStatus: 0 | 1;
  riskLevel: string;
  stockBookStatus: 0 | 1;
  suppleContractList: any[];
  saleType: 0 | 1;
  watermarkContractSignUrl: string;
  revealBookUrl: string;
  contractFlowNo: string;
  productName: string;
  manageCode: string;
  contractUrl: string;
  manageName: string;
  trustUserName: string;
  category: string;
  productNo: string;
  productSerialNo: string;
};

export type DataStr = {
  [name: string]: {
    value?: string | null;
    fontSize?: number;
    isMultiline?: 0 | 1;
  };
};

export const productListInputSchema = z
  .object({ name: z.string() })
  .partial()
  .optional();

export const setSignPasswordInputSchema = z.object({
  userNo: z.string(),
  password: z.string().trim().min(6).max(6),
  name: z.string().trim().optional(),
  identityNumber: z.string().trim().optional(),
  organizationName: z.string().trim().optional(),
  organizationIdentityNumber: z.string().trim().optional(),
  representativeName: z.string().trim().optional(),
});

export const verifySignPasswordInputSchema = z.object({
  userNo: z.string(),
  password: z.string().trim().min(6).max(6),
});

export type UserInfoResponse = {
  isOrg: boolean;
  signatureImage: string;
  crawlSignatureImage: string;
  signPassword: string;
};
