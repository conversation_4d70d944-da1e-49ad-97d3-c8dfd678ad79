import { stringToDate } from "@portfolio-service/schema/utils";
import { z } from "zod";
import { paginationSchema } from "../utils/pagniation";

const filterSchema = z.object({
  name: z.string().nullable(),
  identityNumber: z.string().nullable(),
  portfolioId: z.number().nullable(),
  success: z.boolean().nullable(),
  date: stringToDate.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();
