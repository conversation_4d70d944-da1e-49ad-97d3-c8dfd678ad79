import { stringToDate } from "@portfolio-service/schema/utils";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { z } from "zod";
import * as portfolio from "../schema/portfolio";
import { insertSchema, selectSchema } from "../schema/supplementProcess";
import { paginationSchema } from "../utils/pagniation";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;
export const portfolioIdSchema = portfolio.selectSchema.shape.id;

const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  portfolioId: portfolioIdSchema.nullable(),
  status: statusSchema.nullable(),
  state: z.string().nullable(),
  date: stringToDate.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

const ownFilterSchema = z.object({
  portfolioId: portfolioIdSchema.nullable(),
  state: z.string().nullable(),
});

export const ownListInputSchema = ownFilterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  operatorId: z.number(),
  parentProcessId: z.number(),
});

export const forceCreateInputSchema = z.object({
  operatorId: z.number(),
  orderNumber: z.string().min(1),
  userId: z.number(),
  portfolioId: z.number(),
});

export const createAltInputSchema = z.object({
  operatorId: z.number(),
  userId: z.number(),
  portfolioId: z.number(),
  productNumber: z.string().trim().min(1),
});

export const nextInputSchema = z.object({ id: idSchema });

export const reviewInputSchema = z.object({
  id: idSchema,
  operatorId: z.number(),
  pass: z.boolean(),
});

export const createSupplementToCheckInputSchema = z.object({
  operatorId: z.number(),
  portfolioId: portfolioIdSchema,
});

export const createSupplementToCheckAltInputSchema = z.object({
  operatorId: z.number(),
  portfolioId: portfolioIdSchema,
  productNumber: z.string().min(1),
  identityNumbers: z.array(z.string()).min(1),
});

export const terminateInputSchema = z.object({
  id: idSchema,
  operatorId: z.number(),
});

export type NotificationType = "supplementSignEntry" | "terminate";
