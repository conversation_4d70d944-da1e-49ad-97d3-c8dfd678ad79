import { dividendMethodSchema } from "@portfolio-service/schema/dividend";
import { z } from "zod";
import { paginationSchema } from "../utils/pagniation";

export const filterSchema = z.object({
  portfolio: z.string().nullable(),
  dividendMethod: dividendMethodSchema.nullable(),
});

export const dividendCalculatorFilter = z.object({
  reinvestmentDistributionDate: z.string().nullable(),
  agency: z.string().nullable(),
  portfolioName: z.string().nullable(),
});


export const inputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();
