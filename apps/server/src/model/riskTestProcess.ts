import { answersSchema } from "@portfolio-service/schema/riskTestQuestionaire";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { z } from "zod";
import * as riskTest from "../schema/riskTest";
import { insertSchema, selectSchema } from "../schema/riskTestProcess";
import { paginationSchema } from "../utils/pagniation";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;

const filterInputSchema = z.object({
  userId: userIdSchema.nullable(),
  state: z.string().nullable(),
  status: statusSchema.nullable(),
});

export const listInputSchema = paginationSchema
  .merge(filterInputSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  questionaireId: riskTest.insertSchema.shape.questionaireId,
  answers: answersSchema,
});

export const managerCreateInputSchema = z.object({
  userId: userIdSchema,
  skipQuestionnaire: z.boolean().optional(),
  operatorId: z.number(),
});

export const riskTestInputSchema = z.object({
  id: idSchema,
  questionaireId: riskTest.insertSchema.shape.questionaireId,
  answers: answersSchema,
});

export const nextInputSchema = z.object({ id: idSchema });
