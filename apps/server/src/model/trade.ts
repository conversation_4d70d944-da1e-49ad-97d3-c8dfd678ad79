import { businessTypeSchema } from "@portfolio-service/schema/trade";
import { z } from "zod";
import { paginationSchema } from "../utils/pagniation";

export const filterSchema = z.object({
  portfolio: z.string().nullable(),
  businessType: businessTypeSchema.nullable(),
});

export const tradeCalculatorFilter = z.object({
   productName: z.string().nullable(),
   applyDate:z.string().nullable()
});

export const inputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();
