import { phone } from "@portfolio-service/schema/utils";
import { z } from "zod";

export const sendCodeInputSchema = z
  .object({ type: z.literal("phone"), phone })
  .or(z.object({ type: z.literal("email"), email: z.string().email() }));

export const loginInputSchema = z
  .object({
    type: z.literal("phone"),
    phone,
    code: z.string().trim(),
  })
  .or(
    z.object({
      type: z.literal("email"),
      email: z.string().email(),
      code: z.string().trim(),
    })
  )
  .or(
    z.object({
      type: z.literal("identityNumber"),
      username: z.string().trim(),
      password: z.string().trim(),
    })
  );
