import {
  individualIdentityTypeSchema,
  organizationIdentityTypeSchema,
} from "@portfolio-service/schema/userType";
import { z } from "zod";
import { selectSchema } from "../schema/registerUser";
import { paginationSchema } from "../utils/pagniation";
import { phone } from "@portfolio-service/schema/utils";

export const filterSchema = z.object({
  userType: selectSchema.shape.userType,
  name: selectSchema.shape.name,
  identityNumber: selectSchema.shape.identityNumber,
  identityType: selectSchema.shape.identityType,
  validationStatus: selectSchema.shape.validationStatus,
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const registerIdentityValidateInputSchema = z
  .object({
    userType: z.literal("个人"),
    name: z.string().trim(),
    identityType: individualIdentityTypeSchema,
    identityNumber: z.string().trim(),
  })
  .or(
    z.object({
      userType: z.literal("机构"),
      name: z.string().trim(),
      identityType: organizationIdentityTypeSchema,
      identityNumber: z.string().trim(),
      representativeName: z.string().trim(),
      representativeIdentityType: individualIdentityTypeSchema,
      representativeIdentityNumber: z.string().trim(),
    })
  );

export const registerCodeValidateInputSchema = z.object({
  code: z.string().min(8).trim(),
});

export const sendCodeInputSchema = z
  .object({ type: z.literal("phone"), phone })
  .or(z.object({ type: z.literal("email"), email: z.string().email() }));

export const registerInputSchema = z
  .object({
    type: z.literal("phone"),
    phone,
    code: z.string().trim(),
  })
  .or(
    z.object({
      type: z.literal("email"),
      email: z.string().email(),
      code: z.string().trim(),
    })
  );

