import { z } from "zod";
import { insertSchema, selectSchema } from "../schema/videoConfigWords";
import { videoConfigModelSchema } from "./ttd";

const wordsIdScheam = selectSchema.shape.id.optional();
const wordsSchema = z.array(z.string().trim()).nonempty();

export const createInputSchema = z.object({
  userId: z.number(),
  portfolioId: z.number(),
  wordsId: wordsIdScheam,
  orderNumber: z.string().optional(),
});

export const createWordInputSchema = z.object({
  name: insertSchema.shape.name,
  words: wordsSchema,
  isDefault: z.boolean(),
  model: videoConfigModelSchema,
});

export const updateWordsInputSchema = z.object({
  id: z.number(),
  name: insertSchema.shape.name,
  words: wordsSchema,
  isDefault: z.boolean(),
});
