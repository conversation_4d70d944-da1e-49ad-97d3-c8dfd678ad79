import { z } from "zod";
import * as portfolio from "../schema/portfolio";
import { insertSchema, selectSchema } from "../schema/tradeApplyProcess";
import { stringToDate } from "@portfolio-service/schema/utils";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;
export const portfolioIdSchema = portfolio.selectSchema.shape.id;

export const createInputSchema = z.object({
  operatorId: z.number(),
  amount: insertSchema.shape.amount,
  time: stringToDate,
  parentProcessId: insertSchema.shape.parentProcessId,
  type: insertSchema.shape.type,
});

export const nextInputSchema = z.object({ id: idSchema });

export const reviewInputSchema = z.object({
  id: idSchema,
  operatorId: z.number(),
  pass: z.boolean(),
});
