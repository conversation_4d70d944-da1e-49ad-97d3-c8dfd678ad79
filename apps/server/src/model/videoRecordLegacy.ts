import { z } from "zod";
import { selectSchema } from "../schema/videoRecordLegacy";
import * as user from "../schema/user";

export const userIdSchema = selectSchema.shape.userId;
export const identityNumberSchema = user.selectSchema.shape.identityNumber;
export const portfolioIdSchema = selectSchema.shape.portfolioId;

export const listInputSchema = z
  .object({
    userId: userIdSchema.nullable(),
    identityNumber: identityNumberSchema.nullable(),
    portfolioId: portfolioIdSchema.nullable(),
  })
  .partial()
  .optional();
