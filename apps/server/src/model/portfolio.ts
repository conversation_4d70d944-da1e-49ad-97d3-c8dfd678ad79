import { categorySchema } from "@portfolio-service/schema/portfolio";
import { z } from "zod";
import { paginationSchema } from "../utils/pagniation";
import moment, { Dayjs } from "dayjs";
import { Dayjs as Moment } from "dayjs";
export const filterSchema = z.object({
  portfolio: z.string().nullable(),
  category: categorySchema.nullable(),
});

export const inputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export type PortfolioAccess = {
  access: boolean;
  reportAccess: boolean;
  holding: boolean;
};

export interface BaseReportData {
  date: Moment;
  netValue: number;
  backfill: boolean;
  beforeHigh: number;
  beforeHighDate: Moment;
  beforeLow: number;
  beforeLowDate: Moment;
  return: number;
  yearReturn: number;
  seasonReturn: number;
  monthReturn: number;
  weekReturn: number;
  dayReturn: number;
}
export interface NetValues {
  // 日期
  date: string;
  // 单位净值
  netValue: number;
  // 累计净值
  cumNetValue: number;
  // 总资产
  assetNet: number;
}

export type StartDateType = {
  type: string;
  date?: Dayjs;
};

export interface SomeNetValue {
  date: Dayjs;
  netValue: number;
}
type SequenceType = "day" | "week" | "month";
export interface ExtendReportData {
  // 最大回撤
  maxRetracement: number;
  // 年化收益
  annualizedReturn: number;
  // 年化标准差
  annualizedStd: number;
  // 年化下半标准差
  annualizedHalfStd: number;
  sharpe: number | null;
  sortino: number | null;
  doNotShow: boolean;
}
export type CalcProps = {
  selectedProductId: number;
  startDate: StartDateType;
  endDate: Dayjs;
  intervalType: string;
  valueType: string;
  indexType: string;
};
