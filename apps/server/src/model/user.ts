import { phone } from "@portfolio-service/schema/utils";
import { z } from "zod";
import { selectSchema } from "../schema/user";
import { paginationSchema } from "../utils/pagniation";
import { faceRecognitionUrlInputSchema } from "./ttdUrl";

export const nameSchema = selectSchema.shape.name;
export const identityNumberSchema = selectSchema.shape.identityNumber;

const filterSchema = z.object({
  name: nameSchema.nullable(),
  identityNumber: identityNumberSchema.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const sendCodeInputSchema = z.object({
  type: z.enum(["phone", "email"]),
});

export const verifyCodeInputSchema = z.object({ code: z.string().trim() });

export const updateLoginSendCodeInputSchema = z
  .object({ type: z.literal("phone"), phone })
  .or(z.object({ type: z.literal("email"), email: z.string().email() }));

export const updateLoginInputSchema = z
  .object({
    type: z.literal("phone"),
    phone,
    code: z.string().trim(),
  })
  .or(
    z.object({
      type: z.literal("email"),
      email: z.string().email(),
      code: z.string().trim(),
    })
  );

export const forgetPasswordFaceRecognitionUrlInputSchema =
  faceRecognitionUrlInputSchema.merge(
    z.object({
      identityNumber: z.string().trim(),
    })
  );

export const forgetPasswordFaceRecognitionInputSchema = z.object({
  encrypted: z.string(),
});

export const forgetPasswordInputSchema = z.object({
  password: z.string().trim(),
});
