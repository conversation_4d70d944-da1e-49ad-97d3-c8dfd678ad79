import { z } from "zod";
import { selectSchema } from "../schema/riskTest";
import { paginationSchema } from "../utils/pagniation";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = selectSchema.shape.userId;

export const filterSchema = z.object({
  userId: userIdSchema.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();
