import { stringToDate, stringToDateMN } from "@portfolio-service/schema/utils";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { z } from "zod";
import * as portfolio from "../schema/portfolio";
import { insertSchema, selectSchema } from "../schema/redeemProcess";
import { paginationSchema } from "../utils/pagniation";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;
export const operatorIdSchema = z.number();
export const portfolioIdSchema = portfolio.selectSchema.shape.id;

const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  portfolioId: portfolioIdSchema.nullable(),
  status: statusSchema.nullable(),
  state: z.string().nullable(),
  date: stringToDate.nullable(),
  bookDate: stringToDate.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

const ownFilterSchema = z.object({
  portfolioId: portfolioIdSchema.nullable(),
  state: z.string().nullable(),
});

export const ownListInputSchema = ownFilterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  userId: userIdSchema,
  operatorId: operatorIdSchema,
  portfolioId: portfolioIdSchema,
  shareAmount: insertSchema.shape.shareAmount,
  time: stringToDateMN,
});

export const nextInputSchema = z.object({ id: idSchema });

export const reviewInputSchema = z.object({
  id: idSchema,
  operatorId: operatorIdSchema,
  pass: z.boolean(),
});

export const terminateInputSchema = z.object({
  id: idSchema,
  operatorId: operatorIdSchema,
});

export type NotificationType = "complete" | "reject" | "terminate";
