import { z } from "zod";
import { stateSchema } from "../schema/portfolio";
import {
  insertSchema,
  visibilitySchema,
} from "../schema/portfolioComplianceConf";
import { paginationSchema } from "../utils/pagniation";
import { stringToDate } from "@portfolio-service/schema/utils";

export const portfolioIdSchema = insertSchema.shape.portfolioId;
export const statusSchema = z.enum(["已设置", "未设置"]);

export const filterSchema = z.object({
  portfolioId: portfolioIdSchema.nullable(),
  portfolioState: stateSchema.nullable(),
  status: statusSchema.nullable(),
  visibility: visibilitySchema.nullable(),
  reportVisibility: visibilitySchema.nullable(),
  portfolioPublishDate: z.tuple([stringToDate, stringToDate]).nullable(),
});

export const inputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const deleteInputSchema = insertSchema.pick({ portfolioId: true });
