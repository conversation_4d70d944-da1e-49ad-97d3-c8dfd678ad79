import { stringToDate, stringToDateMN } from "@portfolio-service/schema/utils";
import { z } from "zod";
import * as portfolio from "../schema/portfolio";
import { insertSchema, selectSchema } from "../schema/subscribeProcess";
import * as videoConfigWords from "../schema/videoConfigWords";
import { paginationSchema } from "../utils/pagniation";
import { statusSchema } from "@portfolio-service/schema/workflow";
import { typeSchema } from "@portfolio-service/schema/subscribeProcess";

export const idSchema = selectSchema.shape.id;
export const userIdSchema = insertSchema.shape.userId;
export const operatorIdSchema = z.number();
export const portfolioIdSchema = portfolio.selectSchema.shape.id;
export const videoConfigWordsIdSchema =
  videoConfigWords.selectSchema.shape.id.optional();

const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  portfolioId: portfolioIdSchema.nullable(),
  status: statusSchema.nullable(),
  state: z.string().nullable(),
  date: stringToDate.nullable(),
  bookDate: stringToDate.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

const ownFilterSchema = z.object({
  portfolioId: portfolioIdSchema.nullable(),
  state: z.string().nullable(),
});

export const ownListInputSchema = ownFilterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  userId: userIdSchema,
  operatorId: operatorIdSchema,
  portfolioId: portfolioIdSchema,
  amount: insertSchema.shape.amount,
  videoConfigWordsId: videoConfigWordsIdSchema,
  type: typeSchema,
  productNumber: z.string(),
  time: stringToDateMN,
  riskMatch: z.boolean().optional(),
  orderNumber: z.string().optional(),
});

const nextInputSchema = z.object({ id: idSchema });

export const videoRecordInputSchema = nextInputSchema;

const reviewInputSchema = z.object({
  id: idSchema,
  operatorId: operatorIdSchema,
  pass: z.boolean(),
});

export const documentsSignInputSchema = nextInputSchema;
export const contractSignInputSchema = nextInputSchema;

export const ttdOrderReviewInputSchema = reviewInputSchema.merge(
  z.object({
    redo: z
      .object({
        videoRecord: z.boolean(),
        documents: z.boolean(),
      })
      .partial()
      .optional(),
  })
);

export const contractReviewInputSchema = reviewInputSchema;

export const payInputSchema = nextInputSchema;

export const terminateInputSchema = z.object({
  id: idSchema,
  operatorId: operatorIdSchema,
});

export type NotificationType =
  | "ttdOrderReviewPass"
  | "contractSignEntry"
  | "payEntry"
  | "reject"
  | "terminate";
