import { z } from "zod";

export const signUrlInputSchema = z.object({
  fileId: z.string(),
  redirectUrl: z.string(),
  password: z.string().trim().min(6).max(6),
});

export const batchSignUrlInputSchema = z.object({
  fileIds: z.array(z.string()).optional(),
  orderNumber: z.string().optional(),
  redirectUrl: z.string(),
  password: z.string().trim().min(6).max(6),
});

export const orderSignUrlInputSchema = z.object({
  orderNumber: z.string(),
  redirectUrl: z.string(),
  password: z.string().trim().min(6).max(6),
});

export const faceRecognitionUrlInputSchema = z.object({
  redirectUrl: z.string(),
});
