import { phone } from "@portfolio-service/schema/utils";
import { z } from "zod";
import { insertSchema } from "../schema/bankCard";
import { paginationSchema } from "../utils/pagniation";

export const userIdSchema = insertSchema.shape.userId;
export const bankNumberSchema = insertSchema.shape.bankNumber;
export const accountNameSchema = z.string();

export const filterSchema = z.object({
  userId: userIdSchema.nullable(),
  bankNumber: bankNumberSchema.nullable(),
  accountName: accountNameSchema.nullable(),
});

export const listInputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = z.object({
  bankNumber: insertSchema.shape.bankNumber,
  accountName: insertSchema.shape.accountName,
  branch: insertSchema.shape.branch,
  phone: phone.nullable().optional(),
  ttdValidationStatus: insertSchema.shape.ttdValidationStatus.optional(),
});
