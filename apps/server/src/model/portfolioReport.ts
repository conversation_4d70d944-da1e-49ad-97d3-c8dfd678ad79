import { z } from "zod";
import { insertSchema } from "../schema/portfolioReport";
import { paginationSchema } from "../utils/pagniation";

export const portfolioIdSchema = insertSchema.shape.portfolioId;
export const dateSchema = insertSchema.shape.date;
export const publishedSchema = insertSchema.shape.published;

export const filterSchema = z.object({
  portfolioId: portfolioIdSchema.nullable(),
  date: dateSchema.nullable(),
  published: publishedSchema.nullable(),
});

export const inputSchema = filterSchema
  .merge(paginationSchema)
  .partial()
  .optional();

export const createInputSchema = insertSchema;
export const updateInputSchema = insertSchema.required();

export const publishInputSchema = insertSchema
  .pick({ id: true, published: true })
  .required();

export const deleteInputSchema = insertSchema.pick({ id: true }).required();
