import { z } from "zod";
import * as ttdUrl from "../model/ttdUrl";
import * as user from "../schema/user";
import { phone } from "@portfolio-service/schema/utils";

export const faceRecognitionUrlInputSchema =
  ttdUrl.faceRecognitionUrlInputSchema.merge(
    z.object({ search: z.string().optional() })
  );

export const faceRecognitionDataSchema = z.object({
  userId: user.selectSchema.shape.id,
  sessionId: z.string(),
});

export const faceRecognitionInputSchema = z.object({
  encrypted: z.string(),
  save: z.boolean().optional(),
});

export const quartletInputSchema = z.object({
  name: z.string().trim(),
  identityNumber: z.string().trim(),
  bankNumber: z.string().trim(),
  accountName: z.string().trim(),
  phone,
  branch: z.string().trim(),
  bindBankCard: z.boolean(),
  save: z.boolean().optional(),
});
