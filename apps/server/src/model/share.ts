import { sortBySchema } from "@portfolio-service/schema/share";
import { z } from "zod";
import { paginationSchema } from "../utils/pagniation";

export const filterSchema = z.object({
  portfolio: z.string().nullable(),
});

export const sortSchema = z.object({
  sortKey: sortBySchema.nullable(),
});

export const inputSchema = filterSchema
  .merge(paginationSchema)
  .merge(sortSchema)
  .partial()
  .optional();
