import { IdentityType } from "@portfolio-service/schema/userType";
import { TRPCError } from "@trpc/server";
import axios from "axios";

type TtdResponse<T = any> = {
  code: number;
  msg: string;
  data: {
    isArray: number;
    bean: T;
    list: T[];
    total: number;
    totalPage: number;
    currentPage: number;
    pageSize: number;
  };
};

const prefix = "http://localhost:10092/api/query";

type QueryParams = {
  url: string;
  serialNumber?: string;
};

export const query = async <T = any>(params: QueryParams, data?: any) => {
  const response = await axios.post<TtdResponse<T>>(prefix, data, { params });
  if (response.data?.code !== 0) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: response.data.msg || "请求失败",
    });
  }

  return response;
};

export const identityTypeTtdTransform: Record<
  IdentityType,
  string | undefined
> = {
  下属机构: "AFFILIATES",
  中国护照: "PASS_PORT",
  产品备案编码: undefined,
  其他: "OTHER",
  军官证: "OFFICER",
  军队: "ARMY",
  台胞证: "TAIWAN",
  士兵证: "SOLDIER",
  外国人永久居留身份证: "USER_ID_CARD",
  外国护照: "PASS_PORT",
  户口本: undefined,
  批文: undefined,
  文职证: undefined,
  武警: "ARMED_POLICE",
  港澳台居民居住证: undefined,
  港澳居民来往内地通行证: "HOME_TOWN",
  登记证书: undefined,
  社会团体: "SOCIAL_GROUPS",
  组织机构代码: "ORG_CODE",
  统一社会信用代码: "UNIFIED_SOCIAL_CREDIT_CODE",
  营业执照: "BUSINESS_LICENSE",
  行政机关: "EXECUTIVE_AGENCY",
  警官证: "POLICE_CARD",
  身份证: "USER_ID_CARD",
  注册号: undefined,
};

export const identityTypeValidationTransform: Record<
  IdentityType,
  string | undefined
> = {
  下属机构: undefined,
  中国护照: "1",
  产品备案编码: undefined,
  其他: "Z",
  军官证: "2",
  军队: undefined,
  台胞证: "X",
  士兵证: "Y",
  外国人永久居留身份证: undefined,
  外国护照: "1",
  户口本: undefined,
  批文: undefined,
  文职证: undefined,
  武警: undefined,
  港澳台居民居住证: undefined,
  港澳居民来往内地通行证: "C",
  登记证书: undefined,
  社会团体: undefined,
  组织机构代码: undefined,
  统一社会信用代码: undefined,
  营业执照: undefined,
  行政机关: undefined,
  警官证: "G",
  身份证: "0",
  注册号: undefined,
};
