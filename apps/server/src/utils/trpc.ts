import { TRPCError, initTRPC } from "@trpc/server";
import * as trpcExpress from "@trpc/server/adapters/express";
import dayjs from "dayjs";
import { ZodError } from "zod";
import env from "../config/env";
import identityVerificationService from "../service/identityVerification";
import userService from "../service/user";
import { isDevEnv } from "./dev";

export const createContext = ({
  req,
  res,
}: trpcExpress.CreateExpressContextOptions) => ({ req, res });

type Context = Awaited<ReturnType<typeof createContext>>;

const t = initTRPC.context<Context>().create({
  errorFormatter: ({ shape, error }) => {
    const zodError =
      error.code === "BAD_REQUEST" && error.cause instanceof ZodError
        ? error.cause.flatten()
        : null;

    const cause = zodError ? null : error.cause?.message || null;
    const { stack, ...rest } = shape.data;

    const data = { ...rest, cause, zodError };
    return { ...shape, data };
  },
});

export const router = t.router;
export const publicProcedure = t.procedure;

export const logedInProcedure = publicProcedure.use(async ({ ctx, next }) => {
  const { user } = ctx.req.session;
  if (!user) {
    throw new TRPCError({ code: "FORBIDDEN", message: "未登录" });
  }

  return next({
    ctx: { ...ctx, user },
  });
});

export const forgetPasswordProcedure = publicProcedure.use(
  async ({ ctx, next }) => {
    const { forgetPasswordUser } = ctx.req.session;
    if (!forgetPasswordUser) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "会话失效，请重新操作",
      });
    }

    return next({
      ctx: { ...ctx, forgetPasswordUser },
    });
  }
);

export const protectedProcedure = logedInProcedure.use(
  async ({ ctx, next }) => {
    const validated = await identityVerificationService.validatedByUserId(
      ctx.user.id
    );
    if (!validated) {
      throw new TRPCError({ code: "FORBIDDEN", message: "请先完成实名认证" });
    }

    return next({ ctx });
  }
);

export const privateProcedure = publicProcedure.use(({ ctx, next }) => {
  if (!isDevEnv()) {
    const { secret } = ctx.req.headers;
    if (!env.SECRET || secret != env.SECRET) {
      throw new TRPCError({ code: "NOT_FOUND" });
    }
  }

  return next({ ctx });
});

export const registerProcedure = publicProcedure.use(async ({ ctx, next }) => {
  const { registerUser } = ctx.req.session;
  if (!registerUser) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "会话失效，请重新操作",
    });
  }

  return next({ ctx: { ...ctx, registerUser } });
});

export const identityVerifiedProcedure = protectedProcedure.use(
  async ({ ctx, next }) => {
    const { identityVerificationTime } = ctx.req.session;
    const time = identityVerificationTime
      ? dayjs(identityVerificationTime)
      : undefined;

    if (!time || !time.isValid() || dayjs().diff(time, "minute") > 15) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        cause: "identityVerification",
        message: "身份认证失效，请重新认证",
      });
    }

    return next({ ctx });
  }
);

export const forgetPasswordIdentityVerifiedProcedure =
  forgetPasswordProcedure.use(async ({ ctx, next }) => {
    const { forgetPasswordIdentityVerificationTime } = ctx.req.session;
    const time = forgetPasswordIdentityVerificationTime
      ? dayjs(forgetPasswordIdentityVerificationTime)
      : undefined;

    if (!time || !time.isValid() || dayjs().diff(time, "minute") > 15) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        cause: "identityVerification",
        message: "身份认证失效，请重新认证",
      });
    }

    return next({ ctx });
  });

export const updateLoginProcedure = protectedProcedure.use(
  async ({ ctx, next }) => {
    const user = await userService.getById(ctx.user.id);
    if (!!user.email || !!user.phone) {
      const { genericCodeVerificationTime } = ctx.req.session;
      const time = genericCodeVerificationTime
        ? dayjs(genericCodeVerificationTime)
        : undefined;

      if (!time || !time.isValid() || dayjs().diff(time, "minute") > 15) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          cause: "genericCodeVerification",
          message: "会话失效，请重新认证",
        });
      }
    }

    return next({ ctx });
  }
);
