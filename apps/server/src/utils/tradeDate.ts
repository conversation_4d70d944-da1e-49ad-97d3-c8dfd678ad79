import { datePattern } from "@portfolio-service/schema/utils";
import { Dayjs } from "dayjs";
import { range } from "lodash";
import { tradeDateStorage } from "../store/tradeDate";

export const subtractMany = (date: Dayjs, days: number) => {
  for (const _ of range(0, days)) {
    date = subtractOne(date);
  }

  return date;
};

export const subtractOne = (date: Dayjs) => {
  return getLatestTradeDate(date.subtract(1, "day"));
};

export const getLatestTradeDate = (offset: Dayjs) => {
  let date = offset.startOf("day");
  while (!tradeDateStorage.value.has(date.format(datePattern))) {
    if (date.year() < 2019) break;
    date = date.subtract(1, "day");
  }
  return date;
};
