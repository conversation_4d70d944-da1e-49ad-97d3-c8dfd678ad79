export default class LimitedSizeMap<K, V> extends Map<K, V> {
  private maxSize: number;

  constructor(maxSize: number) {
    super();
    this.maxSize = maxSize;
  }

  override set(key: K, value: V) {
    if (this.size >= this.maxSize) {
      const firstKey = this.keys().next().value;
      this.delete(firstKey);
    }
    this.set(key, value);

    return this;
  }
}

export class LimitedSizeMapWithOnDelete<K, V> extends LimitedSizeMap<K, V> {
  private onDelete: (value: V) => void;

  constructor(maxSize: number, onDelete: (value: V) => void) {
    super(maxSize);
    this.onDelete = onDelete;
  }

  override delete(key: K): boolean {
    const value = this.get(key);
    if (value != null) {
      this.onDelete(value);
    }

    return super.delete(key);
  }
}
