import crypto from "crypto";

const algorithm = "aes-256-cbc";
const key = crypto.randomBytes(32);

export const encrypt = (text: string) => {
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, Buffer.from(key), iv);
  let encrypted = cipher.update(text);
  encrypted = Buffer.concat([encrypted, cipher.final()]);

  return iv.toString("hex") + encrypted.toString("hex");
};

export const decrypt = (encrypted: string) => {
  const ivHex = encrypted.slice(0, 32);
  const encryptedTextHex = encrypted.slice(32);
  if (!ivHex || !encryptedTextHex) return;

  const iv = Buffer.from(ivHex, "hex");
  const encryptedText = Buffer.from(encryptedTextHex, "hex");

  const decipher = crypto.createDecipheriv(algorithm, Buffer.from(key), iv);
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);

  return decrypted.toString();
};
