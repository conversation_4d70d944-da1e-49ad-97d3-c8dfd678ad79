import dayjs from "dayjs";
import {
  ColumnBaseConfig,
  ExtractTablesWithRelations,
  and,
  eq,
  gte,
  inArray,
  lt,
} from "drizzle-orm";
import {
  MySqlTransaction as $MySqlTransaction,
  MySqlColumn,
} from "drizzle-orm/mysql-core";
import {
  MySql2PreparedQueryHKT,
  MySql2QueryResultHKT,
} from "drizzle-orm/mysql2";

export const dateEq = (
  column: MySqlColumn<ColumnBaseConfig<"date", string>>,
  date: Date
) => {
  return eq(column, date.toISOString());
};

export const dateInArray = (
  column: MySqlColumn<ColumnBaseConfig<"date", string>>,
  dates: Date[]
) => {
  return inArray(
    column,
    dates.map((date) => date.toISOString())
  );
};

export const dateGte = (
  column: MySqlColumn<ColumnBaseConfig<"date", string>>,
  date: Date
) => {
  return gte(column, date.toISOString());
};

export const dateLt = (
  column: MySqlColumn<ColumnBaseConfig<"date", string>>,
  date: Date
) => {
  return lt(column, date.toISOString());
};

export const timestampEqDate = (
  column: MySqlColumn<ColumnBaseConfig<"string", string>>,
  date: Date
) => {
  const nextDay = dayjs(date).add(1, "day");

  return and(
    gte(column, dayjs(date).format("YYYY-MM-DD HH:mm:ss")),
    lt(column, nextDay.format("YYYY-MM-DD HH:mm:ss"))
  );
};

export type MySqlTransaction = $MySqlTransaction<
  MySql2QueryResultHKT,
  MySql2PreparedQueryHKT,
  Record<string, never>,
  ExtractTablesWithRelations<Record<string, never>>
>;
