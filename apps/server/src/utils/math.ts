import { z } from "zod";

export const multiplyIgnoreNaN = (...values: (number | null | undefined)[]) => {
  let result: number | undefined = undefined;

  for (const value of values) {
    const valueParsed = z.number().safeParse(value);
    if (!valueParsed.success) {
      continue;
    }

    if (result === undefined) {
      result = valueParsed.data;
      continue;
    }

    result = result * valueParsed.data;
  }

  return result;
};
