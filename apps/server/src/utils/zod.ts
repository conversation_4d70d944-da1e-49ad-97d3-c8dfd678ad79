import { z } from "zod";

export function parseJSON<T extends z.ZodType>(
  value: any,
  schema: T,
  defaultValue: z.infer<T>
): z.infer<T>;

export function parseJSON<T extends z.ZodType>(
  value: any,
  schema: T,
  defualtValue?: undefined
): z.infer<T> | undefined;

export function parseJSON<T extends z.ZodType>(
  value: any,
  schema: T,
  defaultValue?: z.infer<T> | undefined
): z.infer<T> | undefined {
  try {
    return schema.parse(JSON.parse(value));
  } catch {
    return defaultValue;
  }
}
