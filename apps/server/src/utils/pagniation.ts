import { asc, desc } from "drizzle-orm";
import {
  MySqlSelect,
  MySqlTableWithColumns,
  TableConfig,
} from "drizzle-orm/mysql-core";
import { z } from "zod";

export const paginationSchema = z.object({
  page: z.number().min(0).nullable(),
  pageSize: z.number().min(1).nullable(),
  sortKey: z.string().nullable(),
  sortOrder: z.enum(["asc", "desc"]).nullable(),
});

const paginationPartialSchema = paginationSchema.partial();

export type Pagination = z.infer<typeof paginationPartialSchema>;

export const withPagination = <
  TQuery extends MySqlSelect,
  TModel extends TableConfig,
>(
  query: TQuery,
  pagination?: Pagination,
  model?: MySqlTableWithColumns<TModel>
) => {
  if (!pagination) return query;

  const page = pagination.page || 0;
  const pageSize = pagination.pageSize || 20;

  query.limit(pageSize).offset(page * pageSize);

  if (!model) return query;

  const sortKey = pagination.sortKey;
  if (!sortKey) return query;

  const sortFn = pagination.sortOrder === "desc" ? desc : asc;
  query.orderBy(sortFn(model[sortKey]));

  return query;
};

export const paginationResult = <T>(items: T[], amount: number) => ({
  items,
  amount,
});
