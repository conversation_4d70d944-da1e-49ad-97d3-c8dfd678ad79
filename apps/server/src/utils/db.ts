import { TRPCError } from "@trpc/server";
import { sql } from "drizzle-orm";
import { MySqlColumn } from "drizzle-orm/mysql-core";
import env from "../config/env";

export const runHandlingDuplicateEntryError = async <T>(
  block: () => Promise<T>
): Promise<T> => {
  try {
    return await block();
  } catch (e) {
    if (!(e instanceof Error)) throw e;
    if (!e.message.includes("Duplicate entry")) throw e;
    throw new TRPCError({ code: "BAD_REQUEST" });
  }
};

export const withPrefix = (name: string) => {
  const prefix = env.DB_PREFIX;
  if (!prefix) return name;

  return `${prefix}_${name}`;
};

export const convertDouble = <T extends MySqlColumn>(column: T) => {
  return sql<number | null>`${column} + 0.0`;
};
