import {
  appId,
  batchSignUrl,
  faceRecognitionUrl,
  orderSignUrl,
  signUrl,
} from "../config/ttd";
import CryptoJs from "crypto-js";
import { z } from "zod";
import env from "../config/env";

const urlKey = env.TTD_URL_KEY;
const urlIv = env.TTD_URL_IV;

const utf8Parse = (value: string) => {
  return CryptoJs.enc.Utf8.parse(value);
};

const aesEncode = (value: string, key: string, iv: string) => {
  return CryptoJs.AES.encrypt(utf8Parse(value), utf8Parse(key), {
    mode: CryptoJs.mode.CBC,
    padding: CryptoJs.pad.Pkcs7,
    iv: utf8Parse(iv),
  }).toString();
};

const base64 = (value: string) => {
  return CryptoJs.enc.Base64.stringify(utf8Parse(value)).toString();
};

export const cryptoEncode = (value: string) => {
  return base64(aesEncode(value, urlKey, urlIv));
};

const signUrlParamsSchema = z.object({
  fileId: z.string(),
  userNo: z.string(),
  redirectUrl: z.string(),
});

export const getSignUrl = ({
  fileId,
  userNo,
  redirectUrl,
}: z.infer<typeof signUrlParamsSchema>) => {
  const params = new URLSearchParams();
  params.set("app_id", cryptoEncode(appId));
  params.set("file_id", cryptoEncode(fileId));
  params.set("sign_type", cryptoEncode("1"));
  params.set("user_no", cryptoEncode(userNo));
  params.set("redirect_uri", redirectUrl);

  return signUrl + "?" + params.toString();
};

const batchSignUrlParamsSchema = z.object({
  fileIds: z.array(z.string()).optional(),
  orderNumber: z.string().optional(),
  userNo: z.string(),
  redirectUrl: z.string(),
});

export const getBatchSignUrl = ({
  fileIds,
  orderNumber,
  userNo,
  redirectUrl,
}: z.infer<typeof batchSignUrlParamsSchema>) => {
  const params = new URLSearchParams();

  params.set("app_id", cryptoEncode(appId));
  if (!!fileIds?.length) {
    params.set("filed_ids", cryptoEncode(fileIds.join(",")));
  }
  if (!!orderNumber) {
    params.set("order_no", cryptoEncode(orderNumber));
  }

  params.set("user_no", cryptoEncode(userNo));
  params.set("redirect_uri", redirectUrl);

  return batchSignUrl + "?" + params.toString();
};

const orderSignUrlParamsSchema = z.object({
  orderNumber: z.string(),
  redirectUrl: z.string(),
});

export const getOrderSignUrl = ({
  orderNumber,
  redirectUrl,
}: z.infer<typeof orderSignUrlParamsSchema>) => {
  const params = new URLSearchParams();

  params.set("app_id", cryptoEncode(appId));
  params.set("order_no", cryptoEncode(orderNumber));
  params.set("redirect_uri", redirectUrl);

  return orderSignUrl + "?" + params.toString();
};

export const faceRecognitionUrlParamsSchema = z.object({
  redirectUrl: z.string(),
  name: z.string(),
  identityNumber: z.string(),
});

export const getFaceRecognitionUrl = ({
  redirectUrl,
  name,
  identityNumber,
}: z.infer<typeof faceRecognitionUrlParamsSchema>) => {
  const params = new URLSearchParams();

  params.set("app_id", cryptoEncode(appId));
  params.set("user_name", cryptoEncode(name));
  params.set("id_no", cryptoEncode(identityNumber));
  params.set("redirect_uri", redirectUrl);

  return faceRecognitionUrl + "?" + params.toString();
};
