import { sha1 } from "object-hash";

const createMutex = () => {
  let promise: Promise<void> | undefined = undefined;
  let resolve: ((value: void) => void) | undefined = undefined;

  const acquire = async () => {
    if (!!promise) {
      await promise;

      if (!!promise) {
        await acquire();
        return;
      }
    }

    promise = new Promise((res) => (resolve = res));
  };

  const release = () => {
    promise = undefined;
    resolve?.();
  };

  const execute = async <T>(block: () => T | Promise<T>) => {
    await acquire();

    try {
      return await block();
    } finally {
      release();
    }
  };

  return { acquire, release, execute };
};

const globalMutex = createMutex();

const mutexDict: Record<string, ReturnType<typeof createMutex>> = {};

export const withMutex = async <T>(key: any, block: () => T | Promise<T>) => {
  const hashKey = sha1(key);

  await globalMutex.execute(() => {
    if (!mutexDict[hashKey]) {
      mutexDict[hashKey] = createMutex();
    }
  });

  return await mutexDict[hashKey].execute(block);
};
