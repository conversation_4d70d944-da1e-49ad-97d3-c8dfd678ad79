import { AsyncLocalStorage } from "async_hooks";
import { db } from "../config/db";
import { MySqlTransaction } from "./drizzle";

class Transaction {
  private asyncLocalStorage: AsyncLocalStorage<MySqlTransaction>;

  constructor() {
    this.asyncLocalStorage = new AsyncLocalStorage();
  }

  async run<T>(
    block: (tx: MySqlTransaction) => Promise<T>,
    options?: { new?: boolean }
  ) {
    if (!options?.new) {
      const active = this.getActive();
      if (!!active) {
        return await block(active);
      }
    }

    return await db.transaction(async (tx) => {
      return await this.asyncLocalStorage.run(tx, () => block(tx));
    });
  }

  getActive() {
    return this.asyncLocalStorage.getStore();
  }
}

export default new Transaction();
