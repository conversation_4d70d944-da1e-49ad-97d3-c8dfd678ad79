import { eq } from "drizzle-orm";
import { SessionData, Store } from "express-session";
import { db } from "../config/db";
import { sessionStorage } from "../schema/sessionStorage";

export class SessionStore extends Store {
  get(
    sid: string,
    callback: (err: any, session?: SessionData | null | undefined) => void
  ): void {
    db.select()
      .from(sessionStorage)
      .where(eq(sessionStorage.sessionKey, sid))
      .then(([result]) => {
        const data = result?.sessionValue;
        if (data) {
          callback(undefined, JSON.parse(data));
        } else {
          callback(undefined, undefined);
        }
      })
      .catch((err) => callback(err));
  }
  set(
    sid: string,
    session: SessionData,
    callback?: ((err?: any) => void) | undefined
  ): void {
    const data = session ? JSON.stringify(session) : undefined;
    const userId = session?.user?.id;

    const promise = db
      .delete(sessionStorage)
      .where(eq(sessionStorage.sessionKey, sid))
      .then(() =>
        db.insert(sessionStorage).values({
          sessionKey: sid,
          sessionValue: data,
          principleId: userId,
        })
      );

    if (callback) promise.then(() => callback()).catch((err) => callback(err));
  }
  destroy(sid: string, callback?: ((err?: any) => void) | undefined): void {
    const promise = db
      .delete(sessionStorage)
      .where(eq(sessionStorage.sessionKey, sid));

    if (callback) promise.then(() => callback()).catch((err) => callback(err));
  }
}
