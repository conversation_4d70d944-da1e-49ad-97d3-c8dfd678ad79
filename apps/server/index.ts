import cluster from "cluster";
import fs from "fs";
import { z } from "zod";
import { loginInputSchema } from "./src/model/auth";
import { registerInputSchema } from "./src/model/register";
import {
  updateLoginInputSchema,
  verifyCodeInputSchema,
} from "./src/model/user";
import type { RegisterUser } from "./src/schema/registerUser";
import type { User } from "./src/schema/user";

declare module "express-session" {
  interface SessionData {
    user: User;
    registerUser: RegisterUser;
    forgetPasswordUser: User;

    registerValidate: z.infer<typeof registerInputSchema>;
    loginValidate: z.infer<typeof loginInputSchema>;
    updateLoginValidate: z.infer<typeof updateLoginInputSchema>;
    genericCodeValidate: z.infer<typeof verifyCodeInputSchema>;

    identityVerificationTime: string;
    genericCodeVerificationTime: string;
    forgetPasswordIdentityVerificationTime: string;
  }
}

if (cluster.isPrimary) {
  console.log(`Primary ${process.pid} is running`);

  cluster.fork();
  cluster.on("disconnect", (worker) => {
    console.log(`worker ${worker.process.pid} disconnected.`);
    cluster.fork();
  });
} else {
  import("./src/index").then(() => {
    fs.writeFileSync("./server.pid", process.pid.toString());
    console.log(`Worker ${process.pid} started`);
  });
}
