{"name": "server", "private": true, "scripts": {"dev": "ts-node index.ts", "start": "ts-node index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@alicloud/dysmsapi20170525": "^3.0.0", "@alicloud/openapi-client": "^0.4.10", "@portfolio-service/currency": "workspace:^", "@portfolio-service/schema": "workspace:^", "@portfolio-service/typescript-config": "workspace:^", "@trpc/server": "^10.45.1", "adm-zip": "^0.5.16", "bankcard": "^3.1.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cron": "^3.1.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "drizzle-orm": "^0.30.4", "drizzle-zod": "^0.5.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-fileupload": "^1.5.0", "express-session": "^1.18.0", "lodash": "^4.17.21", "mysql2": "^3.9.2", "nodemailer": "^6.9.14", "object-hash": "^3.0.0", "open-day": "git+https://gitlab.yanfuinvest.com/yzy/open-day.git#fb264c799a956063b79133b5b81609f6de096b29", "uuid": "^9.0.1", "webdav": "^4.11.2", "xstate": "^5.13.1", "zod": "^3.22.4"}, "devDependencies": {"@types/adm-zip": "^0.5.7", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/crypto-js": "^4.2.2", "@types/exceljs": "^1.3.2", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.5.0", "@types/express-session": "^1.18.0", "@types/lodash": "^4.17.0", "@types/nodemailer": "^6.4.15", "@types/object-hash": "^3.0.6", "@types/uuid": "^9.0.8", "axios": "^1.6.8", "drizzle-kit": "^0.20.14", "ts-node": "^10.9.2"}}