import type { Config } from "drizzle-kit";
import fs from "fs";
import env from "./src/config/env";

const prefix = "./src/schema";
const files = fs.readdirSync(prefix).map((file) => `${prefix}/${file}`);

export default {
  schema: files,
  out: "./migration",
  driver: "mysql2",
  dbCredentials: {
    host: env.DB_HOST,
    port: env.DB_PORT ? parseInt(env.DB_PORT) : undefined,
    database: env.DB_NAME,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
  },
} satisfies Config;
