{"name": "client", "private": true, "scripts": {"dev": "umi dev", "build": "umi build", "build:prod": "cross-env MODE=production umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@portfolio-service/schema": "workspace:^", "@trpc-swr/client": "^2.0.1", "@trpc/client": "^10.45.1", "antd-mobile": "^5.34.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.6.8", "bankcard": "^3.1.1", "classnames": "^2.5.1", "dayjs": "^1.11.10", "echarts": "^5.6.0", "file-type": "^21.0.0", "immer": "^10.1.1", "lodash": "^4.17.21", "open-day": "git+https://gitlab.yanfuinvest.com/yzy/open-day.git#fb264c799a956063b79133b5b81609f6de096b29", "react-file-viewer": "^1.2.1", "swr": "^2.2.5", "umi": "^4.1.2", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/lodash": "^4.17.0", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/wx-js-sdk-dt": "^1.4.8", "@umijs/plugins": "^4.1.2", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "tailwindcss": "^3", "typescript": "^5.0.3"}}