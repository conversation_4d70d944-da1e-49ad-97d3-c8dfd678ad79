import CompressionWebpackPlugin from 'compression-webpack-plugin';
import { defineConfig } from 'umi';

export default defineConfig({
  chainWebpack: (memo) => {
    memo
      .plugin('compression')
      .use(CompressionWebpackPlugin, [
        { algorithm: 'gzip', test: /\.(mjs|js|css)$/ },
      ]);
  },
  cssLoaderModules: { exportLocalsConvention: 'camelCase' },
  define: { 'process.env.MODE': process.env.MODE },
  esbuildMinifyIIFE: true,
  hash: true,
  headScripts: ['https://res.wx.qq.com/open/js/jweixin-1.6.0.js'],
  metas: [
    {
      name: 'viewport',
      content:
        'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover',
    },
  ],
  npmClient: 'pnpm',
  plugins: ['./plugins/modifyTSConfig', '@umijs/plugins/dist/tailwindcss'],
  proxy: {
    '/api': {
      changeOrigin: true,
      target: 'http://localhost:10091',
    },
    '/trpc': {
      changeOrigin: true,
      target: 'http://localhost:10091',
    },
  },
  svgr: { ref: true },
  tailwindcss: {},
  targets: { chrome: 75 },
});
