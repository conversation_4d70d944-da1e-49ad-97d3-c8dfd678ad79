server {
    listen 80;
    server_name localhost;

    gzip on;
    gzip_static on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/xml text/javascript application/javascript application/json;
    gzip_vary on;

    index index.htm index.html;

    include mime.types;
    types {
        application/javascript js mjs;
    }

    location = / {
        root /app;
        add_header Cache-Control no-cache;
        expires 0;
        try_files /index.html =404;
    }

    location / {
        root /app;
        try_files $uri $uri/ @index;
    }

    location @index {
        root /app;
        add_header Cache-Control no-cache;
        expires 0;
        try_files /index.html =404;
    }

    location /api/ {
        proxy_pass http://host.docker.internal:10091;
    }

    location /trpc/ {
        proxy_pass http://host.docker.internal:10091;
    }
}