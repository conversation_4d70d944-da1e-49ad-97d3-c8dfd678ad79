import { z } from 'zod';

export const toCNY = (value: number | null | undefined): string | undefined => {
  const valueParsed = z.number().min(0).safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  });
};

export const toSignedCNY = (
  value: number | null | undefined,
): string | undefined => {
  const valueParsed = z.number().safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  });
};

export const toCN = (
  value: number | null | undefined,
  precision: number = 2,
): string | undefined => {
  const valueParsed = z.number().min(0).safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });
};

export const toSignedCN = (
  value: number | null | undefined,
  precision: number = 2,
): string | undefined => {
  const valueParsed = z.number().safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });
};

export const toInt = (value: number | null | undefined): string | undefined => {
  const valueParsed = z.number().min(0).safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    maximumFractionDigits: 0,
  });
};

export const toPercent = (
  value: number | null | undefined,
): string | undefined => {
  const valueParsed = z.number().safeParse(value);
  if (!valueParsed.success) return;

  return valueParsed.data.toLocaleString('zh-CN', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    signDisplay: 'always',
  });
};
