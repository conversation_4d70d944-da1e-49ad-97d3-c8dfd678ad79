import { DependencyList, EffectCallback, useEffect, useRef } from 'react';

/** @returns whether the effect will be triggered in the current life cycle */
export const useEffectTriggered = (
  effect: EffectCallback,
  deps?: DependencyList,
) => {
  const ref = useRef<typeof deps>(deps);

  const changed = !isShallowEqual(ref.current, deps);
  ref.current = deps;

  useEffect(effect, deps);

  return changed;
};

const isShallowEqual = (
  a: DependencyList | undefined,
  b: DependencyList | undefined,
) => {
  if (a === b) {
    return true;
  }

  if (!a || !b) {
    return false;
  }

  if (a.length !== b.length) {
    return false;
  }

  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) {
      return false;
    }
  }

  return true;
};
