import { useSearchParams } from 'umi';

export const getSearchString = () => {
  const searchParams = new URLSearchParams(location.search);

  const searchString = searchParams.toString();
  if (!searchString) return '';

  return '?' + searchString;
};

export const useSearchString = () => {
  const [searchParams] = useSearchParams();

  const searchString = searchParams.toString();
  if (!searchString) return '';

  return '?' + searchString;
};
