import { TRPCClientError } from '@trpc/client';
import { Space, Toast } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import React from 'react';
import type { AppRouter } from 'server/typings';
import { SWRResponse } from 'swr';

export const voidKey = 'void';

export const fetcher = (url: string) => fetch(url);

export const extractErrorMessage = (e: unknown) => {
  const error = e as TRPCClientError<AppRouter> | undefined;
  const message = error?.message || error?.data?.code || error || '未知错误';

  return typeof message === 'string' ? message : JSON.stringify(message);
};

export const onError = (e: unknown) => {
  Toast.show({
    icon: (
      <Space block justify="center">
        <CloseOutline />
      </Space>
    ),
    content: extractErrorMessage(e),
  });
};

export const Prefetch = <T extends SWRResponse>({
  query,
}: {
  query: () => T;
}) => {
  query();

  return <React.Fragment />;
};
