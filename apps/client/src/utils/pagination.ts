import { range } from 'lodash';
import { useMemo, useState } from 'react';
import { useEffectTriggered } from './react';

type Params = { pageSize: number; resetDeps?: unknown[] };

export const usePagination = ({ pageSize, resetDeps }: Params) => {
  let [page, setPage] = useState(0);

  const triggered = useEffectTriggered(() => {
    setPage(0);
  }, resetDeps || []);

  page = triggered ? 0 : page;

  const prevPages = range(0, page);

  const useHelpers = useMemo(() => {
    return <T>(data: { items: T[]; amount: number } | undefined) => {
      const hasMore = data
        ? prevPages.length * pageSize + data.items.length < data.amount
        : true;

      const empty = !!data && !data?.items?.length && !page;

      return { hasMore, empty };
    };
  }, [page, pageSize]);

  const reset = () => setPage(0);

  const nextPage = () => setPage((prev) => prev + 1);

  return { page, prevPages, useHelpers, reset, nextPage };
};

type CursorParams<T> = {
  firstOffset: T;
  resetDeps: unknown[];
};

export const useCursorPagination = <TOffset>({
  firstOffset,
  resetDeps,
}: CursorParams<TOffset>) => {
  let [cursors, setCursors] = useState<TOffset[]>([firstOffset]);
  console.log(cursors);

  const triggered = useEffectTriggered(() => {
    setCursors([firstOffset]);
  }, resetDeps || []);

  cursors = triggered ? [firstOffset] : cursors;

  const prevPages = cursors.slice(0, Math.max(0, cursors.length - 1));

  const useHelpers = useMemo(() => {
    return <TData>(data: TData[] | undefined) => {
      const hasMore = !!data?.length;
      const empty = !!data && !data.length && !cursors.length;

      const nextPage = (
        getOffset: (data: TData[] | undefined) => TOffset | null | undefined,
      ) => {
        const offset = getOffset(data);
        if (offset == null) return;

        setCursors((prev) => [...prev, offset]);
      };

      return { hasMore, empty, nextPage };
    };
  }, [cursors.length]);

  const reset = () => setCursors([firstOffset]);

  return { cursors, prevPages, useHelpers, reset };
};
