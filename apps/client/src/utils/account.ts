import * as account from '@/queries/account';
import * as auth from '@/queries/auth';
import { Modal } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'umi';
import { $produce } from './immer';
import { createAtom } from './zustand';

export const useTtdUserNo = () => {
  const query = account.userNo();

  return query.data;
};

export const usePasswordResetCheck = () => {
  const navigate = useNavigate();
  const [shown, setShown] = useState(false);

  const { data: user } = account.user();

  useEffect(() => {
    if (shown) return;
    if (!user?.passwordReset) return;

    Modal.show({
      bodyStyle: {
        borderRadius: '32px',
        padding: '1.5rem 1.5rem 1rem 1.5rem',
      },
      closeOnAction: true,
      actions: [
        {
          key: 'confirm',
          text: '前往修改',
          primary: true,
          style: { borderRadius: '9999px' },
          onClick: () => navigate('/secondary/password'),
        },
        {
          key: 'cancel',
          text: '暂时不要',
        },
      ],
      title: '为了您的账户安全，请尽快修改密码',
    });

    setShown(true);
  }, [user]);
};

type Popup = {
  key: string;
  action: () => unknown;
  status: 'stale' | 'active' | 'done';
  allowPath: (path: string) => boolean;
  priority: number;
};

const createPopupQueueAtom = () => {
  const atom = createAtom<Record<string, Popup>>({});

  const useActive = () => {
    return atom.use((state) => {
      const list = Object.values(state);
      return list.find((it) => it.status === 'active');
    });
  };

  const useAllowPath = (path: string) => {
    return atom.use((state) => {
      const list = Object.values(state);
      const remainingAmount = list.filter((it) => it.status !== 'done').length;
      const active = list.find((it) => it.status === 'active');

      return !remainingAmount || !!active?.allowPath(path);
    });
  };

  const set = (value: Omit<Popup, 'status'>) => {
    const queue = popupQueueAtom.store.getState();
    if (!!queue[value.key]) return;

    popupQueueAtom.store.setState(
      $produce((draft) => {
        draft[value.key] = { ...value, status: 'stale' };
      }),
    );
  };

  const run = () => {
    const queue = popupQueueAtom.store.getState();
    const list = Object.values(queue);
    if (list.some((it) => it.status === 'active')) return;

    const items = list.filter((it) => it.status === 'stale');
    const maxPriority = Math.max(...items.map((it) => it.priority));
    const item = items.find((it) => it.priority === maxPriority);
    if (!item) return;

    popupQueueAtom.store.setState(
      $produce((draft) => {
        draft[item.key].status = 'active';
      }),
    );

    item.action();
  };

  const refresh = () => {
    const queue = popupQueueAtom.store.getState();
    const list = Object.values(queue);
    const activeKeys = list
      .filter((it) => it.status === 'active')
      .map((it) => it.key);

    if (!!activeKeys.length) {
      popupQueueAtom.store.setState(
        $produce((draft) => {
          for (const key of activeKeys) {
            draft[key].status = 'stale';
          }
        }),
      );
    }

    run();
  };

  const complete = (key: string) => {
    const queue = popupQueueAtom.store.getState();
    const item = queue[key];
    if (!!item) {
      popupQueueAtom.store.setState(
        $produce((draft) => {
          draft[key].status = 'done';
        }),
      );
    }

    setTimeout(() => {
      run();
    }, 1000);
  };

  return {
    ...atom,
    useActive,
    useAllowPath,
    set,
    refresh,
    run,
    complete,
  };
};

export const popupQueueAtom = createPopupQueueAtom();

export const usePopupsUponLogin = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const active = popupQueueAtom.useActive();

  const userQuery = account.user();
  const user = userQuery.data;

  const logout = auth.logout();

  useEffect(() => {
    if (!active) return;
    if (active.allowPath(pathname)) return;

    popupQueueAtom.refresh();
  }, [pathname]);

  useEffect(() => {
    if (!user) return;

    if (user.validateIdentityUponLogin) {
      const key = 'validateIdentity';
      const priority = 2;

      const path = '/secondary/identity-validation';
      const allowPath = (pathname: string) => pathname.startsWith(path);

      const action = () => {
        if (allowPath(location.pathname)) return;

        Modal.show({
          bodyStyle: {
            borderRadius: '32px',
            padding: '1.5rem 1.5rem 1rem 1.5rem',
          },
          closeOnAction: true,
          actions: [
            {
              key: 'confirm',
              text: '前往',
              primary: true,
              style: { borderRadius: '9999px' },
              onClick: () => navigate(path),
            },
            {
              key: 'cancel',
              text: '登录其他账号',
              onClick: () => logout.trigger(),
            },
          ],
          title: '首次登录，请进行实名认证',
        });
      };

      popupQueueAtom.set({ key, action, priority, allowPath });
    }

    if (user.passwordReset) {
      const key = 'resetPassword';
      const priority = 1;

      const path = '/secondary/password';
      const allowPath = (pathname: string) => pathname === path;

      const action = () => {
        if (allowPath(location.pathname)) return;

        const handler = Modal.show({
          bodyStyle: {
            borderRadius: '32px',
            padding: '1.5rem 1.5rem 1rem 1.5rem',
          },
          actions: [
            {
              key: 'confirm',
              text: '前往修改',
              primary: true,
              style: { borderRadius: '9999px' },
              onClick: () => {
                navigate(path);
                handler.close();
              },
            },
            {
              key: 'cancel',
              text: '暂时不要',
              onClick: () => {
                popupQueueAtom.complete(key);
                handler.close();
              },
            },
          ],
          title: '为了您的账户安全，请尽快修改密码',
        });
      };

      popupQueueAtom.set({ key, action, priority, allowPath });
    }

    popupQueueAtom.run();
  }, [user]);
};
