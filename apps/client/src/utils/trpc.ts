import {
  TRPCClientError,
  createTRPCProxyClient,
  httpBatchLink,
  httpLink,
  splitLink,
} from '@trpc/client';
import type { AppRouter } from 'server/typings';

const url = '/trpc' as const;

export const trpc = createTRPCProxyClient<AppRouter>({
  links: [
    splitLink({
      condition: ({ context }) => !!context.skipBatch,
      true: httpLink({ url }),
      false: httpBatchLink({ url }),
    }),
  ],
});

export const isTRPCClientError = (
  error: unknown,
): error is TRPCClientError<AppRouter> => {
  return error instanceof TRPCClientError;
};
