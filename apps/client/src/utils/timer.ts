import { useCallback, useRef, useState } from 'react';

export const useTimer = () => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const [countDown, setCountDown] = useState(0);

  const status: 'active' | 'stopped' = countDown ? 'active' : 'stopped';

  const start = useCallback((time: number = 0) => {
    clearInterval(timeoutRef.current);

    setCountDown(time);
    timeoutRef.current = setInterval(() => {
      setCountDown((prev) => {
        if (prev > 0) {
          return prev - 1;
        }

        clearTimeout(timeoutRef.current);
        return 0;
      });
    }, 1000);
  }, []);

  const stop = useCallback(() => {
    clearInterval(timeoutRef.current);
    setCountDown(0);
  }, []);

  const setTime = (time: number) => {
    setCountDown(time);
  };

  return { countDown, status, start, stop, setTime };
};
