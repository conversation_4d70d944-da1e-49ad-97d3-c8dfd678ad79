import { Space, Toast } from 'antd-mobile';
import { CheckOutline, CloseOutline } from 'antd-mobile-icons';

export const toastSuccess = (content: React.ReactNode) => {
  Toast.show({
    icon: (
      <Space block justify="center">
        <CheckOutline />
      </Space>
    ),
    content,
  });
};

export const toastError = (content: React.ReactNode) => {
  Toast.show({
    icon: (
      <Space block justify="center">
        <CloseOutline />
      </Space>
    ),
    content,
  });
};
