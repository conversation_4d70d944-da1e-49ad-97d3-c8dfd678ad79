export const getAgeFromIdentityNumber = (
  identityNumber: string | null | undefined,
): number | undefined => {
  if (!identityNumber) return;
  if (!/^\d{17}(\d|X)$/i.test(identityNumber)) return;

  const birthDateString = identityNumber.slice(6, 14);
  const birthYear = parseInt(birthDateString.slice(0, 4), 10);
  const birthMonth = parseInt(birthDateString.slice(4, 6), 10) - 1;
  const birthDay = parseInt(birthDateString.slice(6, 8), 10);

  const today = new Date();

  let age = today.getFullYear() - birthYear;
  if (
    today.getMonth() < birthMonth ||
    (today.getMonth() === birthMonth && today.getDate() < birthDay)
  ) {
    age--;
  }

  return age;
};

export const getBirthDayFromIdentityNumber = (
  identityNumber: string | null | undefined,
): string | undefined => {
  if (!identityNumber) return;
  if (!/^\d{17}(\d|X)$/i.test(identityNumber)) return;

  const birthDateString = identityNumber.slice(6, 14);
  const birthYear = birthDateString.slice(0, 4);
  const birthMonth = birthDateString.slice(4, 6);
  const birthDay = birthDateString.slice(6, 8);

  return `${birthYear}-${birthMonth}-${birthDay}`;
};
