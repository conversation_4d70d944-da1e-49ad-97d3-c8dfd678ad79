import { trpc } from '@/utils/trpc';
import useSWR from 'swr';

const queryKey = 'aIndexFutures' as const;

// export const getLastDateData = () => {
//   return useSWR([queryKey], async () => {
//     return await trpc.aIndexFutures.getLastDayData.query();
//   });
// };

export const getICDayData = (tradeDate: string) => {
  return useSWR(['aIndexICFutures', tradeDate], async () => {
    return await trpc.aIndexFutures.getICDayData.query(tradeDate);
  });
};

export const getIHDayData = (tradeDate: string) => {
  return useSWR(['aIndexIHFutures', tradeDate], async () => {
    return await trpc.aIndexFutures.getIHDayData.query(tradeDate);
  });
};

export const getIFDayData = (tradeDate: string) => {
  return useSWR(['aIndexIFFutures', tradeDate], async () => {
    return await trpc.aIndexFutures.getIFDayData.query(tradeDate);
  });
};

export const getIMDayData = (tradeDate: string) => {
  return useSWR(['aIndexIMFutures', tradeDate], async () => {
    return await trpc.aIndexFutures.getIMDayData.query(tradeDate);
  });
};

export const getICAnnualizedBasisTrend = (tradeDate: string) => {
  return useSWR(['aIndexICAnnualizedBasisTrend', tradeDate], async () => {
    return await trpc.aIndexFutures.getICAnnualizedBasisTrend.query(tradeDate);
  });
};

export const getIHAnnualizedBasisTrend = (tradeDate: string) => {
  return useSWR(['aIndexIHAnnualizedBasisTrend', tradeDate], async () => {
    return await trpc.aIndexFutures.getIHAnnualizedBasisTrend.query(tradeDate);
  });
};

export const getIFAnnualizedBasisTrend = (tradeDate: string) => {
  return useSWR(['aIndexIFAnnualizedBasisTrend', tradeDate], async () => {
    return await trpc.aIndexFutures.getIFAnnualizedBasisTrend.query(tradeDate);
  });
};

export const getIMAnnualizedBasisTrend = (tradeDate: string) => {
  return useSWR(['aIndexIMAnnualizedBasisTrend', tradeDate], async () => {
    return await trpc.aIndexFutures.getIMAnnualizedBasisTrend.query(tradeDate);
  });
};

export const getIC3YearsData = (tradeDate: string) => {
  return useSWR(['aIndexIC3YearsData', tradeDate], async () => {
    return await trpc.aIndexFutures.getIC3YearsData.query(tradeDate);
  });
};

export const getIH3YearsData = (tradeDate: string) => {
  return useSWR(['aIndexIH3YearsData', tradeDate], async () => {
    return await trpc.aIndexFutures.getIH3YearsData.query(tradeDate);
  });
};

export const getIF3YearsData = (tradeDate: string) => {
  return useSWR(['aIndexIF3YearsData', tradeDate], async () => {
    return await trpc.aIndexFutures.getIF3YearsData.query(tradeDate);
  });
};

export const getIM3YearsData = (tradeDate: string) => {
  return useSWR(['aIndexIM3YearsData', tradeDate], async () => {
    return await trpc.aIndexFutures.getIM3YearsData.query(tradeDate);
  });
};

export const getICWeekBasisAverage = (tradeDate: string) => {
  return useSWR(['getICWeekBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getICWeekBasisAverage.query(tradeDate);
  });
};

export const getICMonthBasisAverage = (tradeDate: string) => {
  return useSWR(['getICMonthBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getICMonthBasisAverage.query(tradeDate);
  });
};

export const getICYearBasisAverage = (tradeDate: string) => {
  return useSWR(['getICYearBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getICYearBasisAverage.query(tradeDate);
  });
};

export const getIHWeekBasisAverage = (tradeDate: string) => {
  return useSWR(['getIHWeekBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIHWeekBasisAverage.query(tradeDate);
  });
};

export const getIHMonthBasisAverage = (tradeDate: string) => {
  return useSWR(['getIHMonthBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIHMonthBasisAverage.query(tradeDate);
  });
};

export const getIHYearBasisAverage = (tradeDate: string) => {
  return useSWR(['getIHYearBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIHYearBasisAverage.query(tradeDate);
  });
};

export const getIFWeekBasisAverage = (tradeDate: string) => {
  return useSWR(['getIFWeekBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIFWeekBasisAverage.query(tradeDate);
  });
};

export const getIFMonthBasisAverage = (tradeDate: string) => {
  return useSWR(['getIFMonthBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIFMonthBasisAverage.query(tradeDate);
  });
};

export const getIFYearBasisAverage = (tradeDate: string) => {
  return useSWR(['getIFYearBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIFYearBasisAverage.query(tradeDate);
  });
};

export const getIMWeekBasisAverage = (tradeDate: string) => {
  return useSWR(['getIMWeekBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIMWeekBasisAverage.query(tradeDate);
  });
};

export const getIMMonthBasisAverage = (tradeDate: string) => {
  return useSWR(['getIMMonthBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIMMonthBasisAverage.query(tradeDate);
  });
};

export const getIMYearBasisAverage = (tradeDate: string) => {
  return useSWR(['getIMYearBasisAverage', tradeDate], async () => {
    return await trpc.aIndexFutures.getIMYearBasisAverage.query(tradeDate);
  });
};
