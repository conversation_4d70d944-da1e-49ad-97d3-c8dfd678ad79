import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import { z } from 'zod';

const queryKey = 'netValue' as const;

export type InfoParams = RouterInput['netValue']['infoByPortfolioId'];
export type ListParams = RouterInput['netValue']['listByPortfolioId'];

export const infoByPortfolioId = (params: InfoParams | undefined) => {
  let key: [typeof queryKey, InfoParams] | null = null;

  const idParsed = z.number().safeParse(params);
  if (idParsed.success) {
    key = [queryKey, idParsed.data];
  }

  return useSWR(key, async ([_, params]) => {
    return await trpc.netValue.infoByPortfolioId.query(params);
  });
};

export const listByPortfolioId = (params: Partial<ListParams>) => {
  let key: [typeof queryKey, ListParams] | null = null;

  const idParsed = z.number().safeParse(params.portfolioId);
  if (idParsed.success) {
    key = [queryKey, { ...params, portfolioId: idParsed.data }];
  }

  return useSWR(key, async ([_, params]) => {
    const [listResponse, assetNetListResponse] = await Promise.all([
      trpc.netValue.listByPortfolioId.query(params),
      trpc.netValue.assetNetListByPortfolioId.query(params),
    ]);

    const itemsWithAssetNet: ((typeof listResponse)[number] & {
      assetNet?: number | null;
    })[] = listResponse.map((item) => {
      const { date } = item;
      if (!date) return item;

      const assetNetItem = assetNetListResponse.find((it) => date === it.date);

      return { ...item, assetNet: assetNetItem?.assetNet };
    });

    return itemsWithAssetNet;
  });
};
