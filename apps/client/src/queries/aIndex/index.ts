import { trpc } from '@/utils/trpc';
import useSWR from 'swr';

const queryKey = 'aIndex' as const;

export const getLastDateData = () => {
    return useSWR([queryKey], async () => {
        return await trpc.aIndex.getLastDayData.query()
      });
};


export const getTradeDateData = (tradeDate:string) => {
    return useSWR([queryKey,tradeDate], async () => {
        return await trpc.aIndex.getTradeDayData.query(tradeDate)
      });
};
