import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { z } from 'zod';
const queryKey = 'portfolio' as const;

type StartDateType = {
  type: string;
  date?: string;
};

type calcProps = {
  selectedProductId: number;
  startDate: StartDateType;
  endDate: string;
  intervalType: 'day' | 'week';
  valueType: 'accumulate' | 'compound' | 'unit';
  indexType:string
};

export type ListParams = RouterInput['portfolio']['list'];

export const list = (params: ListParams) => {
  return useSWR([queryKey, params], async ([_, params]) => {
    return await trpc.portfolio.list.query(params);
  });
};

export const byId = (params: { id?: number }) => {
  let key: [typeof queryKey, number] | null = null;

  const idParsed = z.number().safeParse(params.id);
  if (idParsed.success) {
    key = [queryKey, idParsed.data];
  }

  return useSWR(key, async ([_, id]) => {
    return await trpc.portfolio.byId.query(id);
  });
};

export const byIdNoAccess = (params: { id?: number }) => {
  let key: [typeof queryKey, number] | null = null;

  const idParsed = z.number().safeParse(params.id);
  if (idParsed.success) {
    key = [queryKey, idParsed.data];
  }

  return useSWR(key, async ([_, id]) => {
    return await trpc.portfolio.byIdNoAccess.query(id);
  });
};

export const search = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: calcProps }) => {
      return trpc.portfolio.calData.mutate(arg);
    },
    // {
    //   onSuccess: () => {
    //     toastSuccess('提交成功');
    //   },
    // },
  );
};

export const getDataById = (id: number) => {
  const key = id ? [queryKey, id] : null; 
  return useSWR(key, async ([_, validId]) => {
    return await trpc.portfolio.byIdNoAccess.query(validId as number);
  });
};
