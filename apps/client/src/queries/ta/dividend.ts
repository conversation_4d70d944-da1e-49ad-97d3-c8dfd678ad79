import { toastSuccess } from '@/utils/antd';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'dividend' as const;

export type ListParams = RouterInput['dividend']['list'];

export const list = (params: ListParams) => {
  return useSWR([queryKey, params], async () => {
    return await trpc.dividend.list.query(params);
  });
};

type QueryParams = {
  reinvestmentDistributionDate: string | null;
  agency: string | null;
  portfolioName: string | null;
};

export const searchByDividendCalculator = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: QueryParams }) => {
      return trpc.dividend.byDividendCalculator.mutate(arg)
    },
    {
      onSuccess: () => {
        toastSuccess('提交成功');
      },
    },
  );
};
