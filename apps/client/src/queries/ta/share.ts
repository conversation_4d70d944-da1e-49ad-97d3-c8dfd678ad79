import { toastSuccess } from '@/utils/antd';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'share' as const;

export type ListParams = RouterInput['share']['list'];

export type UpdateShares = {
  shareId: number;
  portfolioName: string;
  agency: string;
  region: string | null;
  department: string | null;
  channelPerson: string;
};
export const list = (params: ListParams) => {
  return useSWR([queryKey, params], async () => {
    return await trpc.share.list.query(params);
  });
};

export const userAgentsShareList = () => {
  return useSWR([queryKey, 'getAgentsTradesByIdentityNumber'], async () => {
    return await trpc.share.getAgencySharesByIdentityNumber.query();
  });
};

export const userShareList = () => {
  return useSWR([queryKey, 'getTradesByIdentityNumber'], async () => {
    return await trpc.share.getSharesByIdentityNumber.query();
  });
};

export const userShareListAddOpenDay = () => {
  return useSWR([queryKey, 'getTradesByIdentityNumberAddOpenDay'], async () => {
    return await trpc.share.getSharesByIdentityNumberAddOpenDay.query();
  });
};

export const downLoadDividendCalculator = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: string }) => {
      return trpc.share.downLoadDividendCalculator.mutate(arg);
    },
    {
      onSuccess: () => {
        toastSuccess('提交成功');
      },
    },
  );
};

export const getDividedDataByProductName = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: string }) => {
      return trpc.share.getDividedDataByProductName.mutate(arg);
    },
    // {
    //   onSuccess: () => {
    //     toastSuccess('提交成功');
    //   },
    // },
  );
};

// export const updateShareList = (params:UpdateShares) => {
//   return useSWR([queryKey,params], async () => {
//     return await trpc.share.updateShareList.mutate(params);
//   });
// };

export const updateShareList = async (params: UpdateShares) => {
  return await trpc.share.updateShareList.mutate(params);
};

export const checkDividendRecordExistence = (productName: string) => {
  return useSWR([queryKey, productName], 
    () => trpc.share.checkDividendRecordExistence.query(productName)
  );
};
