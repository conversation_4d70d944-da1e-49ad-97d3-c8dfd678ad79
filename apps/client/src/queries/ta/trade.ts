import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { toastSuccess } from '@/utils/antd';

const queryKey = 'trade' as const;

export type ListParams = RouterInput['trade']['list'];

export const list = (params: ListParams) => {
  return useSWR([queryKey, params], async () => {
    return await trpc.trade.list.query(params);
  });
};

export const tradeDate = () => {
  return useSWR('tradeDate', async () => {
    return await trpc.trade.getTradeDays.query();
  });
};

export const userTradeData = () => {
   return useSWR([queryKey, 'getTradesByIdentityNumber'], async () => {
    return await trpc.trade.getTradesByIdentityNumber.query();
  });
}

export const getDividedDataByProductName = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: string }) => {
      return trpc.trade.getApplyDatesByProduct.mutate(arg);
    },
    // {
    //   onSuccess: () => {
    //     toastSuccess('提交成功');
    //   },
    // },
  );
};

type QueryParams = {
  productName: string | null;
  applyDate: string | null;
};

export const search = () => {
  return useSWRMutation(
    queryKey,
    async (_, { arg }: { arg: QueryParams }) => {
      return trpc.trade.search.mutate(arg);
    },
    {
      onSuccess: () => {
        toastSuccess('提交成功');
      },
    },
  );
};
