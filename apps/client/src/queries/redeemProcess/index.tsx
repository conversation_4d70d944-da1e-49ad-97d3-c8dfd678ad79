import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';

export const queryKey = 'redeemProcess' as const;

export type ListParams = RouterInput['redeemProcess']['ownList'];

export const queryKeyPredicate = (value: unknown) => {
  if (Array.isArray(value)) {
    return value[0] === queryKey;
  }

  return value === queryKey;
};

export const list = (params: ListParams) => {
  return useSWR(
    [queryKey, params],
    async () => {
      return await trpc.redeemProcess.ownList.query(params);
    },
    { refreshInterval: 5000 },
  );
};

export const activeAmount = () => {
  return useSWR(
    'redeemProcessActiveAmount',
    async () => {
      const response = await trpc.redeemProcess.activeAmount.query();
      return response.amount;
    },
    { refreshInterval: 5000 },
  );
};
