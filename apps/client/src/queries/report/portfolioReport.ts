import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import { z } from 'zod';

const queryKey = 'portfolioReport' as const;

export type ListParmas =
  RouterInput['portfolioReportReadonly']['listByPortfolioId'];

export const listByPortfolioId = (params: Partial<ListParmas>) => {
  let key: [typeof queryKey, ListParmas] | null = null;
  const idParsed = z.number().safeParse(params.portfolioId);
  if (idParsed.success) {
    key = [queryKey, { ...params, portfolioId: idParsed.data }];
  }

  return useSWR(key, async ([_, params]) => {
    return trpc.portfolioReportReadonly.listByPortfolioId.query(params);
  });
};
