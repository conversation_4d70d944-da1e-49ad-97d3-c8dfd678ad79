import { toastSuccess } from '@/utils/antd';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const queryKey = 'baseInfo' as const;

export const own = () => {
  return useSWR(queryKey, () => trpc.baseInfo.own.query());
};

type UpdateInput = RouterInput['baseInfo']['update'];

export const update = () => {
  return useSWRMutation(
    queryKey,
    (_, { arg }: { arg: UpdateInput }) => trpc.baseInfo.update.mutate(arg),
    {
      onSuccess: () => {
        toastSuccess('提交成功');
      },
    },
  );
};
