import { toastSuccess } from '@/utils/antd';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const queryKey = 'taxDeclaration' as const;

export const own = () => {
  return useSWR(queryKey, () => trpc.taxDeclaration.own.query());
};

type UpdateInput = RouterInput['taxDeclaration']['update'];

export const update = () => {
  return useSWRMutation(
    queryKey,
    (_, { arg }: { arg: UpdateInput }) =>
      trpc.taxDeclaration.update.mutate(arg),
    {
      onSuccess: () => {
        toastSuccess('提交成功');
      },
    },
  );
};
