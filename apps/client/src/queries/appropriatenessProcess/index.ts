import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';
import { z } from 'zod';

export const queryKey = 'appropriatenessProcess' as const;

export const queryKeyPredicate = (value: unknown) => {
  if (Array.isArray(value)) {
    return value[0] === queryKey;
  }

  return value === queryKey;
};

export const own = () => {
  return useSWR(queryKey, () => trpc.appropriatenessProcess.own.query(), {
    refreshInterval: 5000,
  });
};

export const documentsById = (id: number | undefined) => {
  let key: [string, number] | null = null;
  const idParsed = z.number().safeParse(id);
  if (idParsed.success) {
    key = ['appropriatenessProcessDocuments', idParsed.data];
  }

  return useSWR(key, ([_, id]) =>
    trpc.appropriatenessProcess.documentsById.query({ id }),
  );
};

export const create = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    () => trpc.appropriatenessProcess.userCreate.mutate(),
    {
      onSuccess: () => {
        mutate(queryKey);
        toastSuccess('提交成功');
      },
    },
  );
};

export type ClassificationParams =
  RouterInput['appropriatenessProcess']['classification'];

export const classification = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: ClassificationParams }) =>
      trpc.appropriatenessProcess.classification.mutate(arg),
    { onSuccess: () => mutate(queryKey) },
  );
};

type RiskTestParams = RouterInput['appropriatenessProcess']['riskTest'];

export const riskTest = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: RiskTestParams }) =>
      trpc.appropriatenessProcess.riskTest.mutate(arg),
    { onSuccess: () => mutate(queryKey) },
  );
};
