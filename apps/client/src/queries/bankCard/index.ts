import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'bankCard' as const;

export const list = () => {
  return useSWR(queryKey, () => trpc.bankCard.list.query());
};

type CreateParams = RouterInput['bankCard']['create'];

export const create = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: CreateParams }) => trpc.bankCard.create.mutate(arg),
    {
      onSuccess: () => {
        toastSuccess('添加成功');
        mutate(queryKey);
      },
    },
  );
};
