import { voidKey } from '@/utils/swr';
import { fileTypeFromBlob } from 'file-type';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const uploadRequset = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  const response = await fetch('/api/fileUpload', {
    method: 'post',
    body: formData,
  });
  const { id } = await response.json();
  return id;
};

export const upload = () => {
  return useSWRMutation(voidKey, (_, { arg }: { arg: { file: File } }) =>
    uploadRequset(arg.file),
  );
};

export const byId = (id: string | null | undefined) => {
  const key: string[] | null = id ? ['fileById', id] : null;

  return useSWR(key, async ([_, id]) => {
    const response = await fetch(`/api/static?id=${id}`);
    const blob = await response.blob();
    const { ext } = (await fileTypeFromBlob(blob)) || {};

    return { blob, ext };
  });
};
