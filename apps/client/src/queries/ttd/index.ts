import { trpc } from '@/utils/trpc';
import useSWR from 'swr';
import { z } from 'zod';

const queryKey = 'ttd' as const;

export const queryKeyPredicate = (value: unknown) => {
  if (Array.isArray(value)) {
    return value[0] === queryKey;
  }

  return value === queryKey;
};

export const orderState = (orderNumber?: string | null) => {
  let key: [string, string] | null = null;
  const orderNumberParsed = z.string().safeParse(orderNumber);
  if (orderNumberParsed.success) {
    key = [queryKey + 'OrderState', orderNumberParsed.data];
  }

  return useSWR(key, async ([_, orderNumber]) => {
    return await trpc.ttd.orderState.query({ orderNumber });
  });
};
