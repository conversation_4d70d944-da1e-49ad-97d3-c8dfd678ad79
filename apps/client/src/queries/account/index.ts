import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'account' as const;

export const user = () => {
  return useSWR(queryKey, () => trpc.account.user.query(), {
    onError: () => {},
  });
};

export const userNo = () => {
  return useSWR(queryKey + 'userNo', () => trpc.account.userNo.query());
};

export const createUserTag = ()=>{
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: RouterInput['account']['createUserTag'] }) => {
      return await trpc.account.createUserTag.mutate(arg);
    },
    // { onSuccess: () => toastSuccess('标签创建成功') },
  );
}

export const updatePassword = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: RouterInput['account']['updatePassword'] }) => {
      return await trpc.account.updatePassword.mutate(arg);
    },
    { onSuccess: () => toastSuccess('提交成功') },
  );
};

export const userQueryKey = queryKey;
