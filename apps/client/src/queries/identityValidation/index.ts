import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'identityValidation' as const;

export const status = () => {
  return useSWR(queryKey, () => trpc.identityVerification.status.query());
};

type FaceRecognitionUrlParams =
  RouterInput['identityVerification']['faceRecognitionUrl'];

export const faceRecognitionUrl = (
  params: Partial<FaceRecognitionUrlParams>,
) => {
  const { redirectUrl } = params;
  let key: [string, FaceRecognitionUrlParams] | null = null;

  if (!!redirectUrl) {
    key = [queryKey + 'FaceRecognitionUrl', { ...params, redirectUrl }];
  }

  return useSWR(key, async ([_, params]) =>
    trpc.identityVerification.faceRecognitionUrl.query(params),
  );
};

type QuartletParams = RouterInput['identityVerification']['quartlet'];

export const quartlet = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: QuartletParams }) =>
      trpc.identityVerification.quartlet.mutate(arg),
    { onSuccess: () => mutate(queryKey) },
  );
};
