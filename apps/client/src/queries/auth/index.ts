import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import type { RouterInput } from 'server/typings';
import useSWRMutation from 'swr/mutation';

type SendCodeInput = RouterInput['auth']['sendCode'];

export const sendCode = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { data: SendCodeInput } }) => {
      await trpc.auth.sendCode.mutate(arg.data);
    },
  );
};

export const login = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: RouterInput['auth']['login'] }) => {
      return await trpc.auth.login.mutate(arg);
    },
    { onSuccess: () => (location.href = location.origin) },
  );
};

export const logout = () => {
  return useSWRMutation(voidKey, () => trpc.auth.logout.query(), {
    onSuccess: () => (location.href = location.origin),
  });
};
