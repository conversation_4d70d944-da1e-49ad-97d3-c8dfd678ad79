import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const genericCodeQueryKey = 'genericCodeVerificationStatus';

export const genericCodeVerificationStatus = () => {
  return useSWR(genericCodeQueryKey, async () => {
    const response = await trpc.user.genericCodeVerificationStatus.query();
    return response;
  });
};

type SendCodeParams = RouterInput['user']['sendCode'];

export const sendCode = () => {
  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: SendCodeParams }) => trpc.user.sendCode.mutate(arg),
    { onSuccess: () => toastSuccess('发送成功') },
  );
};

type VerifyCodeInput = RouterInput['user']['verifyCode'];

export const verifyCode = () => {
  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: VerifyCodeInput }) => trpc.user.verifyCode.mutate(arg),
    { onSuccess: () => toastSuccess('提交成功') },
  );
};

type UpdateLoginSendCodeInput = RouterInput['user']['updateLoginSendCode'];

export const updateLoginSendCode = () => {
  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: UpdateLoginSendCodeInput }) =>
      trpc.user.updateLoginSendCode.mutate(arg),
    { onSuccess: () => toastSuccess('发送成功') },
  );
};

type UpdateLoginInput = RouterInput['user']['updateLogin'];

export const updateLogin = () => {
  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: UpdateLoginInput }) =>
      trpc.user.updateLogin.mutate(arg),
    { onSuccess: () => toastSuccess('提交成功') },
  );
};

export const getUserTag = () => {
  return useSWR(['userTag'], async () => {
    return await trpc.user.getUserTags.query();
  });
};
