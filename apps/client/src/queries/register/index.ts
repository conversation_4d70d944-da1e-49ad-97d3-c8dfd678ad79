import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const user = () => {
  return useSWR('registerUser', () => trpc.register.user.query());
};

type IdentityValidateInput = RouterInput['register']['identityValidate'];

export const identityValidate = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { data: IdentityValidateInput } }) => {
      await trpc.register.identityValidate.mutate(arg.data);
    },
  );
};

type CodeValidateInput = RouterInput['register']['codeValidate'];

export const codeValidate = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { data: CodeValidateInput } }) => {
      await trpc.register.codeValidate.mutate(arg.data);
    },
  );
};

type RegisterInput = RouterInput['register']['register'];

export const register = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { data: RegisterInput } }) => {
      await trpc.register.register.mutate(arg.data);
    },
    { onSuccess: () => (location.href = location.origin) },
  );
};

type SendCodeInput = RouterInput['register']['sendCode'];

export const sendCode = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { data: SendCodeInput } }) => {
      await trpc.register.sendCode.mutate(arg.data);
    },
  );
};
