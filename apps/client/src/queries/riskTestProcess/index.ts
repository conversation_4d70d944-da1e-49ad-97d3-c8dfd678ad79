import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';
import { z } from 'zod';

export const queryKey = 'riskTestProcess' as const;

export const queryKeyPredicate = (value: unknown) => {
  if (Array.isArray(value)) {
    return value[0] === queryKey;
  }

  return value === queryKey;
};

export const own = () => {
  return useSWR(queryKey, () => trpc.riskTestProcess.own.query(), {
    refreshInterval: 5000,
  });
};

export const documentsById = (id: number | undefined) => {
  let key: [string, number] | null = null;
  const idParsed = z.number().safeParse(id);
  if (idParsed.success) {
    key = ['riskTestProcessDocuments', idParsed.data];
  }

  return useSWR(key, ([_, id]) =>
    trpc.riskTestProcess.documentsById.query({ id }),
  );
};

type CreateParams = RouterInput['riskTestProcess']['create'];

export const create = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: CreateParams }) =>
      trpc.riskTestProcess.create.mutate(arg),
    {
      onSuccess: () => {
        mutate(queryKey);
        toastSuccess('提交成功，接下来请签署风险测评问卷');
      },
    },
  );
};


type RiskTestParams = RouterInput['riskTestProcess']['riskTest'];

export const riskTest = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: RiskTestParams }) =>
      trpc.riskTestProcess.riskTest.mutate(arg),
    {
      onSuccess: () => {
        mutate(queryKey);
        toastSuccess('提交成功，接下来请签署风险测评问卷');
      },
    },
  );
};
