import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';

const queryKey = 'signPassword' as const;

export const exists = () => {
  return useSWR(queryKey, () => trpc.signPassword.exists.query());
};

type CreateParams = RouterInput['signPassword']['create'];

export const create = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    (_, { arg }: { arg: CreateParams }) => trpc.signPassword.create.mutate(arg),
    {
      onSuccess: () => {
        mutate(queryKey);
        toastSuccess('提交成功');
      },
    },
  );
};

export const setSignPasswordQuerykey = queryKey;
