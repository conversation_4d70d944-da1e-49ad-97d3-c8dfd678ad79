import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const validationPassed = () => {
  return useSWR('forgetPasswordStatus', () =>
    trpc.auth.forgetPasswordStatus.query(),
  );
};

type ForgetPasswordInput = RouterInput['auth']['forgetPassword'];

export const forgetPassword = () => {
  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: ForgetPasswordInput }) => {
      await trpc.auth.forgetPassword.mutate(arg);
    },
    { onSuccess: () => toastSuccess('提交成功') },
  );
};
