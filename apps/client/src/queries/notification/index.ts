import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { RouterInput } from 'server/typings';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';

export const queryKey = 'notification' as const;
export const queryKeyPredicate = (value: unknown) => {
  if (Array.isArray(value)) {
    return value[0] === queryKey;
  }

  return value === queryKey;
};

export type ListParams = RouterInput['notification']['ownList'];

export const list = (params: ListParams) => {
  return useSWR(
    [queryKey, params],
    async ([_, params]) => {
      return await trpc.notification.ownList.query(params);
    },
    { refreshInterval: 5000 },
  );
};

type ReadParams = RouterInput['notification']['read'];

export const read = () => {
  const { mutate } = useSWRConfig();

  return useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: ReadParams }) => {
      await trpc.notification.read.mutate(arg);
    },
    {
      onSuccess: () => {
        toastSuccess('已标记为已读');
        mutate(queryKeyPredicate);
      },
    },
  );
};

type UnreadAmountParams = RouterInput['notification']['unreadAmount'];

export const unreadAmount = (params: UnreadAmountParams) => {
  return useSWR(
    [queryKey, 'unreadAmount', params],
    async ([_, __, params]) => {
      return await trpc.notification.unreadAmount.query(params);
    },
    { refreshInterval: 5000 },
  );
};
