import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';


const queryKey = 'userFollowedProducts' as const;
export const addProducts = () => {
    return useSWRMutation(
      queryKey,
      async (_, { arg }: { arg: string[] }) => {
        return trpc.userFollowedProducts.addProudcts.mutate(arg);
      },
      {
        onSuccess: () => {
          toastSuccess('提交成功');
        },
      },
    );
  };


export const getProducts = () =>{
    return useSWR([queryKey], async () => {
        return await trpc.userFollowedProducts.getList.query();
      });
}  