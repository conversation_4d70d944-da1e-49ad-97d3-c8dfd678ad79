import { ReactComponent as Logo } from '@/assets/logo.svg';
import Loading from '@/components/loading';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as bankCard from '@/queries/bankCard';
import Identity from '@/ui/main/account/identity';
import Profile from '@/ui/main/account/profile';
import { Space } from 'antd-mobile';
import React from 'react';

const Account = () => (
  <Safe>
    <div className="pt-12 pb-6">
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div className="mx-7">
          <SvgWrapper component={Logo} />
        </div>
        <div className="mx-6">
          <Profile />
        </div>
        <div className="mx-6">
          <Identity />
        </div>
      </Space>
    </div>
  </Safe>
);

export default () => {
  const userQuery = account.user();
  const bankCardQuery = bankCard.list();

  const loading = userQuery.isLoading || bankCardQuery.isLoading;

  if (loading) {
    return <Loading />;
  }

  return (
    <React.Fragment>
      <Account />
    </React.Fragment>
  );
};
