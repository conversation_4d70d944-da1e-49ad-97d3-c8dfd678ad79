import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import Asset from '@/ui/main/holding/asset';
import HoldingDetail from '@/ui/main/holding/holding-detail';
import SorterPicker from '@/ui/main/holding/sorter-picker';
import TradeDividend from '@/ui/main/holding/trade-dividend';
import { Space } from 'antd-mobile';
import BasisCalculator from '@/ui/main/calculator/basisCalculator';
import DividedCalculator from '@/ui/main/calculator/dividedCalculator';
import ProductCalculator from '@/ui/main/calculator/productCalculator';
import RedemptionCalculator from '@/ui/main/calculator/redemptionCalculator';
import TimeHeldCalculator from '@/ui/main/calculator/timeHeldCalculator';
import React from 'react';

const DividedCalculators = () => {
    return (
        <Safe>
            <>
                <DividedCalculator />
            </>
        </Safe>
    );
};

export default () => (
    <React.Fragment>
        <DividedCalculators />
    </React.Fragment>
);