import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import Asset from '@/ui/main/holding/asset';
import HoldingDetail from '@/ui/main/holding/holding-detail';
import SorterPicker from '@/ui/main/holding/sorter-picker';
import TradeDividend from '@/ui/main/holding/trade-dividend';
import { Space } from 'antd-mobile';
import React from 'react';

const Holding = () => {
  return (
    <Safe>
      <div className="pt-12 pb-6">
        <Space block direction="vertical" style={{ '--gap': '1.5rem' }}>
          <div className="mx-7">
            <SvgWrapper component={Logo} fill="white" />
          </div>
          <div className="mx-6">
            <Asset />
          </div>
          <div className="mx-6">
            <TradeDividend />
          </div>
          <div className="mx-6">
            <HoldingDetail />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Holding />
    <SorterPicker />
  </React.Fragment>
);
