import { ReactComponent as Logo } from '@/assets/logo.svg';
import Loading from '@/components/loading';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as bankCard from '@/queries/bankCard';
import Identity from '@/ui/main/account/identity';
import Profile from '@/ui/main/account/profile';
import NewPeriodicReport from '@/ui/main/newPeriodicReport';
import { Space } from 'antd-mobile';
import React from 'react';
import { useParams } from 'umi';

const NewPeriodicReport$Id = () => {
    const params = useParams();
    const id = params.id ? parseInt(params.id) : undefined;


    return (

        <Safe>
            <NewPeriodicReport />
        </Safe>
    )
};

export default () => {

    // const userQuery = account.user();
    // const bankCardQuery = bankCard.list();

    // const loading = userQuery.isLoading || bankCardQuery.isLoading;

    // if (loading) {
    //     return <Loading />;
    // }

    return (
        <React.Fragment>
            <NewPeriodicReport$Id />
        </React.Fragment>
    );
};
