import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import Asset from '@/ui/main/holding/asset';
import HoldingDetail from '@/ui/main/holding/holding-detail';
import SorterPicker from '@/ui/main/holding/sorter-picker';
import TradeDividend from '@/ui/main/holding/trade-dividend';
import { Button, Space } from 'antd-mobile';
import BasisCalculator from '@/ui/main/calculator/basisCalculator';
import DividedCalculator from '@/ui/main/calculator/dividedCalculator';
import ProductCalculator from '@/ui/main/calculator/productCalculator';
import RedemptionCalculator from '@/ui/main/calculator/redemptionCalculator';
import TimeHeldCalculator from '@/ui/main/calculator/timeHeldCalculator';
import { ReactComponent as CalcSVG } from '@/assets//分红计算器.svg';
import { ReactComponent as RedemptionSVG } from '@/assets/赎回计算器.svg';
import { ReactComponent as BasisSVG } from '@/assets/基差计算器.svg';
import { ReactComponent as TimeHeldSVG } from '@/assets/基金持有时间计算器.svg';
import { ReactComponent as ProductSVG } from '@/assets/产品计算器.svg';
import React from 'react';
import { useNavigate } from 'umi';

const containerStyle: React.CSSProperties = {
    width: "93%",
    minHeight: 'calc(100vh - 100px)',
    // minHeight: 692,
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    margin: '0 auto',
    position: 'relative',
    padding: 16,
    display: 'flex',
    flexDirection: 'column',
    gap: 12,
    boxShadow: "0px 4px 44px 5px rgba(9, 116, 179, 0.07)"

}


const cardButtonStyle: React.CSSProperties = {
    // backgroundColor:"red",
    position: 'relative',
    height: 90,
    borderRadius: 12,
    border: 'none',
    // background: '#fff',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 16px',
    marginBottom: 16
};

const textStyle = {
    fontSize: 16,
    // lineHeight: '20px',
    marginTop: 14,
    color: "#02588B",
    fontWeight: 600,
    fontFamily: "PingFang SC",
};

const ArrowIcon = () => (
    <div style={{
        width: 18,
        height: 18,
        borderRight: '2px solid #CCCCCC',
        borderTop: '2px solid #CCCCCC',
        transform: 'rotate(45deg)',
        marginLeft: 16
    }} />
);

const BottomLine = () => (
    <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 1,
        backgroundColor: '#CCCCCC'
    }} />
);

const Calculator = () => {
    const navigate = useNavigate();
    return (

        <div className="pt-12 pb-6">
            <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
                <div className="ml-7 mr-6 flex justify-between items-end">
                    <SvgWrapper component={Logo} fill="white" />
                </div>
                <div className="mx-3">
                    <div style={{ marginTop: "20px", width: "100%" }}>
                        <div style={containerStyle}>
                            {/* 分红计算器 - 已有样式 */}
                            <div style={{ ...cardButtonStyle, marginTop: 30 }} onClick={() => navigate('/main/dividedCalculator')}>
                                <div style={{ width: "80%", height: 81 }}>
                                    <SvgWrapper component={CalcSVG} style={{ width: 40, height: 40, color: "#02588B", fill: "#02588B", marginTop: -10 }} />
                                    <div style={{ ...textStyle }}>分红计算器</div>
                                    <span style={{ fontSize: 9, width: "60%", color: "#02588B", marginTop: 30 ,whiteSpace:"nowrap"}}>注:测试期间的结果仅供参考，不确保数据完全准确，特此提示注意。</span>
                                </div>

                                <ArrowIcon />
                                <BottomLine />
                            </div>

                            {/* 赎回计算器 */}
                            <div style={cardButtonStyle} onClick={() => navigate('/main/redemptionCalculator')}>
                                <div style={{ width: 83, height: 81 }}>
                                    <SvgWrapper component={RedemptionSVG} style={{ width: 40, height: 40, color: "#02588B", fill: "#02588B", marginTop: -10 }} />
                                    <div style={{ ...textStyle }}>赎回计算器</div>
                                </div>
                                <ArrowIcon />
                                <BottomLine />
                            </div>

                            {/* 基差计算器 */}
                            <div style={cardButtonStyle} onClick={() => navigate('/main/basisCalculator')}>
                                <div style={{ width: 83, height: 81 }}>
                                    <SvgWrapper component={BasisSVG} style={{ width: 40, height: 40, color: "#02588B", fill: "#02588B", marginTop: -10 }} />
                                    <div style={{ ...textStyle }}>基差计算器</div>
                                </div>
                                <ArrowIcon />
                                <BottomLine />
                            </div>

                            {/* 基金持有时间计算器 */}
                            <div style={cardButtonStyle} onClick={() => navigate('/main/timeHeldCalculator')}>
                                <div style={{ width: 83, height: 81 }}>
                                    <SvgWrapper component={TimeHeldSVG} style={{ width: 40, height: 40, color: "#02588B", fill: "#02588B", marginTop: -10 }} />
                                    <div style={{ ...textStyle, whiteSpace: 'nowrap' }}>基金持有时间计算器</div>
                                </div>
                                <ArrowIcon />
                                <BottomLine />
                            </div>

                            {/* 产品计算器 */}
                            <div style={cardButtonStyle} onClick={() => navigate('/main/productCalculator')}>
                                <div style={{ width: 83, height: 81 }}>
                                    <SvgWrapper component={ProductSVG} style={{ width: 40, height: 40, color: "#02588B", fill: "#02588B", marginTop: -10 }} />
                                    <div style={{ ...textStyle }}>产品计算器</div>
                                </div>
                                <ArrowIcon />
                                <BottomLine />
                            </div>
                        </div>
                    </div>
                </div>
            </Space>
        </div>
    );
};

export default () => (
    <React.Fragment>
        <Calculator />
    </React.Fragment>
);
