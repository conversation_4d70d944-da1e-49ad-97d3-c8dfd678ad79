import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as ta from '@/queries/ta';

import Loading from '@/components/loading';
import * as appropriateness from '@/queries/appropriatenessProcess';
import * as notification from '@/queries/notification';
import * as redeemProcess from '@/queries/redeemProcess';
import * as subscribeProcess from '@/queries/subscribeProcess';
import * as supplementProcess from '@/queries/supplementProcess';
import Asset from '@/ui/main/home/<USER>';
import Navigators from '@/ui/main/home/<USER>';
import TodoList from '@/ui/main/home/<USER>';
import { Space } from 'antd-mobile';
import React, { useEffect } from 'react';
import CollectionMarketPersonnel from '../collectionMarketPersonnel';

const Home = () => {
  const { isLoading: totalAssetLoading } = ta.totalAsset();
  const { isLoading: unreadAmountLoading } = notification.unreadAmount();
  const { isLoading: ownLoading } = appropriateness.own();

  const { isLoading: subscribeAmountLoading } = subscribeProcess.activeAmount();
  const { isLoading: redeemAmountLoading } = redeemProcess.activeAmount();
  const { isLoading: supplementAmountLoading } =
    supplementProcess.activeAmount();

  const loading =
    totalAssetLoading ||
    unreadAmountLoading ||
    ownLoading ||
    subscribeAmountLoading ||
    redeemAmountLoading ||
    supplementAmountLoading;

  if (loading) return <Loading />;

  return (
    <Safe>
      <CollectionMarketPersonnel />
      <div className="pt-12 pb-6">
        <Space block direction="vertical" style={{ '--gap': '1.5rem' }}>
          <div className="mx-7">
            <SvgWrapper component={Logo} fill="white" />
          </div>
          <div className="mx-6">
            <Asset />
          </div>
          <div className="mx-6">
            <Navigators />
          </div>
          <div className="mx-6">
            <TodoList />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => {
  const userQuery = account.user();
  const updateTag = account.createUserTag();

  useEffect(() => {
    if (userQuery.isLoading) {
      return;
    } else {
      updateTag.trigger({
        name: userQuery.data?.name || '',
        identityNumber: userQuery.data?.identityNumber || '',
      });
    }
  }, []);
  // const bankCardQuery = bankCard.list();

  // const loading = userQuery.isLoading || bankCardQuery.isLoading;
  return (
    <React.Fragment>
      <Home />
    </React.Fragment>
  );
};
