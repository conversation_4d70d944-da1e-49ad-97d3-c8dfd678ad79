import { ReactComponent as Logo } from '@/assets/logo.svg';

import SvgWrapper from '@/components/svgWrapper';
import Asset from '@/ui/main/holding/asset';
import HoldingDetail from '@/ui/main/holding/holding-detail';
import SorterPicker from '@/ui/main/holding/sorter-picker';
import TradeDividend from '@/ui/main/holding/trade-dividend';
import { Space } from 'antd-mobile';
import BasisCalculator from '@/ui/main/calculator/basisCalculator';
import DividedCalculator from '@/ui/main/calculator/dividedCalculator';
import ProductCalculator from '@/ui/main/calculator/productCalculator';
import RedemptionCalculator from '@/ui/main/calculator/redemptionCalculator';
import TimeHeldCalculator from '@/ui/main/calculator/timeHeldCalculator';
import React from 'react';
import Safe from '@/components/safe';

const BasisCalculatorPage = () => {
    return (
        <Safe>
            <>
                <BasisCalculator />
            </>
        </Safe>
    );
};

export default () => (
    <React.Fragment>
        <BasisCalculatorPage />
    </React.Fragment>
);
