import React, { useCallback, useState, useEffect } from 'react'
import { Button, Modal, Form, Input, Cascader, Toast } from 'antd-mobile'
import type { CascaderOption, CascaderValueExtend, } from 'antd-mobile/es/components/cascader'
import type { CheckListValue } from 'antd-mobile/es/components/check-list'
import * as shareQuery from '@/queries/ta/share';
import * as regionQuery from '@/queries/ta/region';
// const mockData = [
//     { productName: '产品A', agency: '代销机构1' },
//     { productName: '产品B', agency: '代销机构2' },
//     { productName: '产品C', agency: '代销机构3' }
// ]
const HeaderNotice = () => (
    <div style={{
        fontSize: 13,
        color: '#666',
        lineHeight: 1.5,
        padding: '8px 12px',
        backgroundColor: '#f9f9f9',
        borderRadius: 4,
        marginBottom: 16
    }}>
        根据我司与众多代销方签署的代销协议规定，我司不得直接接触代销客户，
        您可以填写更多信息，帮助我们与代销机构配合更好地服务您
    </div>
)

function transformRegionData(flatData: any[]): CascaderOption[] {
    const nodeMap = new Map();
    flatData.forEach(item => {
        nodeMap.set(item.id, {
            label: item.name,
            value: item.id,
            children: []
        });
    });

    const tree: CascaderOption[] = [];
    flatData.forEach(item => {
        const node = nodeMap.get(item.id);

        if (!item.parent_id) {
            tree.push(node);
        } else {
            const parent = nodeMap.get(item.parent_id);
            if (parent) {
                parent.children!.push(node);
            }
        }
    });

    tree.sort((a, b) => {
        const aValue = a.value?.toString() || '';
        const bValue = b.value?.toString() || '';
        return aValue.localeCompare(bValue);
    });

    const cleanTree = (nodes: CascaderOption[]) => {
        return nodes.map(node => {
            const newNode = { ...node };

            if (newNode.children && newNode.children.length > 0) {
                newNode.children = cleanTree(newNode.children);
            }

            if (newNode.children && newNode.children.length === 0) {
                delete newNode.children;
            }

            return newNode;
        });
    };
    return cleanTree(tree);

}


export default () => {

    const listQuery = shareQuery.userAgentsShareList()

    const regionsRes = regionQuery.getData()

    const regionOptions = regionsRes.data ? transformRegionData(regionsRes.data) : [];


    // console.log(regionOptions,"seeOprionsData");


    const mockData = listQuery.data ? listQuery.data : []

    const [form] = Form.useForm()
    const [cascaderVisible, setCascaderVisible] = useState(false)
    const [initialized, setInitialized] = useState(false)
    const [pendingQueue, setPendingQueue] = useState<typeof mockData>([])

    const handleCascaderConfirm = useCallback((value: CheckListValue[], extend: CascaderValueExtend) => {
        form.setFieldValue('region', extend.items.map(i => i?.label ?? ''))
    }, [form])

    useEffect(() => {
        if (!initialized && mockData.length > 0) {
            setPendingQueue(mockData)
            showNextModal(mockData[0])
            setInitialized(true)
        }
    }, [initialized, mockData])


    const handleAfterClose = () => {

        setPendingQueue(prev => {
            const newQueue = prev.slice(1)

            if (newQueue.length > 0) {
                setTimeout(() => showNextModal(newQueue[0]), 300)
            }

            return newQueue
        })
    }


    const showNextModal = (currentData: typeof mockData[number]) => {
        form.setFieldsValue(currentData)

        Modal.show({
            content: (
                <>
                    <HeaderNotice />
                    <Form form={form} initialValues={currentData} layout="horizontal">
                        <Form.Item name="portfolioName" label="产品名称">
                            <Input placeholder="请输入" clearable readOnly />
                        </Form.Item>
                        <Form.Item name="agency" label="代销机构">
                            <Input placeholder="请输入" clearable readOnly />
                        </Form.Item>
                        <Form.Item name="region" label="所在地区">
                            <Input
                                onClick={() => setCascaderVisible(true)}
                                placeholder='请选择'
                                clearable
                            >
                            </Input>
                        </Form.Item>
                        <Form.Item name="department" label="营业部">
                            <Input placeholder="请输入" clearable />
                        </Form.Item>
                        <Form.Item name="channelPerson" label="渠道人员" rules={[{ required: true, message: "请输入渠道人员" }]}>
                            <Input placeholder="请输入" clearable />
                        </Form.Item>
                    </Form>
                </>
            ),
            actions: [
                {
                    key: 'cancel',
                    text: '暂不提交',
                    onClick: () => {
                        Modal.clear()
                    }
                },
                {
                    key: 'confirm',
                    text: '确认提交',
                    primary: true,
                    onClick: () => {
                        form.validateFields().then(values => {
                            const currentShareId = currentData.id
                            if (values.region && Array.isArray(values.region)) {
                                values.region = values.region.join('')
                            }
                            shareQuery.updateShareList({
                                shareId: currentShareId,
                                ...values
                            }).then(() => {
                                Toast.show({ content: '提交成功' })
                                listQuery.mutate()
                                Modal.clear()
                            }).catch((error) => {
                                Toast.show({ content: '提交失败' })
                                Modal.clear()
                            })

                        })
                        // return false
                    }
                }
            ],
            onClose: handleAfterClose
        })
    }


    return (
        <>
            <Cascader
                options={regionOptions}
                visible={cascaderVisible}
                onClose={() => setCascaderVisible(false)}
                onConfirm={handleCascaderConfirm}
                placeholder="请选择"
            >
            </Cascader>
            {/* <Button block onClick={() => {
                setPendingQueue(mockData)
                setTimeout(() => showNextModal(mockData[0]), 300)
            }}>
                打开多信息收集（{pendingQueue.length}条待处理）
            </Button> */}
            {/* {!initialized && <div>正在加载数据...</div>} */}
        </>
    )
}