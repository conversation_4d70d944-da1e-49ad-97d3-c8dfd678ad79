import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import FilterButton from '@/ui/main/portfolio/filter-button';
import FilterPicker from '@/ui/main/portfolio/filter-picker';
import Portfolios from '@/ui/main/portfolio/portfolios';
import Search from '@/ui/main/portfolio/search';
import MonitoredProducts from '@/ui/main/portfolio/monitoredProducts';
import { Space, Button } from 'antd-mobile';
import React from 'react';

const Portfolio = () => {
  return (
    <Safe>
      <div className="pt-12">
        <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
          <div className="ml-7 mr-6 flex justify-between items-end">
            <SvgWrapper component={Logo} fill="white" />
            <FilterButton />
          </div>
          <div className="mx-3">
            <Search />
          </div>
          <div className="mx-6">
            <Portfolios />
          </div>
          {/* <div className='flex justify-center items-center'>
            <MonitoredProducts />
          </div> */}
        </Space>
      </div>
    </Safe>
  );
};

export default () => {
  return (
    <React.Fragment>
      <Portfolio />
      <FilterPicker />
    </React.Fragment>
  );
};
