import Loading from '@/components/loading';
import Safe from '@/components/safe';
import NetValueHistory from '@/ui/main/portfolio/$id/net-value-history';
import Overview from '@/ui/main/portfolio/$id/overview';
import Report from '@/ui/main/portfolio/$id/report';
import { Space } from 'antd-mobile';
import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'umi';
import { z } from 'zod';

const Portfolio$Id = () => {
  const navigate = useNavigate();
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const idParsed = z.number().safeParse(id);

  useEffect(() => {
    if (idParsed.success) return;
    navigate('/main/portfolio');
  }, [idParsed]);

  if (!idParsed.success) {
    return <Loading />;
  }

  return (
    <Safe>
      <div className="pt-6 pb-6">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.25rem' }}
        >
          <div className="mx-6">
            <Overview />
          </div>
          <div className="mx-6">
            <NetValueHistory />
          </div>
          <div className="mx-6">
            <Report />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Portfolio$Id />
  </React.Fragment>
);
