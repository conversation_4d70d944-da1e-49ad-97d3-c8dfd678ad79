import Safe from '@/components/safe';
import * as query from '@/queries/fileUpload';
import { Loading } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';
// @ts-ignore
import FileViewer from 'react-file-viewer';

export default () => {
  const [id] = useState(new URLSearchParams(location.search).get('id'));
  const { data, isLoading } = query.byId(id);
  const { blob, ext } = data || {};

  const url = useMemo(
    () => (blob ? URL.createObjectURL(blob) : undefined),
    [blob],
  );

  useEffect(() => {
    return () => {
      if (url) URL.revokeObjectURL(url);
    };
  }, [url]);

  if (isLoading) return <Loading />;

  const element =
    ext === 'pdf' ? (
      <div>
        <iframe
          src={`/pdfjs/web/viewer.html?file=${url}`}
          className="w-full"
          style={{ height: 'calc(100vh - 2.8125rem)' }}
        />
      </div>
    ) : (
      <div style={{ height: 'calc(100vh - 2.8125rem)' }}>
        <FileViewer
          fileType={ext}
          filePath={url}
          unsupportedComponent={() => ErrorComponent({ id })}
        />
      </div>
    );

  return <Safe>{element}</Safe>;
};

const ErrorComponent = ({ id }: { id: string | null }) => {
  return (
    <div>
      文件类型不支持预览。<a href={`/api/static?id=${id}`}>点击下载</a>
    </div>
  );
};
