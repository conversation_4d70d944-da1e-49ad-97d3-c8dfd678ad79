import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as query from '@/queries/appropriatenessProcess';
import AppropriatenessApply from '@/ui/secondary/appropriateness/appropriateness-apply';
import AppropriatenessExisting from '@/ui/secondary/appropriateness/appropriateness-existing';
import { Space } from 'antd-mobile';
import React from 'react';

const Appropriateness = () => {
  const ownQuery = query.own();
  const { data, isLoading } = ownQuery;

  if (isLoading) {
    return <Loading />;
  }

  const active = !data?.item || data?.item?.status === 'active';

  return (
    <Safe>
      <div className="py-3">
        <Space block direction="vertical">
          <div className="mx-3">
            {active ? <AppropriatenessApply /> : <AppropriatenessExisting />}
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Appropriateness />
  </React.Fragment>
);
