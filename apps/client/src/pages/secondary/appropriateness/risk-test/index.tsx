import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as query from '@/queries/appropriatenessProcess';
import RiskTest, { RiskTestProps } from '@/ui/secondary/risk-test';
import { toastSuccess } from '@/utils/antd';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  const ownQuery = query.own();
  const mutation = query.riskTest();

  const id = ownQuery.data?.item?.id!;
  if (!id) {
    return <Loading />;
  }

  const onFinish: RiskTestProps['onFinish'] = async (data) => {
    await mutation.trigger({ ...data, id });
    toastSuccess('提交成功');
    navigate('/secondary/appropriateness');
  };

  return (
    <Safe>
      <div className="m-3">
        <RiskTest onFinish={onFinish} />
      </div>
    </Safe>
  );
};
