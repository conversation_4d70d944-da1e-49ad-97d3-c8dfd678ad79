import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as appropriateness from '@/queries/appropriatenessProcess';
import * as query from '@/queries/riskTestProcess';
import RiskTestApply from '@/ui/secondary/risk-test/risk-test-apply';
import { toastError } from '@/utils/antd';
import { Space } from 'antd-mobile';
import React, { useEffect } from 'react';
import { useNavigate } from 'umi';

const RiskTest = () => {
  const navigate = useNavigate();

  const ownQuery = query.own();
  const ownAppropriatenessQuery = appropriateness.own();
  const loading = ownQuery.isLoading || ownAppropriatenessQuery.isLoading;

  const { data } = ownAppropriatenessQuery;
  const appropriatenessActive =
    !!data && (!data.item || data.item.status === 'active');

  useEffect(() => {
    if (!appropriatenessActive) return;
    toastError('请先完成适当性流程');
    navigate('/secondary/appropriateness');
  }, [appropriatenessActive]);

  if (loading || !!appropriatenessActive) {
    return <Loading />;
  }

  return (
    <Safe>
      <div className="py-3">
        <Space block direction="vertical">
          <div className="mx-3">
            <RiskTestApply />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <RiskTest />
  </React.Fragment>
);
