import Loading from '@/components/loading';
import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import React, { useEffect } from 'react';
import useSWRMutation from 'swr/mutation';
import { useNavigate, useParams } from 'umi';
import { z } from 'zod';

const SignRedirect$Id = () => {
  const navigate = useNavigate();
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const idParsed = z.number().safeParse(id);

  useEffect(() => {
    if (idParsed.success) return;
    navigate('/secondary/contract');
  }, [idParsed]);

  if (!idParsed.success) {
    return <Loading />;
  }

  return <Confirm id={idParsed.data} />;
};

const Confirm = ({ id }: { id: number }) => {
  const navigate = useNavigate();

  const mutation = useSWRMutation(
    voidKey,
    (_, { arg }: { arg: { id: number } }) =>
      trpc.redeemProcess.documentsSign.mutate({ id: arg.id }),
    {
      onSuccess: () => {
        toastSuccess('确认成功');
        navigate('/secondary/contract');
      },
    },
  );

  useEffect(() => {
    mutation.trigger({ id });
  }, []);

  return <Loading />;
};

export default () => (
  <React.Fragment>
    <SignRedirect$Id />
  </React.Fragment>
);
