import Safe from '@/components/safe';
import * as query from '@/queries/account';
import { popupQueueAtom } from '@/utils/account';
import { Button, Form, Input, Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const mutation = query.updatePassword();

  const onFinish = async () => {
    const { oldPassword, newPassword } = form.getFieldsValue();
    await mutation.trigger({ oldPassword, newPassword });
    navigate('/main/account');

    popupQueueAtom.complete('resetPassword');
  };

  return (
    <Safe>
      <div className="mx-6 py-6">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.25rem' }}
        >
          <div className="bg-white shadow-custom p-4 rounded-4xl">
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '0.75rem' }}
            >
              <div className="text-lg font-medium px-4 pt-4">修改密码</div>

              <Form
                form={form}
                className="w-full"
                mode="card"
                onFinish={onFinish}
                disabled={mutation.isMutating}
                footer={
                  <Button
                    block
                    color="primary"
                    fill="solid"
                    shape="rounded"
                    type="submit"
                    loading={mutation.isMutating}
                  >
                    提交
                  </Button>
                }
              >
                <Form.Item
                  className="bg-white"
                  label="旧密码"
                  name="oldPassword"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="请输入" type="password" clearable />
                </Form.Item>
                <Form.Item
                  className="bg-white"
                  label="新密码"
                  name="newPassword"
                  rules={[{ required: true }]}
                >
                  <Input placeholder="请输入" type="password" clearable />
                </Form.Item>
                <Form.Item
                  className="bg-white"
                  label="再次输入新密码"
                  name="newPasswordCheck"
                  required
                  rules={[
                    {
                      validator: async (_, value) => {
                        if (!value) {
                          throw new Error('请再次输入新密码');
                        }

                        if (value !== form.getFieldValue('newPassword')) {
                          throw new Error('与新密码不一致');
                        }
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入" type="password" clearable />
                </Form.Item>
              </Form>
            </Space>
          </div>
        </Space>
      </div>
    </Safe>
  );
};
