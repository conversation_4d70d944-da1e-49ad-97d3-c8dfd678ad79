import Safe from '@/components/safe';
import NotificationList from '@/ui/secondary/notification/notification-list';
import Tabs from '@/ui/secondary/notification/tabs';
import { Space } from 'antd-mobile';
import React from 'react';

const Notification = () => {
  return (
    <Safe>
      <div className="my-3">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="mx-6">
            <Tabs />
          </div>
          <div className="mx-6">
            <NotificationList />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Notification />
  </React.Fragment>
);
