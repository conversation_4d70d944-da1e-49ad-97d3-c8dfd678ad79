import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as account from '@/queries/account';
import * as query from '@/queries/identityValidation';
import IdentityVerification from '@/ui/secondary/identity-verification';
import { popupQueueAtom } from '@/utils/account';
import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Form } from 'antd-mobile';
import first from 'lodash/first';
import { useEffect } from 'react';
import { RouterInput } from 'server/typings';
import useSWRMutation from 'swr/mutation';
import { useNavigate } from 'umi';

const redirectUrl = `${location.origin}/secondary/identity-validation/redirect`;

export default () => {
  const [form] = Form.useForm();

  const navigate = useNavigate();

  const userQuery = account.user();
  const validate = userQuery.data?.validateIdentityUponLogin;

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      {
        arg,
      }: { arg: RouterInput['identityVerification']['faceRecognitionUrl'] },
    ) => trpc.identityVerification.faceRecognitionUrl.query(arg),
  );

  const getLink = async () => {
    const { url } = await linkQuery.trigger({ redirectUrl });
    return url;
  };

  const mutation = query.quartlet();
  const { isMutating } = mutation;

  useEffect(() => {
    if (!!validate) return;
    navigate('/main/account');
  }, [validate]);

  if (userQuery.isLoading || !validate) {
    return <Loading />;
  }

  const onFinish = async () => {
    const { bindBankCard, ...rest } = form.getFieldsValue();

    await mutation.trigger({
      ...rest,
      bindBankCard: Boolean(first(bindBankCard)),
      save: true,
    });

    toastSuccess('认证成功');
    navigate('/main/account');

    popupQueueAtom.complete('validateIdentity');
  };

  return (
    <Safe>
      <div className="mx-6 py-6">
        <IdentityVerification
          title="实名认证"
          faceRecognitionProps={{
            link: getLink,
            loading: linkQuery.isMutating,
          }}
          quartletProps={{
            form,
            loading: isMutating,
            disabled: isMutating,
            onFinish,
          }}
        />
      </div>
    </Safe>
  );
};
