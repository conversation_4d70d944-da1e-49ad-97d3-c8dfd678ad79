import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as validation from '@/queries/identityValidation';
import * as query from '@/queries/signPassword';
import SignPassword from '@/ui/secondary/sign-password';
import { getSignPasswordRedirect } from '@/utils/redirect';
import { getSearchString } from '@/utils/url';
import { useEffect } from 'react';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  const statusQuery = validation.status();
  const status = statusQuery.data?.status;

  const mutation = query.create();

  useEffect(() => {
    if (status !== false) return;

    const searchString = getSearchString();
    navigate(`/secondary/sign-password/identity-validation${searchString}`);
  }, [status]);

  if (statusQuery.isLoading || !status) {
    return <Loading />;
  }

  const onFinish = async (password: string) => {
    await mutation.trigger({ password });
    navigate(getSignPasswordRedirect());
  };

  return (
    <Safe>
      <div className="mx-6 py-6">
        <SignPassword onFinish={onFinish} loading={mutation.isMutating} />
      </div>
    </Safe>
  );
};
