import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as query from '@/queries/identityValidation';
import IdentityVerification from '@/ui/secondary/identity-verification';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { useSearchString } from '@/utils/url';
import { useEffect } from 'react';
import { RouterInput } from 'server/typings';
import useSWRMutation from 'swr/mutation';
import { useNavigate } from 'umi';

const redirectUrl = `${location.origin}/secondary/sign-password/identity-validation/redirect`;

export default () => {
  const navigate = useNavigate();
  const searchString = useSearchString();

  const statusQuery = query.status();
  const status = statusQuery.data?.status;

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      {
        arg,
      }: { arg: RouterInput['identityVerification']['faceRecognitionUrl'] },
    ) => trpc.identityVerification.faceRecognitionUrl.query(arg),
  );

  const getLink = async () => {
    const { url } = await linkQuery.trigger({ redirectUrl });
    return url;
  };

  useEffect(() => {
    if (!status) return;
    navigate(`/secondary/sign-password${searchString}`);
  }, [status]);

  if (statusQuery.isLoading || !!status) {
    return <Loading />;
  }

  return (
    <Safe>
      <div className="mx-6 py-6">
        <IdentityVerification
          disableQuartlet
          title="设置签署密码前，请先进行身份认证"
          faceRecognitionProps={{
            link: getLink,
            loading: linkQuery.isMutating,
          }}
        />
      </div>
    </Safe>
  );
};
