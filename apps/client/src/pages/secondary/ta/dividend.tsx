import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import DividendDetail from '@/ui/secondary/dividend/dividend-detail';
import DividendHistory from '@/ui/secondary/dividend/dividend-history';
import FilterPicker from '@/ui/secondary/dividend/filter-picker';
import PortfolioSearch from '@/ui/secondary/dividend/portfolio-search';
import { Space } from 'antd-mobile';
import React from 'react';

const Dividend = () => {
  return (
    <Safe>
      <div className="pt-[0.1875rem]">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="mx-7">
            <SvgWrapper component={Logo} />
          </div>
          <div className="mx-6">
            <PortfolioSearch />
          </div>
          <div className="mx-6">
            <DividendHistory />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Dividend />
    <FilterPicker />
    <DividendDetail />
  </React.Fragment>
);
