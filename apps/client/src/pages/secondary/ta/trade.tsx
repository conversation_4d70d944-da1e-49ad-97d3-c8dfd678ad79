import { ReactComponent as Logo } from '@/assets/logo.svg';
import Safe from '@/components/safe';
import SvgWrapper from '@/components/svgWrapper';
import FilterPicker from '@/ui/secondary/trade/filter-picker';
import PortfolioSearch from '@/ui/secondary/trade/portfolio-search';
import TradeDetail from '@/ui/secondary/trade/trade-detail';
import TradeHistory from '@/ui/secondary/trade/trade-history';
import { Space } from 'antd-mobile';
import React from 'react';

const Trade = () => {
  return (
    <Safe>
      <div className="pt-[0.1875rem]">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="mx-7">
            <SvgWrapper component={Logo} />
          </div>
          <div className="mx-6">
            <PortfolioSearch />
          </div>
          <div className="mx-6">
            <TradeHistory />
          </div>
        </Space>
      </div>
    </Safe>
  );
};

export default () => (
  <React.Fragment>
    <Trade />
    <FilterPicker />
    <TradeDetail />
  </React.Fragment>
);
