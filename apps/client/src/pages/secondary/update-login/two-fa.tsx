import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as account from '@/queries/account';
import TwoFa from '@/ui/secondary/update-login/two-fa';
import { useEffect } from 'react';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  const userQuery = account.user();
  const { data: user, isLoading } = userQuery;

  useEffect(() => {
    if (!user) return;
    if (!!user.email || !!user.phone) return;

    navigate('/secondary/update-login/update');
  }, [user]);

  if (isLoading || (!user?.email && !user?.phone)) {
    return <Loading />;
  }

  return (
    <Safe>
      <div className="mx-6 py-6">
        <TwoFa />
      </div>
    </Safe>
  );
};
