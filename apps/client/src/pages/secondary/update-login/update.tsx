import Loading from '@/components/loading';
import Safe from '@/components/safe';
import * as account from '@/queries/account';
import * as query from '@/queries/user';
import Update from '@/ui/secondary/update-login/update';
import { useEffect } from 'react';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  const statusQuery = query.genericCodeVerificationStatus();
  const userQuery = account.user();

  const { data: user } = userQuery;
  const { data: status } = statusQuery;

  const loading =
    userQuery.isLoading || statusQuery.isLoading || statusQuery.isValidating;

  useEffect(() => {
    if (loading) return;
    if (!user || !status) return;
    if (!!status.valid) return;
    if (!user.phone && !user.email) return;

    navigate('/secondary/update-login/two-fa');
  }, [user, status, loading]);

  if (loading) {
    return <Loading />;
  }

  return (
    <Safe>
      <div className="mx-6 py-6">
        <Update />
      </div>
    </Safe>
  );
};
