import { ReactComponent as LogoColor } from '@/assets/logo-color.svg';
import Checkbox from '@/components/checkbox';
import SvgWrapper from '@/components/svgWrapper';
import * as auth from '@/queries/auth';
import space from '@/styles/space.less';
import { guideDialog } from '@/ui/tos/guide';
import TOSButtons from '@/ui/tos/tos-buttons';
import { toastError, toastSuccess } from '@/utils/antd';
import { useTimer } from '@/utils/timer';
import { phone } from '@portfolio-service/schema/utils';
import { Button, DotLoading, Form, Input, Modal, Space } from 'antd-mobile';
import React, { useMemo, useState } from 'react';
import { useNavigate } from 'umi';
import { z } from 'zod';

export default () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { trigger, isMutating } = auth.login();

  const [passwordFocus, setPasswordFocus] = useState(false);

  const [username, setUsername] = useState<string>();
  const [step, setStep] = useState<'username' | 'login'>('username');

  const type = useMemo(() => {
    if (!username) return;

    if (phone.safeParse(username).success) {
      return 'phone';
    } else if (z.string().email().safeParse(username).success) {
      return 'email';
    } else {
      return 'identityNumber';
    }
  }, [username]);

  const checkCheckbox = () => {
    return new Promise<boolean>((resolve) => {
      if (form.getFieldValue('agreeTos')) {
        resolve(true);
        return;
      }

      Modal.show({
        title: (
          <span>
            <span>请阅读并同意</span>
            <TOSButtons />
          </span>
        ),
        bodyStyle: {
          borderRadius: '32px',
          padding: '1.5rem 1.5rem 1rem 1.5rem',
        },
        closeOnAction: true,
        actions: [
          {
            key: 'confirm',
            text: '同意',
            primary: true,
            style: { borderRadius: '9999px' },
            onClick: () => {
              form.setFieldValue('agreeTos', true);
              resolve(true);
            },
          },
          {
            key: 'cancel',
            text: '不同意',
            onClick: () => resolve(false),
          },
        ],
      });
    });
  };

  const onNext = async () => {
    await form.validateFields();
    if (!(await checkCheckbox())) return;

    const username = form.getFieldValue('username');
    setUsername(username);
    setStep('login');
  };

  const onPrev = () => {
    setStep('username');
  };

  const onFinish = async () => {
    if (!(await checkCheckbox())) return;

    const { username, password } = form.getFieldsValue();
    if (!username || !password) return;

    if (type === 'email') {
      await trigger({ type: 'email', email: username, code: password });
    } else if (type === 'phone') {
      await trigger({ type: 'phone', phone: username, code: password });
    } else {
      await trigger({ type: 'identityNumber', username, password });
    }
  };

  return (
    <div className="h-full flex flex-col justify-center items-center px-6">
      <div className="mb-6">
        <SvgWrapper component={LogoColor} />
      </div>
      <Form
        form={form}
        onFinish={onFinish}
        className="w-full"
        mode="card"
        initialValues={{ agreeTos: true }}
        disabled={isMutating}
        footer={
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '1rem' }}
          >
            {step === 'username' && (
              <Button
                block
                color="primary"
                fill="solid"
                size="large"
                shape="rounded"
                onClick={onNext}
              >
                下一步
              </Button>
            )}
            {step === 'login' && (
              <Space
                block
                style={{ '--gap-horizontal': '1.5rem' }}
                className={space.stretch}
              >
                <Button
                  block
                  fill="solid"
                  size="large"
                  shape="rounded"
                  onClick={onPrev}
                >
                  上一步
                </Button>
                <Button
                  block
                  color="primary"
                  fill="solid"
                  size="large"
                  type="submit"
                  shape="rounded"
                  loading={isMutating}
                >
                  登录
                </Button>
              </Space>
            )}
            <Space block justify="center">
              <a
                style={{ color: 'var(--adm-color-primary)' }}
                onClick={() => navigate('/register')}
              >
                注册
              </a>
            </Space>
            <Space block justify="center">
              <a
                style={{ color: 'var(--adm-color-primary)' }}
                onClick={() => navigate('/forget-password')}
              >
                忘记密码
              </a>
            </Space>
            <Space block justify="center">
              <a
                style={{ color: 'var(--adm-color-primary)' }}
                onClick={() => guideDialog.show()}
              >
                查看登录注册指引
              </a>
            </Space>
            <Space block justify="center" className="mt-4">
              <a
                style={{ color: 'var(--adm-color-primary)' }}
                onClick={() => (location.href = 'https://beian.miit.gov.cn')}
              >
                沪ICP备20002316号-1
              </a>
            </Space>
          </Space>
        }
      >
        <Form.Item
          label="手机号/邮箱/证件号"
          name="username"
          rules={[{ required: true }]}
        >
          <Input
            placeholder="请输入"
            clearable
            disabled={step !== 'username'}
          />
        </Form.Item>
        {step === 'login' && (
          <React.Fragment>
            {type === 'identityNumber' && (
              <Form.Item
                label={
                  <span>
                    <span>密码</span>
                    {passwordFocus && (
                      <span style={{ color: 'var(--adm-color-weak)' }}>
                        （初始密码默认为证件号码后 8 位）
                      </span>
                    )}
                  </span>
                }
                name="password"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入"
                  type="password"
                  clearable
                  onFocus={() => setPasswordFocus(true)}
                  onBlur={() => setPasswordFocus(false)}
                />
              </Form.Item>
            )}
            {(type === 'phone' || type === 'email') && (
              <Form.Item
                label="验证码"
                name="password"
                rules={[{ required: true }]}
                extra={<SendCodeButton form={form} />}
              >
                <Input placeholder="请输入" clearable maxLength={6} />
              </Form.Item>
            )}
          </React.Fragment>
        )}
        <Form.Item name="agreeTos">
          <Checkbox>
            <span
              className="text-xs"
              style={{ color: 'var(--adm-color-weak)' }}
            >
              <span>我已阅读并同意</span>
              <TOSButtons />
            </span>
          </Checkbox>
        </Form.Item>
      </Form>
    </div>
  );
};

const SendCodeButton = ({
  form,
}: {
  form: ReturnType<typeof Form.useForm>[0];
}) => {
  const { trigger, isMutating } = auth.sendCode();
  const timer = useTimer();

  const sendCode = async () => {
    await form.validateFields(['username']);
    const name = form.getFieldValue('username');
    let data: Parameters<typeof trigger>[0]['data'] | undefined = undefined;

    if (z.string().email().safeParse(name).success) {
      data = { type: 'email', email: name };
    } else if (phone.safeParse(name).success) {
      data = { type: 'phone', phone: name };
    }

    if (!data) {
      toastError('格式不正确');
      return;
    }

    await trigger({ data });
    toastSuccess('发送成功');
    timer.start(60);
  };

  if (isMutating) {
    return <DotLoading />;
  }

  if (timer.status === 'active') {
    return <span>再次发送（{timer.countDown}）</span>;
  }

  return (
    <a style={{ color: 'var(--adm-color-primary)' }} onClick={sendCode}>
      <span>发送验证码</span>
    </a>
  );
};
