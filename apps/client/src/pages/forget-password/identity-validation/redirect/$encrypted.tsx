import Loading from '@/components/loading';
import { toastError, toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import React, { useEffect, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { useNavigate, useParams, useSearchParams } from 'umi';

const FaceRecognitionRedirect$Encrypted = () => {
  const [toasted, setToasted] = useState(false);

  const navigate = useNavigate();
  const params = useParams();
  const encrypted = params.encrypted;

  const [search] = useSearchParams();
  const pass = Boolean(Number(search.get('is_success')));

  useEffect(() => {
    if (pass || toasted) return;

    toastError('人脸识别失败，请重新识别');
    setToasted(true);
  }, [pass]);

  useEffect(() => {
    if (!!encrypted && !!pass) return;
    navigate('/forget-password');
  }, [encrypted, pass]);

  if (!encrypted || !pass) {
    return <Loading text="认证中，请勿刷新页面或关闭窗口" />;
  }

  return <Confirm encrypted={encrypted} />;
};

const Confirm = ({ encrypted }: { encrypted: string }) => {
  const navigate = useNavigate();

  const mutation = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { encrypted: string } }) =>
      trpc.auth.forgetPasswordFaceRecognition.mutate({
        encrypted: arg.encrypted,
      }),
    {
      onSuccess: () => {
        toastSuccess('识别成功');
        navigate('/forget-password/update-password');
      },
    },
  );

  useEffect(() => {
    mutation.trigger({ encrypted });
  }, []);

  return <Loading text="认证中，请勿刷新页面或关闭窗口" />;
};

export default () => (
  <React.Fragment>
    <FaceRecognitionRedirect$Encrypted />
  </React.Fragment>
);
