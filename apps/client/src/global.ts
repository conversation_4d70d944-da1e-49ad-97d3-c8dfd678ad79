// @ts-nocheck

import debounce from 'lodash/debounce';
import { scaleAtom } from './store';

// import { appId } from './conf/wechat';
// import { wechatSdkReadyAtom } from './store/wechat';
// import { trpc } from './utils/trpc';

// trpc.wechat.signature.query({ url: location.href }).then((response) => {
//   const { nonce, signature, timestamp } = response;

//   wx.ready(() => {
//     wechatSdkReadyAtom.store.setState(true);
//   });

//   wx.config({
//     // debug: process.env.NODE_ENV === 'development',
//     appId,
//     timestamp,
//     signature,
//     nonceStr: nonce,
//     jsApiList: ['scanQRCode'],
//   });
// });

if (
  typeof WeixinJSBridge == 'object' &&
  typeof WeixinJSBridge.invoke == 'function'
) {
  handleFontSize();
} else {
  if (document.addEventListener) {
    document.addEventListener('WeixinJSBridgeReady', handleFontSize, false);
  } else if (document.attachEvent) {
    document.attachEvent('WeixinJSBridgeReady', handleFontSize);
    document.attachEvent('onWeixinJSBridgeReady', handleFontSize);
  }
}
function handleFontSize() {
  WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: '2' });
  WeixinJSBridge.on('menu:setfont', function () {
    WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: '2' });
  });
}

const normalizeSize = () => {
  const scale = Math.min(window.innerWidth / 430, 1);
  scaleAtom.store.setState(scale);
};

normalizeSize();

const observer = new ResizeObserver(debounce(normalizeSize, 500));
observer.observe(document.body);
