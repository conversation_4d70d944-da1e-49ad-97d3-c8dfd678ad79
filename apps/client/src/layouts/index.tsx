import Loading from '@/components/loading';
import * as account from '@/queries/account';
import Nav from '@/ui/nav';
import Tabs from '@/ui/tabs';
import Tos from '@/ui/tos';
import { popupQueueAtom, usePopupsUponLogin } from '@/utils/account';
import React, { useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const isAuthPage = pathname.startsWith('/auth');
  const isRegisterPage = pathname.startsWith('/register');
  const isForgetPasswordPage = pathname.startsWith('/forget-password');
  const isRedirect = ['/', '/main'].includes(pathname);
  const showTabs = /^\/main\/(home|portfolio|holding|account|calculator)$/.test(pathname);
  const showNav =
    !isAuthPage &&
    !isRegisterPage &&
    !isForgetPasswordPage &&
    !isRedirect &&
    !showTabs;
  const showBannerBackground = showBannerRoutes.includes(pathname);

  const userQuery = account.user();
  const { data: user, isLoading } = userQuery;

  usePopupsUponLogin();

  const allowPath = popupQueueAtom.useAllowPath(pathname);

  useEffect(() => {
    if (isLoading) return;

    if (!!user && (isAuthPage || isRegisterPage || isForgetPasswordPage)) {
      navigate('/');
    } else if (
      !user &&
      !isAuthPage &&
      !isRegisterPage &&
      !isForgetPasswordPage
    ) {
      navigate('/auth');
    }
  }, [user, isLoading, isAuthPage, isRegisterPage, isForgetPasswordPage]);

  if (isLoading) {
    return <Loading />;
  }

  if (!allowPath && !isAuthPage && !isRegisterPage && !isForgetPasswordPage) {
    return <Loading />;
  }

  return (
    <React.Fragment>
      <div className="h-screen">
        {showNav && (
          <div className="fixed top-0 w-full z-50">
            <div className="container">
              <Nav />
            </div>
          </div>
        )}
        <div className="container h-full relative overflow-y-auto">
          {showBannerBackground && (
            <div className="absolute top-0 left-0 right-0 h-56">
              <div
                className="h-full rounded-b-4xl"
                style={{ backgroundColor: 'var(--adm-color-primary)' }}
              />
            </div>
          )}
          <div className="absolute inset-x-0 inset-y-0">
            <Outlet />
          </div>
        </div>
        {showTabs && (
          <div className="fixed bottom-0 w-full z-50">
            <div className="container">
              <Tabs />
            </div>
          </div>
        )}
      </div>
      <Tos />
    </React.Fragment>
  );
};

const showBannerRoutes = [
  '/main/home',
  '/main/portfolio',
  '/main/newPeriodicReport',
  "/main/calculator",
  '/main/holding',
  '/main/account',
  '/secondary/ta/trade',
  '/secondary/ta/dividend',
];
