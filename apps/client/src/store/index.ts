import { createAtom } from '@/utils/zustand';
import { createStore } from 'zustand';
import { persist } from 'zustand/middleware';

type ShowGuideOnLoadStore = {
  value: boolean;
};
const defaultShowGuideOnLoadStore: ShowGuideOnLoadStore = {
  value: true,
};

export const showGuideOnLoadStore = createStore<ShowGuideOnLoadStore>()(
  persist(() => defaultShowGuideOnLoadStore, {
    name: 'showGuideOnLoad',
    version: 1,
  }),
);

export const scaleAtom = createAtom<number | undefined>(undefined);

scaleAtom.store.subscribe((scale) => {
  if (scale == null) return;
  document.documentElement.style.fontSize = `${16 * scale}px`;
});
