import { ReactComponent as AccountActive } from '@/assets/account-active.svg';
import { ReactComponent as Account } from '@/assets/account.svg';
import { ReactComponent as HoldingActive } from '@/assets/holding-active.svg';
import { ReactComponent as Holding } from '@/assets/holding.svg';
import { ReactComponent as HomeActive } from '@/assets/home-active.svg';
import { ReactComponent as Home } from '@/assets/home.svg';
import { ReactComponent as PortfolioActive } from '@/assets/portfolio-active.svg';
import { ReactComponent as Portfolio } from '@/assets/portfolio.svg';
import { ReactComponent as Calc } from "@/assets/计算器未选中.svg";
import { ReactComponent as CalcActive } from '@/assets/计算器选中.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/account';
import * as riskTestProcess from '@/queries/riskTestProcess';
import { Badge, TabBar } from 'antd-mobile';
import { useLocation, useNavigate } from 'umi';

const extractPathnameKey = (pathname: string) => {
  const reg = /^\/main\/(home|portfolio|holding|account|calculator)(\/|$)/;
  const result = pathname.match(reg);
  if (!result) return 'home';

  const [match] = result;
  const [_, __, key] = match.split('/');
  return key;
};

export default () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const { data: user } = query.user();
  const { data: riskTest } = riskTestProcess.own();

  const activeKey = extractPathnameKey(pathname);
  const homeSVG = activeKey === 'home' ? HomeActive : Home;
  const portfolioSVG = activeKey === 'portfolio' ? PortfolioActive : Portfolio;
  const accountSVG = activeKey === 'account' ? AccountActive : Account;
  const holdingSVG = activeKey === 'holding' ? HoldingActive : Holding;
  const calculateSVG = activeKey === "calculator" ? CalcActive : Calc;

  const home = <SvgWrapper component={homeSVG} />;
  const portfolio = <SvgWrapper component={portfolioSVG} />;
  const account = <SvgWrapper component={accountSVG} />;
  const holding = <SvgWrapper component={holdingSVG} />;
  const calculator = <SvgWrapper component={calculateSVG} style={{
    ...(activeKey === 'calculator' ? {
      width: 26,
      height: 26,
    } : {
      width: 26,
      height: 26,  // 未选中状态颜色
      fill: 'rgba(153, 153, 153, 1)'
    })
  }} />;

  const showAccountBadge =
    !!user?.passwordReset || riskTest?.item?.status === 'active';

  return (
    <TabBar
      className="px-6 pt-2"
      safeArea
      style={{
        backgroundColor: '#fafbfc',
        height: 'calc(3.4375rem + env(safe-area-inset-bottom))',
      }}
      key={activeKey}
      defaultActiveKey={activeKey}
      onChange={(key) => navigate(`/main/${key}`)}
    >
      <TabBar.Item key="home" icon={home} title="主页" />
      <TabBar.Item key="portfolio" icon={portfolio} title="产品" />
      <TabBar.Item key="holding" icon={holding} title="持仓" />
      <TabBar.Item key="calculator" icon={calculator} title="计算器" />
      <TabBar.Item
        key="account"
        icon={
          <div className="relative">
            {account}
            {showAccountBadge && (
              <div className="absolute -top-3 -right-2">
                <Badge content={Badge.dot} />
              </div>
            )}
          </div>
        }
        title="我的"
      />
    </TabBar>
  );
};
