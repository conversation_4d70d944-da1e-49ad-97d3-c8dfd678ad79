import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import { showGuideOnLoadStore } from '@/store';
import { createPopup } from '@/utils/zustand';
import { Popup, Space } from 'antd-mobile';
import { useEffect } from 'react';
import { useLocation } from 'umi';
import { useStore } from 'zustand';

export const guideDialog = createPopup();

export default () => {
  const { pathname } = useLocation();
  const isAuthOrRegisterPage =
    pathname.startsWith('/auth') || pathname.startsWith('/register');

  const { open } = guideDialog.use();
  const { value } = useStore(showGuideOnLoadStore);

  const { data: user, isLoading } = account.user();

  useEffect(() => {
    if (!value || !isAuthOrRegisterPage || isLoading || !!user) {
      if (!!user) {
        showGuideOnLoadStore.setState({ value: false });
      }

      return;
    }

    guideDialog.show();
    showGuideOnLoadStore.setState({ value: false });
  }, [value, pathname, user, isLoading, isAuthOrRegisterPage]);

  return (
    <Popup
      visible={open}
      onClose={() => guideDialog.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: 'calc(29.125rem + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        onClick={() => guideDialog.hide()}
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      >
        <SvgWrapper component={DownArrow} />
      </div>
      <div
        className="mt-8 p-8 rounded-4xl"
        style={{ backgroundColor: 'var(--adm-color-box)' }}
      >
        <Space
          block
          direction="vertical"
          className="whitespace-pre-wrap break-all"
        >
          <strong>登录注册指引</strong>
          <span>
            对于已持有衍复产品的用户，登录可以输入证件号码，通过密码登录；或输入已绑定的手机号，通过验证码登录。
          </span>
          <span>未持有衍复产品，首次注册本平台需要准备：</span>
          <span>
            （1）投资者资料信息，在身份验证时填写，务必与将办理基金认申购业务所填写的交易资料一致；
          </span>
          <span>
            （2）管理人邀约码，在授权验证时填写，可联系管理人获取邀约码；
          </span>
          <span>（3）手机或邮箱，用于接收验证码。</span>

          <strong>
            请认真阅读并同意《网络服务协议书》、《隐私政策》、《人脸识别服务协议》、《合格投资者承诺书》，并遵守相关内容。
          </strong>
        </Space>
      </div>
    </Popup>
  );
};
