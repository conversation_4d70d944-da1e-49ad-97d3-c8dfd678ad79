import { leftPad } from '@/utils/leftPad';
import { useTimer } from '@/utils/timer';
import { createPopup } from '@/utils/zustand';
import { Button, Popup, Space } from 'antd-mobile';
import { useEffect } from 'react';

type CFCATosData = {
  name: string;
  identityType: string | null;
  identityNumber: string;
};

export const cfcaTos = createPopup<CFCATosData>();

export default () => {
  const { open, data } = cfcaTos.use();
  const timer = useTimer();

  const waiting = timer.status === 'active';

  useEffect(() => {
    if (!open) return;
    timer.start(5);
  }, [open]);

  const onClose = () => {
    cfcaTos.hide();
    timer.setTime(5);
  };

  return (
    <Popup
      visible={open}
      bodyStyle={{
        height: 'calc(34.75rem + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      />

      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div
          className="p-8 rounded-4xl"
          style={{ backgroundColor: 'var(--adm-color-box)' }}
        >
          <Space
            block
            direction="vertical"
            className="whitespace-pre-wrap break-all"
          >
            <strong>CFCA 数字证书服务合同</strong>
            <span>甲方名称（订户）：{data?.name || ''}</span>
            <span>证件类型：{data?.identityType || ''}</span>
            <span>证件号码：{data?.identityNumber || ''}</span>
            <span>乙方：中金金融认证中心有限公司</span>
            <span>
              {leftPad(
                '为明确双方的权利和义务，订户（甲方）和中金金融认证中心有限公司（CFCA）（乙方）就“ 电子签名数字证书 ”（以下简称“数字证书 ”）相关事项达成本合同，以资共同遵守。',
              )}
            </span>
            <span>
              {leftPad(
                '1 、本合同构成甲方与乙方之间的权利义务约定，甲方在签署本合同之前，应仔细阅读本合同的 全部内容。甲方在线勾选“ 同意 ”即视为签署本合同，表明甲方同意接受和愿意遵守本合同的所有条 款（包括第 3 条约定的内容）。',
              )}
            </span>
            <span>
              {leftPad(
                '2 、甲方同意委托四川妥妥递科技有限公司代甲方向乙方申请数字证书，数字证书有效期为（ 1 小时 ），证书服务费为 0 元/张/年。甲方同意提交甲方的名称、证件类型、证件号码等信息用 于向乙方申请数字证书。甲方确认所提交的身份信息真实、有效，并授权乙方至少在数字证书失效后 5 年内保存甲方的上述信息，法律、行政法规另有规定的依其规定。甲方知悉数字证书将绑定甲方的 身份信息，使用数字证书作出的电子签名将代表甲方的真实意思表示，数据电文经过电子签名后即代表甲方知悉并认可其中所载内容。',
              )}
            </span>
            <span>
              {leftPad(
                '3 、甲方应当在勾选“ 同意 ”之前登录乙方官网（www.cfca.com.cn）阅读《CFCA 数字证书服务协议》、《数字证书使用安全提示》、《CFCA 认证业务规则》以了解有关数字证书的事项及与乙方之间的权利义务内容，其内容对双方均有约束力。',
              )}
            </span>
            <span>
              {leftPad(
                '4 、在乙方发放数字证书后，甲方授权四川妥妥递科技有限公司将数字证书托管在四川妥妥递科技有限公司为此设立的服务器上。四川妥妥递科技有限公司将按照国家法律法规等规定严格履行安全保管义务，仅在获得甲方的授权后由甲方按照授权内容调用数字证书制作电子签名。',
              )}
            </span>
            <span>
              {leftPad(
                '5 、本合同的签署意味着甲方与乙方之间成立数字证书服务合同关系。当双方对有关数字证书的服务事项发生争议时，应首先通过友好协商方式解决；双方不能达成一致意见的，任何一方可以向北 京仲裁委员会申请仲裁，按照该会规则在北京进行仲裁，仲裁裁决是终局的，对任何一方均有约束力。',
              )}
            </span>
          </Space>
        </div>
        <Button
          block
          color={waiting ? undefined : 'primary'}
          shape="rounded"
          disabled={waiting}
          onClick={onClose}
        >
          <span>我已知晓</span>
          {waiting && <span>（{timer.countDown}）</span>}
        </Button>
      </Space>
    </Popup>
  );
};
