import React from 'react';
import Appropriateness from './appropriateness';
import CFCA from './cfca';
import FaceRecognition from './face-recognition';
import Guide from './guide';
import Net from './net';
import Privacy from './privacy';

export default () => (
  <React.Fragment>
    <Guide />
    <Net />
    <Privacy />
    <FaceRecognition />
    <Appropriateness />
    <CFCA />
  </React.Fragment>
);
