import { leftPad } from '@/utils/leftPad';
import { useTimer } from '@/utils/timer';
import { createPopup } from '@/utils/zustand';
import { Button, Popup, Space } from 'antd-mobile';
import { useEffect } from 'react';

export const appropriatenessTos = createPopup();

export default () => {
  const { open } = appropriatenessTos.use();
  const timer = useTimer();

  const waiting = timer.status === 'active';

  useEffect(() => {
    if (!open) return;
    timer.start(5);
  }, [open]);

  const onClose = () => {
    appropriatenessTos.hide();
    timer.setTime(5);
  };

  return (
    <Popup
      visible={open}
      bodyStyle={{
        height: 'calc(34.75rem + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      />

      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div
          className="p-8 rounded-4xl"
          style={{ backgroundColor: 'var(--adm-color-box)' }}
        >
          <Space
            block
            direction="vertical"
            className="whitespace-pre-wrap break-all"
          >
            <strong>合格投资者承诺书</strong>
            <span>上海衍复投资管理有限公司：</span>
            <span>
              {leftPad(
                '本人/本单位承诺符合中国证券监督管理委员会规定的私募投资基金的合格 投资者（即具备相应风险识别能力和风险承担能力，投资于本基金的金额不低于 100 万元，且个人投资者的金融资产不低于 300 万元或者最近三年个人年均收入 不低于 50 万元，机构投资者的净资产不低于 1000 万元，或为监管机构认可的其他合格投资者：若法律法规对上述要求有变化的，以最新要求为准）。',
              )}
            </span>
            <span>
              {leftPad(
                '本人/本单位承诺，在接收贵公司发送的招募说明书等基金推介材料，以及 基金净值、基金信披报告等业绩相关信息后，不向任何其他机构或个人展示和传递。',
              )}
            </span>
            <span>
              {leftPad(
                '本人/本单位承诺投资资金来源合法，没有非法汇集他人资金投资本基金， 具备相应出资能力，且系为自己购买本基金，不存在代持，亦没有违反监管规定通过资产管理产品（包括私募投资基金）投资私募基金。',
              )}
            </span>
            <span>
              {leftPad(
                '本人/本单位承诺在参与管理人发起设立的私募基金的投资过程中，如果因 存在欺诈、隐瞒或其他不符合实际情况的陈述所产生的一切责任，由本人/本单位自行承担，与管理人无关。',
              )}
            </span>
            <span>{leftPad('特此承诺。')}</span>
          </Space>
        </div>
        <Button
          block
          color={waiting ? undefined : 'primary'}
          shape="rounded"
          disabled={waiting}
          onClick={onClose}
        >
          <span>我已知晓</span>
          {waiting && <span>（{timer.countDown}）</span>}
        </Button>
      </Space>
    </Popup>
  );
};
