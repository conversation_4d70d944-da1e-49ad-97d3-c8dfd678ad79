import React from 'react';
import { netTos } from './net';
import { privacyTos } from './privacy';
import { faceRecognitionTos } from './face-recognition';
import { appropriatenessTos } from './appropriateness';

export default () => {
  const style: React.CSSProperties = { color: 'var(--adm-color-primary)' };

  return (
    <span>
      <a
        style={style}
        onClick={(e) => {
          e.preventDefault();
          netTos.show();
        }}
      >
        《网络服务协议书》
      </a>
      <span>、</span>
      <a
        style={style}
        onClick={(e) => {
          e.preventDefault();
          privacyTos.show();
        }}
      >
        《隐私政策》
      </a>
      <span>、</span>
      <a
        style={style}
        onClick={(e) => {
          e.preventDefault();
          faceRecognitionTos.show();
        }}
      >
        《人脸识别服务协议》
      </a>
      <span>、</span>
      <a
        style={style}
        onClick={(e) => {
          e.preventDefault();
          appropriatenessTos.show();
        }}
      >
        《合格投资者承诺书》
      </a>
    </span>
  );
};
