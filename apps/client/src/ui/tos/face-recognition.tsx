import { leftPad } from '@/utils/leftPad';
import { useTimer } from '@/utils/timer';
import { createPopup } from '@/utils/zustand';
import { Button, Popup, Space } from 'antd-mobile';
import { useEffect } from 'react';

export const faceRecognitionTos = createPopup();

export default () => {
  const { open } = faceRecognitionTos.use();
  const timer = useTimer();

  const waiting = timer.status === 'active';

  useEffect(() => {
    if (!open) return;
    timer.start(5);
  }, [open]);

  const onClose = () => {
    faceRecognitionTos.hide();
    timer.setTime(5);
  };

  return (
    <Popup
      visible={open}
      bodyStyle={{
        height: 'calc(34.75rem + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      />

      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div
          className="p-8 rounded-4xl"
          style={{ backgroundColor: 'var(--adm-color-box)' }}
        >
          <Space
            block
            direction="vertical"
            className="whitespace-pre-wrap break-all"
          >
            <strong>人脸识别服务协议</strong>

            <span>版本更新日期：2023 年 8 月 1 日</span>
            <span>版本生效日期：2023 年 8 月 1 日</span>

            <span>
              {leftPad(
                '欢迎您使用人脸识别功能，本服务协议是【上海衍复投资管理有限公司 】 （“本公司 ”）与用户（“您 ”）为明确服务内容而签订的有效协议。在接受本协议之前，请  仔细阅读本协议的全部内容。',
              )}
            </span>
            <span>{leftPad('一、功能说明')}</span>
            <span>
              {leftPad(
                '为保障用户账户的安全，提供更好的服务，本公司在提供部分产品及服务之前，采用人脸 识别和身份验证功能对用户的身份进行认证，用于验证操作人是否为账户持有者本人，通过人 脸识别结果评估是否为用户提供产品或服务。该功能由北京今始科技有限公司、四川妥妥递科 技有限公司等机构提供核验数据及技术支持。',
              )}
            </span>
            <span>{leftPad('二、授权与许可')}</span>
            <span>
              {leftPad(
                '如您点击“ 同意 ”或以其他方式选择接受本协议规则，则视为您在使用人脸识别服务时， 同意并授权本公司获取、使用您在申请本公司服务过程中所提供的个人信息（包括姓名、身份 证号、身份证照片）及人脸影像数据，并提供给合法存有您信息的第三方合作机构进行比对核 验，以核验您的身份。如您不同意本协议的任何内容，或者无法准确理解协议内容的解释，请 不要进行后续操作。',
              )}
            </span>
            <span>{leftPad('三、信息安全声明')}</span>
            <span>
              {leftPad(
                '本公司承诺对您的个人信息严格保密，并基于国家监管部门认可的加密算法进行数据加密 传输，数据加密存储。如因不可抗力、计算机黑客袭击、系统故障、通讯故障、电脑病毒、恶 意程序攻击及其他不可归因于本公司的情况而导致用户损失的，本公司不承担任何责任。本公 司与合作机构签订保密合同，并要求合作伙伴做好用户信息安全保障，承诺尽到信息安全保护 义务。',
              )}
            </span>
            <span>{leftPad('四、协议的效力及变更')}</span>
            <span>
              {leftPad(
                '本协议在签署后立即生效，您在本公司小程序/公众号点击“ 同意 ”或以其他方式选择接 受本协议规则，即视为对本协议的签订。在不损害用户利益的前提下，本公司保留修改或增补 本协议内容的权利，并以小程序/公众号公告的方式予以公布，无需另行单独通知您。若您在 本协议内容通知变更后继续办理相关业务的，表示您已充分阅读、理解并接受变更后的协议内 容，也将遵循变更后的协议内容办理相关业务。若您不同意变更后的协议内容，您应向本公司 提出终止本协议并停止办理相关业务。',
              )}
            </span>
            <span>{leftPad('五、纠纷的解决办法')}</span>
            <span>
              {leftPad(
                '因履行本协议或与本协议有关的任何争议，由协议签订各方协商解决；协商不成的，协议 各方经协商一致可向被告所在地具有管辖权的人民法院提起诉讼解决。',
              )}
            </span>
          </Space>
        </div>
        <Button
          block
          color={waiting ? undefined : 'primary'}
          shape="rounded"
          disabled={waiting}
          onClick={onClose}
        >
          <span>我已知晓</span>
          {waiting && <span>（{timer.countDown}）</span>}
        </Button>
      </Space>
    </Popup>
  );
};
