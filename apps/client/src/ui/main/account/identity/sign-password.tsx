import * as query from '@/queries/signPassword';
import { MoreOutline } from 'antd-mobile-icons';
import { useNavigate } from 'umi';
import { Active, Done, Loading } from './item';

export default () => {
  const navigate = useNavigate();

  const existsQuery = query.exists();
  const exists = existsQuery.data?.exists;

  const loading = existsQuery.isLoading;

  const Component = loading ? Loading : exists ? Done : Active;
  const text = exists ? '已申请' : '待申请';

  return (
    <Component
      icon={<MoreOutline className="text-white text-[1.5625rem]" />}
      onClick={() => navigate('/secondary/sign-password/view')}
      title="数字证书"
      text={text}
    />
  );
};
