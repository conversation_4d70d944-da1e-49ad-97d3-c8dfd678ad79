import * as userConfig from '@/queries/user/userConfig';
import { Space } from 'antd-mobile';
import Appropriateness from './appropriateness';
import IdentityVerification from './identity-verification';
import Info from './info';
import LoginMethod from './login-method';
import RiskLevel from './risk-level';
import SignPassword from './sign-password';
import TaxDeclaration from './tax-declaration';

export default () => {
  const { data, isLoading } = userConfig.own();
  const enableRiskLevel = !isLoading && !data?.disableRiskTest;

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
      <IdentityVerification />
      <Info />
      <Appropriateness />
      {enableRiskLevel && <RiskLevel />}
      <TaxDeclaration />
      <SignPassword />
      <LoginMethod />
    </Space>
  );
};
