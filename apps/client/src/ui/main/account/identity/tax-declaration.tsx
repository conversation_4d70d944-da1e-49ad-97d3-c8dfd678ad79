import { ReactComponent as Stamp } from '@/assets/stamp.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/appropriatenessProcess/taxDeclaration';
import { Active, Done, Loading } from './item';

export default () => {
  const taxDeclarationQuery = query.own();

  const loading = taxDeclarationQuery.isLoading;

  const complete = !!taxDeclarationQuery.data;

  const Component = loading ? Loading : complete ? Done : Active;
  const text = complete ? '已完善' : '待完善';

  return (
    <Component
      icon={<SvgWrapper component={Stamp} />}
      title="税收居民声明信息"
      text={<span className="text-[#747474]">{text}</span>}
    />
  );
};
