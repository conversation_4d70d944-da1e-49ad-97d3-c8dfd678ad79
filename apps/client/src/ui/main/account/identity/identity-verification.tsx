import { ReactComponent as Identity } from '@/assets/identity.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import { Loading } from 'antd-mobile';
import { Active, Done } from './item';

export default () => {
  const userQuery = account.user();

  const validated = !userQuery.data?.validateIdentityUponLogin;
  const loading = userQuery.isLoading;

  const Component = loading ? Loading : validated ? Done : Active;
  const text = validated ? '已认证' : '待认证';

  return (
    <Component
      icon={<SvgWrapper component={Identity} />}
      title="实名认证"
      text={<span className="text-[#747474]">{text}</span>}
    />
  );
};
