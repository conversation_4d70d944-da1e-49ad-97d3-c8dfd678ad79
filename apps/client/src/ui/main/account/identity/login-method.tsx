import * as account from '@/queries/account';
import { MoreOutline } from 'antd-mobile-icons';
import { useNavigate } from 'umi';
import { Active, Done, Loading } from './item';

export default () => {
  const navigate = useNavigate();

  const userQuery = account.user();
  const { data: user } = userQuery;

  const bind = !!user?.email && !!user?.phone;
  const partialBind = !bind && (!!user?.email || !!user?.phone);

  const loading = userQuery.isLoading;

  const Component = loading ? Loading : bind ? Done : Active;
  const text = bind ? '已设置' : partialBind ? '部分设置' : '未设置';

  return (
    <Component
      icon={<MoreOutline className="text-white text-[1.5625rem]" />}
      title="登录方式"
      text={text}
      onClick={() => navigate('/secondary/update-login/two-fa')}
    />
  );
};
