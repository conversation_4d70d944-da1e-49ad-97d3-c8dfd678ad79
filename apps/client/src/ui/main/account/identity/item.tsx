import { Badge, DotLoading } from 'antd-mobile';

type Props = {
  icon: React.ReactNode;
  title: React.ReactNode;
  text: React.ReactNode;
  onClick?: () => void;
  showBadge?: boolean;
};

export const Loading = (props: Props) => (
  <div
    className="rounded-full h-16 p-[0.375rem]"
    style={{ backgroundColor: 'var(--adm-color-box)' }}
    onClick={props.onClick}
  >
    <div className="h-full flex justify-between">
      <div
        className="h-full basis-7/12 rounded-full px-6"
        style={{ backgroundColor: 'var(--adm-color-light)' }}
      >
        <div className="flex items-center h-full">
          <span className="mr-4">{props.icon}</span>
          <span className="text-white text-sm break-all">{props.title}</span>
        </div>
      </div>
      <div className="pr-6 flex items-center">
        <DotLoading />
      </div>
    </div>
  </div>
);

export const Done = (props: Props) => (
  <div
    className="rounded-full h-16 p-[0.375rem]"
    style={{ backgroundColor: 'var(--adm-color-box)' }}
    onClick={props.onClick}
  >
    <div className="h-full flex justify-between">
      <div
        className="h-full basis-7/12 rounded-full px-6"
        style={{ backgroundColor: 'var(--adm-color-light)' }}
      >
        <div className="flex items-center h-full">
          <span className="mr-4">{props.icon}</span>
          <span className="text-white text-sm break-all">{props.title}</span>
        </div>
      </div>
      <div className="pr-6 flex items-center">
        <span
          className="text-[1rem] font-medium"
          style={{ color: 'var(--adm-color-primary)' }}
        >
          {props.text}
        </span>
      </div>
    </div>
  </div>
);

export const Active = (props: Props) => (
  <div
    className="rounded-full h-16 p-[0.375rem]"
    style={{ backgroundColor: 'var(--adm-color-box)' }}
    onClick={props.onClick}
  >
    <div className="h-full flex justify-between">
      <div className="pl-6 flex items-center">
        {!!props.showBadge && (
          <Badge className="mr-2" content={Badge.dot} />
        )}
        <span className="text-[1rem] font-medium text-[#747474]">
          {props.text}
        </span>
      </div>
      <div
        className="h-full basis-7/12 rounded-full px-6"
        style={{ backgroundColor: 'var(--adm-color-primary)' }}
      >
        <div className="flex items-center h-full">
          <span className="mr-4">{props.icon}</span>
          <span className="text-white text-sm break-all">{props.title}</span>
        </div>
      </div>
    </div>
  </div>
);
