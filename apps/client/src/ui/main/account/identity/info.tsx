import { ReactComponent as Info } from '@/assets/info.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as query from '@/queries/appropriatenessProcess/baseInfo';
import { useNavigate } from 'umi';
import { Active, Done, Loading } from './item';

const individualFields = [
  'nationality',
  'profession',
  'job',
  'landline',
  'postCode',
  'email',
  'residency',
] as const;

const organizationFields = [
  'businessRange',
  'registerAddress',
  'operationAddress',
  'registerAsset',
] as const;

export default () => {
  const navigate = useNavigate();
  const userQuery = account.user();
  const infoQuery = query.own();

  const loading = infoQuery.isLoading;

  const { data: user } = userQuery;
  const info = infoQuery.data?.item?.formData;

  let complete = true;

  if (user?.type === '个人') {
    if (individualFields.some((key) => !info?.[key])) {
      complete = false;
    }
  } else if (user?.type === '机构') {
    if (organizationFields.some((key) => !info?.[key])) {
      complete = false;
    }
  }

  const Component = loading ? Loading : complete ? Done : Active;
  const text = complete ? '已完善' : '待完善';

  return (
    <Component
      icon={<SvgWrapper component={Info} />}
      title="身份基本信息"
      text={text}
      onClick={() => navigate('/secondary/appropriateness/info')}
    />
  );
};
