import { ReactComponent as Cog } from '@/assets/cog.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import { useNavigate } from 'umi';
import { Active, Done } from './item';

export default () => {
  const navigate = useNavigate();
  const { data: user } = account.user();

  const Component = user?.classification ? Done : Active;
  const text = user?.classification || '待完善';

  return (
    <Component
      icon={<SvgWrapper component={Cog} />}
      title="合格投资者类型"
      text={text}
      onClick={() => navigate('/secondary/appropriateness')}
    />
  );
};
