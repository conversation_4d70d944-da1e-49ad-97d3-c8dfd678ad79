import { ReactComponent as RiskLevel } from '@/assets/riskLevel.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as appropriateness from '@/queries/appropriatenessProcess';
import * as riskTestProcess from '@/queries/riskTestProcess';
import { toastError } from '@/utils/antd';
import { useNavigate } from 'umi';
import { Active, Done, Loading } from './item';

export default () => {
  const navigate = useNavigate();

  const ownAppropriatenessQuery = appropriateness.own();
  const ownRiskTestQuery = riskTestProcess.own();
  const userQuery = account.user();

  const loading =
    ownAppropriatenessQuery.isLoading ||
    ownRiskTestQuery.isLoading ||
    userQuery.isLoading;

  const { data: user } = userQuery;
  const { data: riskTest } = ownRiskTestQuery;
  const { data } = ownAppropriatenessQuery;

  const { riskLevel } = user || {};

  const riskTestProcessActive = riskTest?.item?.status === 'active';
  const active = riskTestProcessActive || !riskLevel;

  const Component = loading ? Loading : active ? Active : Done;

  const text = riskTestProcessActive ? '进行中' : riskLevel || '待评估';

  const onClick = () => {
    const appropriatenessActive =
      !!data && (!data.item || data.item.status === 'active');

    if (!!appropriatenessActive) {
      toastError('请先完成适当性流程');
      navigate('/secondary/appropriateness');
      return;
    }

    if (riskTestProcessActive) {
      navigate('/secondary/risk-test');
    } else {
      navigate('/secondary/risk-test/landing');
    }
  };

  return (
    <Component
      icon={<SvgWrapper component={RiskLevel} />}
      title="风险承受能力评估"
      text={text}
      onClick={onClick}
      showBadge={riskTestProcessActive}
    />
  );
};
