import { ReactComponent as Exit } from '@/assets/exit.svg';
import { ReactComponent as Lock } from '@/assets/lock.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as account from '@/queries/account';
import * as auth from '@/queries/auth';
import * as bankCard from '@/queries/bankCard';
import { Avatar, Badge, Space } from 'antd-mobile';
import { AddOutline } from 'antd-mobile-icons';
import React from 'react';
import { useNavigate } from 'umi';
import AddBankCard, { addBankCard } from './add-bank-card';

const bankCardColors = [
  { backgroundColor: '#BD2121', color: '#ffffff' },
  { backgroundColor: '#02588B', color: '#ffffff' },
];

const secondaryBankCardColor = {
  backgroundColor: '#e6e6e6',
  color: 'var(--adm-color-text-secondary)',
};

export default () => {
  const navigate = useNavigate();
  const { data: user } = account.user();

  const listQuery = bankCard.list();
  const { data: bankCards } = listQuery;
  const amount = bankCards?.length;

  const logoutMutation = auth.logout();

  const containerHeight = amount ? 11 + amount * 3 : 13;

  return (
    <React.Fragment>
      <div className="relative" style={{ height: `${containerHeight}rem` }}>
        <div className="absolute inset-x-0 top-0 bg-[#333333] rounded-3xl h-52" />
        {bankCards
          ?.map((card, index) => {
            const { backgroundColor, color } =
              bankCardColors[index] || secondaryBankCardColor;

            return (
              <div
                key={index}
                style={{ top: `${index * 3 + 1}rem`, backgroundColor }}
                className="absolute inset-x-[0.375rem] rounded-3xl h-52 pt-40 shadow-xl"
              >
                <div
                  className="px-6 h-full flex justify-between items-center text-sm"
                  style={{ color }}
                >
                  <span>{card.bankName || ''}</span>
                  <span>{`**** ${card.bankNumber.slice(
                    card.bankNumber.length - 4,
                  )}`}</span>
                </div>
              </div>
            );
          })
          .reverse()}
        <div className="absolute inset-x-0 top-0 bg-[#4d4d4d] rounded-3xl h-44 p-1">
          <div className="border-dashed border-black border-[0.0625rem] h-full rounded-3xl">
            <Space
              block
              justify="between"
              align="center"
              style={{ '--gap-horizontal': '1rem' }}
              className="px-6 h-32"
            >
              <Space
                block
                align="center"
                style={{ '--gap-horizontal': '1rem' }}
              >
                <div>
                  <Avatar
                    src=""
                    style={{ '--size': '4rem' }}
                    className="rounded-full"
                  />
                </div>
                <span className="text-xl text-white font-medium">
                  {user?.name}
                </span>
              </Space>
              <Space align="center">
                <div
                  className="relative h-[1.8125rem] w-[1.8125rem] rounded-xl flex justify-center items-center"
                  style={{ backgroundColor: 'var(--adm-color-weak)' }}
                  onClick={() => navigate('/secondary/password')}
                >
                  <SvgWrapper component={Lock} />
                  {!!user?.passwordReset && (
                    <div className="absolute -top-2 right-0">
                      <Badge content={Badge.dot} />
                    </div>
                  )}
                </div>
                <div
                  className="relative h-[1.8125rem] w-[1.8125rem] rounded-xl flex justify-center items-center"
                  style={{ backgroundColor: 'var(--adm-color-weak)' }}
                  onClick={() => logoutMutation.trigger()}
                >
                  <SvgWrapper component={Exit} />
                </div>
              </Space>
            </Space>
          </div>
        </div>
        <div className="absolute inset-x-0 top-32 bg-[#4d4d4d] rounded-3xl h-12 p-[0.375rem] shadow-xl">
          <div className="px-6 h-full flex justify-between items-center text-sm text-white">
            <span>
              <span>银行卡</span>
              {!!amount && <span>（{amount}张）</span>}
            </span>
            <AddOutline onClick={() => addBankCard.show()} />
          </div>
        </div>
      </div>
      <AddBankCard />
    </React.Fragment>
  );
};
