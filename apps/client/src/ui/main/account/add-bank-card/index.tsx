import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/bankCard';
import { createPopup } from '@/utils/zustand';
import { Button, Divider, Form, Input, Popup } from 'antd-mobile';

export const addBankCard = createPopup();

export default () => {
  const [form] = Form.useForm();
  const { open } = addBankCard.use();

  const mutation = query.create();

  const onFinish = async () => {
    const { bankNumber } = form.getFieldsValue();
    await mutation.trigger({ bankNumber });
    addBankCard.hide();
  };

  return (
    <Popup
      visible={open}
      onClose={() => addBankCard.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: '19.625rem',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        padding: '0 2rem 2rem 2rem',
        overflowY: 'scroll',
        backgroundColor: '#fcfdfe',
      }}
    >
      <div
        onClick={() => addBankCard.hide()}
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      >
        <SvgWrapper component={DownArrow} />
      </div>
      <div className="text-lg font-medium">添加银行卡</div>
      <Divider className="my-2" />

      <Form
        form={form}
        mode="card"
        onFinish={onFinish}
        footer={
          <Button
            block
            color="primary"
            shape="rounded"
            type="submit"
            loading={mutation.isMutating}
          >
            提交
          </Button>
        }
      >
        <Form.Item
          name="bankNumber"
          label="银行卡号"
          rules={[{ required: true }]}
        >
          <Input clearable placeholder="请输入" />
        </Form.Item>
      </Form>
    </Popup>
  );
};
