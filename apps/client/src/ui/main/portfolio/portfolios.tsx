import { ReactComponent as RightArrow } from '@/assets/right-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/portfolio';
import { toCN, toPercent } from '@/utils/number';
import { usePagination } from '@/utils/pagination';
import { stringToDate } from '@portfolio-service/schema/utils';
import { InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React from 'react';
import type { RouterOutput } from 'server/typings';
import { useNavigate } from 'umi';
import { z } from 'zod';
import { filterAtom } from './filter-picker';
import { searchAtom } from './search';

const pageSize = 20;

export default () => {
  const search = searchAtom.use();
  const category = filterAtom.use();

  const { page, prevPages, useHelpers, nextPage } = usePagination({
    pageSize,
    resetDeps: [search, category],
  });

  const listQuery = query.list({ page, pageSize, category, portfolio: search });
  const { data } = listQuery;

  const { hasMore } = useHelpers(data);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <React.Fragment>
      <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
        {prevPages.map((page) => (
          <Page
            key={page}
            params={{ page, pageSize, category, portfolio: search }}
          />
        ))}
        {data?.items?.map((it, index) => (
          <ListItem key={index + prevPages.length} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['portfolio']['list']['items'][number];
}) => {
  const navigate = useNavigate();

  const netValueParsed = z.number().safeParse(data.netValue);
  const netValue = netValueParsed.success ? netValueParsed.data : undefined;

  const cumNetValueParsed = z.number().safeParse(data.cumNetValue);

  let cumNetValue: number | undefined = undefined;
  let diff: number | undefined = undefined;

  if (cumNetValueParsed.success) {
    cumNetValue = cumNetValueParsed.data;
    diff = cumNetValue - 1;
  }

  const dateParsed = stringToDate.safeParse(data.netValueDate);
  const date = dateParsed.success
    ? dayjs(dateParsed.data).format('YYYY-MM-DD')
    : 'N/A';

  return (
    <div
      className="bg-white shadow-custom py-6 pl-6 rounded-4xl flex"
      onClick={() => {
        const idParsed = z.number().safeParse(data.id);
        if (idParsed.success) {
          navigate(`/main/portfolio/${idParsed.data}`);
        }
      }}
    >
      <Space block direction="vertical" className="flex-1 mr-6">
        <span className="text-lg font-bold">{data.name}</span>
        <Space block style={{ '--gap-horizontal': '1.5rem' }}>
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '0.25rem' }}
          >
            <span
              className="text-lg font-bold"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              {toCN(netValue, 4) || '-'}
            </span>
            <span
              className="text-xs"
              style={{ color: 'var(--adm-color-text-secondary)' }}
            >
              最新净值（{date}）
            </span>
          </Space>
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '0.25rem' }}
          >
            <span
              className="text-lg font-bold"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              {toPercent(diff)}
            </span>
            <span
              className="text-xs"
              style={{ color: 'var(--adm-color-text-secondary)' }}
            >
              成立以来
            </span>
          </Space>
        </Space>
      </Space>
      <div className="basis-6 flex items-center">
        <SvgWrapper component={RightArrow} />
      </div>
    </div>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data } = query.list(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {data?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </Space>
  );
};
