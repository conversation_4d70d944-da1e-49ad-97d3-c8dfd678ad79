import { Button, Modal, Card, Checkbox } from "antd-mobile";
import React, { useEffect, useState } from "react";
import * as query from "@/queries/userFollowedProducts"
import * as userQuery from "@/queries/user"

// 策略类型定义
type Strategy = {
    id: string;
    name: string;
    products: {
        product: string;
        name: string;
    }[];
};


export default () => {
    const addProductsMutation = query.addProducts();

    const followedProductsQuery = query.getProducts()

    const userTagQuery = userQuery.getUserTag();

    const tagMessages = {
        "直销普通已过期": "识别到您的风险测评问卷已过期，请前往适当性认证界面重新测评,审核通过后方可查看产品业绩",
        "直销普通": "根据您风险识别能力和承受能力调查问卷结果，无法查看代表产品业绩",
        "代销": "识别到您没有进行适当性认证，通过适当性认证后方可查看产品业绩",
        "未持仓客户": "识别到您没有进行适当性认证，通过适当性认证后方可查看产品业绩"
    }

    const showModalTags = [
        "代销专业",
        "代销普通C5",
        "代销认证",
        "直销专业",
        "直销普通C5",
        "未持仓专业"
    ]

    const userTag = userTagQuery.data ? userTagQuery.data : [];

    const followedProductsData = followedProductsQuery.data ? followedProductsQuery.data : [];

    const [visible, setVisible] = useState<boolean>(false);
    const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
    const [followedProducts, setFollowedProducts] = useState<string[]>([]);

    const strategies: Strategy[] = [
        {
            id: '1',
            name: '对冲策略',
            products: [
                { product: '衍复中性三号私募证券投资基金', name: '完全对冲' },
                { product: '衍复天禄灵活对冲一号私募证券投资基金', name: '灵活对冲' },
            ]
        },
        {
            id: '2',
            name: '指增策略',
            products: [
                { product: '衍复300指增一号私募证券投资基金', name: '沪深300' },
                { product: '衍复中证全指指数增强一号私募证券投资基金', name: '中证全指' },
                { product: "衍复指增三号私募证券投资基金", name: "中证500" },
                { product: "衍复鲲鹏三号私募证券投资基金", name: "中证1000" },
                { product: "衍复小市值指数增强一号私募证券投资基金", name: "万得小市值概念" },
            ]
        },
    ];

    useEffect(() => {
        setFollowedProducts(followedProductsData);
    }, [followedProductsData])
    const handleConfirm = async () => {

        const selectedProductsData = strategies
            .flatMap(strategy => strategy.products)
            .filter(products => followedProducts.includes(products.product))
            .map(products => products.product);


        const res = await addProductsMutation.trigger(selectedProductsData)

        setFollowedProducts([...followedProducts, ...selectedProducts]);
        setSelectedProducts([]);
        setVisible(false);
    };

    const showModalCondition = userTag.some(tag => showModalTags.includes(tag));

    const matchedTag = userTag.find(tag => tag in tagMessages);
    const hintMessage = matchedTag ? tagMessages[matchedTag] : '';

    return (
        <>
            <Button color='primary' shape='rounded' onClick={() => setVisible(true)}>
                关注代表产品（已关注 {followedProducts.length}）
            </Button>

            <Modal
                visible={visible}
                title={showModalCondition ? "选择关注产品" : "不能进行关注"}
                content={
                    <>
                        {
                            showModalCondition ? <> {strategies.map(strategy => (
                                <Card key={strategy.id} title={strategy.name}>
                                    {strategy.products.map(product => (
                                        <div key={product.product} style={{
                                            padding: 8,
                                            margin: 4,
                                            border: selectedProducts.includes(product.product) ? '2px solid #1677ff' : '1px solid #eee',
                                            borderRadius: 8
                                        }}>
                                            <Checkbox
                                                checked={followedProducts.includes(product.product)}
                                                disabled={followedProducts.includes(product.product)}
                                                onChange={checked => {
                                                    if (checked) {
                                                        setFollowedProducts([...followedProducts, product.product]);
                                                    } else {
                                                        setFollowedProducts(followedProducts.filter(id => id !== product.product));
                                                    }
                                                }}
                                            >
                                                {product.name}
                                            </Checkbox>
                                        </div>
                                    ))}
                                </Card>
                            ))}
                                <Card title="已关注产品">
                                    {followedProducts.map(productName => {
                                        const product = strategies
                                            .flatMap(s => s.products)
                                            .find(p => p.product === productName);

                                        return (
                                            <div key={productName} style={{
                                                padding: 8,
                                                margin: 4,
                                                border: '1px solid #eee',
                                                borderRadius: 8,
                                                backgroundColor: '#f5f5f5'
                                            }}>
                                                <span>{product?.name}</span>
                                                <Button
                                                    size='mini'
                                                    color='danger'
                                                    onClick={() => {
                                                        setFollowedProducts(followedProducts.filter(id => id !== productName))
                                                    }}
                                                >
                                                    取消关注
                                                </Button>
                                            </div>
                                        );
                                    })}
                                </Card></> : hintMessage
                        }
                    </>
                }
                closeOnAction
                bodyStyle={{
                    padding: "12px",
                    width: "auto"
                }}
                onClose={() => {
                    setSelectedProducts([]);
                    setVisible(false);
                }}
                actions={showModalCondition ? [
                    {
                        key: 'cancel',
                        text: '取消',
                    },
                    {
                        key: 'confirm',
                        text: '确认选择',
                        primary: true,
                        onClick: handleConfirm
                    }
                ] : [
                    {
                        key: 'acknowledge',
                        text: '我知道了',
                        onClick: () => setVisible(false)
                    }
                ]}
            >
            </Modal>
        </>
    );
};
