import { ReactComponent as Search } from '@/assets/search.svg';
import SvgWrapper from '@/components/svgWrapper';
import { createAtom } from '@/utils/zustand';
import { Form, Input } from 'antd-mobile';
import { debounce } from 'lodash';
import styles from './index.less';

export const searchAtom = createAtom<string | undefined>(undefined);
const setSearchDebounced = debounce(
  (value: string) => searchAtom.store.setState(value),
  500,
);

export default () => (
  <Form
    className={styles['input-rounded']}
    mode="card"
    initialValues={{ search: searchAtom.store.getState() }}
  >
    <Form.Item name="search">
      <div className="flex items-center">
        <span className="mr-4">
          <SvgWrapper component={Search} />
        </span>
        <Input
          clearable
          placeholder="产品名称/代码"
          style={{ '--font-size': '0.875rem' }}
          onChange={(value) => setSearchDebounced(value)}
        />
      </div>
    </Form.Item>
  </Form>
);
