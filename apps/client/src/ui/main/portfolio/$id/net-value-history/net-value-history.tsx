import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as netValueQuery from '@/queries/netValue';
import { Divider, Grid, Space } from 'antd-mobile';
import React from 'react';
import { useParams } from 'umi';
import ListItem from './net-value-history-list-item';
import { netValueHistoryDialog } from './net-value-history-more';

export default () => {
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const listQuery = netValueQuery.listByPortfolioId({
    portfolioId: id,
    pageSize: 5,
  });
  const { data: netValues } = listQuery;

  const empty = !!netValues && !netValues.length;

  return (
    <div className="bg-white rounded-4xl shadow-custom px-6 pt-8 pb-4">
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <Grid columns={4} className="font-medium text-center text-lg">
          <Grid.Item>净值日期</Grid.Item>
          <Grid.Item>单位净值</Grid.Item>
          <Grid.Item>累计净值</Grid.Item>
          <Grid.Item>资产规模（亿元）</Grid.Item>
        </Grid>

        <div>
          {netValues?.map((it, index) => (
            <React.Fragment key={index}>
              <ListItem data={it} />
              <Divider className="my-2" />
            </React.Fragment>
          ))}
          {empty && (
            <Space block justify="center" className="pt-2">
              <span style={{ fontSize: 'var(--adm-font-size-main)' }}>
                暂无
              </span>
            </Space>
          )}
          {!empty && (
            <Space
              block
              justify="center"
              className="pt-2"
              onClick={() => netValueHistoryDialog.show()}
            >
              <SvgWrapper component={DownArrow} />
            </Space>
          )}
        </div>
      </Space>
    </div>
  );
};
