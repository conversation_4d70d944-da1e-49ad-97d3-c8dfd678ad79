import { listByPortfolioId } from '@/queries/netValue';
import { toCN } from '@/utils/number';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Grid } from 'antd-mobile';
import dayjs from 'dayjs';

type Data = Exclude<
  ReturnType<typeof listByPortfolioId>['data'],
  undefined
>[number];

export default ({
  data,
}: {
  data: Data;
}) => {
  const dateParsed = stringToDate.safeParse(data.date);
  const date = dateParsed.success
    ? dayjs(dateParsed.data).format('YYYY-MM-DD')
    : 'N/A';

  return (
    <Grid columns={4} className="text-center text-xs font-light">
      <Grid.Item>{date}</Grid.Item>
      <Grid.Item>{toCN(data.netValue, 4)}</Grid.Item>
      <Grid.Item>{toCN(data.cumNetValue, 4)}</Grid.Item>
      <Grid.Item>
        {toCN(data.assetNet ? data.assetNet / 100000000 : null, 4)}
      </Grid.Item>
    </Grid>
  );
};
