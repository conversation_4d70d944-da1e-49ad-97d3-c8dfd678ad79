import * as netValueQuery from '@/queries/netValue';
import { useCursorPagination } from '@/utils/pagination';
import { createPopup } from '@/utils/zustand';
import { Divider, Grid, InfiniteScroll, Popup, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { min } from 'lodash';
import React, { useState } from 'react';
import { useParams } from 'umi';
import ListItem from './net-value-history-list-item';

const pageSize = 10;

const NetValueHistory = () => {
  const [offset, setOffset] = useState<string>();

  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const { prevPages, useHelpers } = useCursorPagination<string | undefined>({
    firstOffset: undefined,
    resetDeps: [id],
  });

  const listQuery = netValueQuery.listByPortfolioId({
    portfolioId: id,
    offset,
    pageSize,
  });
  const { data: netValues } = listQuery;

  const { hasMore, nextPage } = useHelpers(netValues);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage((data) => {
      const minDate = min(data?.map((it) => it.date)?.filter(Boolean));
      if (!minDate) return;

      const date = dayjs(minDate).subtract(1, 'day');
      const offset = date.format('YYYY-MM-DD');

      setOffset(offset);
      return offset;
    });
  };

  return (
    <React.Fragment>
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <Grid columns={4} className="font-medium text-center text-lg">
          <Grid.Item>净值日期</Grid.Item>
          <Grid.Item>单位净值</Grid.Item>
          <Grid.Item>累计净值</Grid.Item>
          <Grid.Item>资产规模（亿元）</Grid.Item>
        </Grid>
        <div>
          {prevPages.map((page) => (
            <Page
              key={page || ''}
              params={{ portfolioId: id, offset: page, pageSize }}
            />
          ))}
          {netValues?.map((it, index) => (
            <ListItem key={prevPages.length + index} data={it} />
          ))}
        </div>
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const Page = ({ params }: { params: Partial<netValueQuery.ListParams> }) => {
  const { data: netValues } = netValueQuery.listByPortfolioId(params);

  return (
    <React.Fragment>
      {netValues?.map((it, index) => (
        <React.Fragment key={index}>
          <ListItem data={it} />
          <Divider className="my-2" />
        </React.Fragment>
      ))}
    </React.Fragment>
  );
};

export const netValueHistoryDialog = createPopup();

export default () => {
  const { open } = netValueHistoryDialog.use();

  return (
    <Popup
      visible={open}
      onClose={() => netValueHistoryDialog.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: '39.25rem',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        padding: '2rem',
        overflowY: 'scroll',
        backgroundColor: '#fcfdfe',
      }}
    >
      {open && <NetValueHistory />}
    </Popup>
  );
};
