import { ReactComponent as PeriodicReport } from '@/assets/periodic-report.svg';
import { ReactComponent as PortfolioInfo } from '@/assets/portfolio-info.svg';
import { ReactComponent as PortfolioReport } from '@/assets/portfolio-report.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as portfolioQuery from '@/queries/portfolio';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { Navigate, useNavigate, useParams } from 'umi';
import { reportDetailDialog } from './report-detail';

export default () => {
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;
  const navigate = useNavigate()
  const { data: portfolio } = portfolioQuery.byId({ id });

  const dateParsed = stringToDate.safeParse(portfolio?.publishDate);
  const date = dateParsed.success
    ? dayjs(dateParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <div className="flex justify-between">
      <div
        className="flex-1 h-28 bg-white rounded-3xl shadow-custom flex justify-center items-center mr-5"
        onClick={() => reportDetailDialog.show({ data: 'portfolio' })}
      >
        <Space direction="vertical" justify="center" align="center">
          <SvgWrapper component={PortfolioInfo} />
          <span
            className="font-medium break-keep"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            产品概况
          </span>
        </Space>
      </div>

      <div
        className="flex-1 h-28 bg-white rounded-3xl shadow-custom flex justify-center items-center mr-5"
        onClick={() => reportDetailDialog.show({ data: 'portfolioReport' })}
      >
        <Space direction="vertical" justify="center" align="center">
          <SvgWrapper component={PortfolioReport} />
          <span
            className="font-medium break-keep"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            产品公告
          </span>
        </Space>
      </div>

      <div
        className="flex-1 h-28 bg-white rounded-3xl shadow-custom flex justify-center items-center"
        onClick={() => {
          // navigate(`/main/newPeriodicReport/${id}`)
          reportDetailDialog.show({ data: 'periodicReport' })
        }}
      >
        <Space direction="vertical" justify="center" align="center">
          <SvgWrapper component={PeriodicReport} />

          <span
            className="font-medium break-keep"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            定期报告
          </span>
        </Space>
      </div>
    </div>
  );
};
