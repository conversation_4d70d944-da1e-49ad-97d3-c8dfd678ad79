import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import { createPopup } from '@/utils/zustand';
import { Popup } from 'antd-mobile';
import PeriodicReport from './tabs/periodicReport';
import Portfolio from './tabs/portfolio';
import PortfolioReport from './tabs/portfolioReport';

export const reportDetailDialog = createPopup<
  'portfolio' | 'portfolioReport' | 'periodicReport'
>();

export default () => {
  const { open, data } = reportDetailDialog.use();

  return (
    <Popup
      visible={open}
      onClose={() => reportDetailDialog.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: 'calc(50vh + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        onClick={() => reportDetailDialog.hide()}
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      >
        <SvgWrapper component={DownArrow} />
      </div>
      <div>
        {data === 'portfolio' && <Portfolio />}
        {data === 'portfolioReport' && <PortfolioReport />}
        {data === 'periodicReport' && <PeriodicReport />}
      </div>
    </Popup>
  );
};
