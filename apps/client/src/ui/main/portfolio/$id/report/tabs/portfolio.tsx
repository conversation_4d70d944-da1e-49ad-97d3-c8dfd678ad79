import * as portfolioQuery from '@/queries/portfolio';
import { Divider, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { useParams } from 'umi';

export default () => {
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const { data: portfolio } = portfolioQuery.byId({ id });
  const date = portfolio?.publishDate
    ? dayjs(portfolio.publishDate).format('YYYY-MM-DD')
    : undefined;

  const fundManageBegin = portfolio?.fundManageBegin
    ? dayjs(portfolio.fundManageBegin).format('YYYY-MM-DD')
    : undefined;

  const fundManageEnd = portfolio?.fundManageEnd
    ? dayjs(portfolio.fundManageEnd).format('YYYY-MM-DD')
    : undefined;

  const fundManageDuration = fundManageBegin
    ? `${fundManageBegin} ~ ${fundManageEnd || '至今'}`
    : undefined;

  return (
    <Space block direction="vertical">
      <span className="font-medium text-lg">产品概况</span>
      <Divider className="my-2" />

      <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
        <Item label="基金代码" value={portfolio?.no || '-'} />
        <Item label="基金全称" value={portfolio?.name || '-'} />
        <Item
          label="管理人"
          value={portfolio ? '上海衍复投资管理有限公司' : '-'}
        />
        <Item label="托管人" value={portfolio?.custodian || '-'} />
        <Item label="成立日期" value={date || '-'} />
        <Item label="投资经理" value={portfolio?.fundManager} />
        <Item label="任职日期" value={fundManageDuration || '-'} />
        <Item label="产品策略" value={portfolio?.portfolioStrategy || '-'} />
      </Space>
    </Space>
  );
};

type ItemProps = {
  label: React.ReactNode;
  value: React.ReactNode;
};

const Item = (props: ItemProps) => {
  return (
    <Space block justify="between">
      <span style={{ color: 'var(--adm-color-weak)' }}>{props.label}</span>
      <span style={{ color: 'var(--adm-color-text-secondary)' }}>
        {props.value}
      </span>
    </Space>
  );
};
