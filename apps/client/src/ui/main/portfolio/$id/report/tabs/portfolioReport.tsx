import { ReactComponent as RightArrow } from '@/assets/right-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/report/portfolioReport';
import { usePagination } from '@/utils/pagination';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Divider, InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { RouterOutput } from 'server/typings';
import { history, useParams } from 'umi';

const pageSize = 10;

export default () => {
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;

  const { nextPage, page, prevPages, useHelpers } = usePagination({
    pageSize,
    resetDeps: [id],
  });

  const listQuery = query.listByPortfolioId({
    page,
    pageSize,
    portfolioId: id,
  });
  const { data: reports } = listQuery;
  const { hasMore } = useHelpers(reports);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <Space block direction="vertical">
      <span className="font-medium text-lg">产品公告</span>
      <Divider className="my-2" />
      <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
        {prevPages.map((page) => (
          <Page key={page} params={{ page, pageSize, portfolioId: id }} />
        ))}
        {reports?.items?.map((it, index) => (
          <ListItem key={prevPages.length + index} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </Space>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['portfolioReportReadonly']['listByPortfolioId']['items'][number];
}) => {
  const dateParsed = stringToDate.safeParse(data.date);
  const date = dateParsed.success
    ? dayjs(dateParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <div
      className="bg-white rounded-4xl shadow-custom pl-6 py-6 flex"
      onClick={() => history.push(`/file-viewer?id=${data.fileUploadId}`)}
    >
      <Space
        block
        direction="vertical"
        className="flex-1 mr-6"
        style={{ '--gap-vertical': '0.75rem' }}
      >
        <span className="text-lg break-all">{data.name}</span>
        <Space block align="center" style={{ '--gap-horizontal': '1.25rem' }}>
          <div
            className="py-1 px-3 rounded-full text-white"
            style={{ backgroundColor: 'var(--adm-color-primary)' }}
          >
            运行公告
          </div>
          <span style={{ color: 'var(--adm-color-weak)' }}>{date || '-'}</span>
        </Space>
      </Space>
      <div className="basis-6 flex items-center">
        <SvgWrapper component={RightArrow} />
      </div>
    </div>
  );
};

const Page = ({ params }: { params: Partial<query.ListParmas> }) => {
  const { data: reports } = query.listByPortfolioId(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {reports?.items?.map((it) => (
        <ListItem data={it} />
      ))}
    </Space>
  );
};
