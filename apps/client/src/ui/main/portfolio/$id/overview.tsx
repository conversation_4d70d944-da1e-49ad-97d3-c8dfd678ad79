import * as netValueQuery from '@/queries/netValue';
import * as portfolioQuery from '@/queries/portfolio';
import { toCN, toPercent } from '@/utils/number';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Grid, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import { useParams } from 'umi';
import { z } from 'zod';
import { filterAtom } from '../filter-picker';
export default () => {
  const params = useParams();
  const id = params.id ? parseInt(params.id) : undefined;
  
  const category = filterAtom.use();
  
  const { data: portfolio } =category == "sub" ? portfolioQuery.byIdNoAccess({id}) : portfolioQuery.byId({ id });
  const { netValue, netValueDate, yearlyDiff, publishDiff } =
    useNetValueInfo(id);

  const diffElements: { title: string; element: React.ReactNode }[] = [];
  if (publishDiff != null) {
    diffElements.push({
      title: '成立以来',
      element: toPercent(publishDiff) || '-',
    });
  }

  if (yearlyDiff != null) {
    diffElements.push({
      title: '今年以来',
      element: toPercent(yearlyDiff) || '-',
    });
  }

  return (
    <div className="bg-white shadow-custom p-8 rounded-4xl">
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div className="flex gap-6">
          <span className="text-lg font-medium">{portfolio?.name}</span>
          <Space className="self-end text-xs" align="center">
            <span style={{ color: 'var(--adm-color-weak)' }}>
              {portfolio?.no}
            </span>

            {!!portfolio?.riskLevel && (
              <div
                className="h-5 w-5 rounded-full text-white flex justify-center items-center"
                style={{ backgroundColor: 'var(--adm-color-primary)' }}
              >
                {portfolio?.riskLevel}
              </div>
            )}
          </Space>
        </div>
        <Grid columns={diffElements.length + 1} className="text-center gap-y-2">
          {diffElements.map((it, index) => (
            <Grid.Item key={`element-${index}`}>
              <span
                className="text-xl font-bold"
                style={{ color: 'var(--adm-color-primary)' }}
              >
                {it.element}
              </span>
            </Grid.Item>
          ))}
          <Grid.Item>
            <span
              className="text-xl font-bold"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              {toCN(netValue, 4) || '-'}
            </span>
          </Grid.Item>
          {diffElements.map((it, index) => (
            <Grid.Item key={`key-${index}`}>
              <span style={{ color: 'var(--adm-color-text-secondary)' }}>
                {it.title}
              </span>
            </Grid.Item>
          ))}

          <Grid.Item>
            <div style={{ color: 'var(--adm-color-text-secondary)' }}>
              <div>最新净值</div>
              {!!netValueDate && <div>（{netValueDate}）</div>}
            </div>
          </Grid.Item>
        </Grid>
      </Space>
    </div>
  );
};

const useNetValueInfo = (portfolioId: number | undefined) => {
  const { data: netValueInfo } = netValueQuery.infoByPortfolioId(portfolioId);

  const [yearlyDiff, publishDiff] = useMemo(() => {
    if (!netValueInfo) {
      return [];
    }

    const cumNetValueParsed = z.number().safeParse(netValueInfo.cumNetValue);
    if (!cumNetValueParsed.success) {
      return [];
    }
    const cumNetValue = cumNetValueParsed.data;

    let yearlyDiff: number | undefined = undefined;
    const startOfYearNetValueParsed = z
      .number()
      .safeParse(netValueInfo.startOfYearNetValue);
    const startOfYearCumNetValueParsed = z
      .number()
      .safeParse(netValueInfo.startOfYearCumNetValue);

    if (
      startOfYearNetValueParsed.success &&
      startOfYearCumNetValueParsed.success
    ) {
      yearlyDiff =
        (cumNetValue - startOfYearCumNetValueParsed.data) /
        startOfYearNetValueParsed.data;
    }

    let publishDiff: number | undefined = undefined;
    const publishNetValueParsed = z
      .number()
      .safeParse(netValueInfo.publishNetValue);
    const publishCumNetValueParsed = z
      .number()
      .safeParse(netValueInfo.publishCumNetValue);

    if (publishNetValueParsed.success && publishCumNetValueParsed.success) {
      publishDiff =
        (cumNetValue - publishCumNetValueParsed.data) /
        publishNetValueParsed.data;
    }

    return [yearlyDiff, publishDiff];
  }, [netValueInfo]);

  let netValueDate: string | undefined = undefined;
  const netValueDateParsed = stringToDate.safeParse(netValueInfo?.netValueDate);
  if (netValueDateParsed.success) {
    netValueDate = dayjs(netValueDateParsed.data).format('YYYY-MM-DD');
  }

  const netValue = netValueInfo?.netValue;

  return { netValue, netValueDate, yearlyDiff, publishDiff };
};
