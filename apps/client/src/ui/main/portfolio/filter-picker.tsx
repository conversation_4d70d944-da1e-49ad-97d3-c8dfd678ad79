import { createAtom, createPopup } from '@/utils/zustand';
import {
  Category,
  categoryEntries,
  categorySchema,
} from '@portfolio-service/schema/portfolio';
import { Picker, PickerProps } from 'antd-mobile';

export const filterPicker = createPopup();
export const filterAtom = createAtom<Category | undefined>(undefined);

// const columns: PickerProps['columns'] = [
//   [{ label: '全部', value: '' }].concat(
//     categoryEntries.map(([value, label]) => ({ label, value })),
//   ),
// ];

const columns: PickerProps['columns'] = [
  categoryEntries.map(([value, label]) => ({ label, value })),
];

export default () => {
  const open = filterPicker.use((state) => state.open);

  const onConfirm = (value: string | number | null) => {
    const valueParsed = categorySchema.safeParse(value?.toString());
    filterAtom.store.setState(
      valueParsed.success ? valueParsed.data : undefined,
    );

    filterPicker.hide();
  };

  return (
    <Picker
      defaultValue={[filterAtom.store.getState() || '']}
      visible={open}
      columns={columns}
      onConfirm={([value]) => onConfirm(value)}
      onCancel={() => filterPicker.hide()}
    />
  );
};
