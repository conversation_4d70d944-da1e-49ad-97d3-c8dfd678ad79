import { categoryTransform } from '@portfolio-service/schema/portfolio';
import { DownOutline } from 'antd-mobile-icons';
import { filterAtom, filterPicker } from './filter-picker';

export default () => {
  const value = filterAtom.use();

  return (
    <div
      onClick={() => filterPicker.show()}
      className="rounded-full px-4 py-1 flex items-center"
      style={{ backgroundColor: 'var(--adm-color-background)' }}
    >
      <span className="mr-2">{value ? categoryTransform[value] : '筛选'}</span>
      <DownOutline />
    </div>
  );
};
