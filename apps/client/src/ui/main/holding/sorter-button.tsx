import { sortByTransform } from '@portfolio-service/schema/share';
import { DownOutline } from 'antd-mobile-icons';
import { sorterAtom, sorterPicker } from './sorter-picker';

export default () => {
  const value = sorterAtom.use();
  const text = sortByTransform[value]
    ?.slice()
    ?.replace('按', '')
    ?.replace('排序', '');

  return (
    <div
      className="bg-white rounded-full px-4 py-[0.375rem] flex items-center"
      onClick={() => sorterPicker.show()}
    >
      <span className="mr-2">{text}</span>
      <DownOutline />
    </div>
  );
};
