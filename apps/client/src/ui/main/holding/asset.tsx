import { ReactComponent as Asset } from '@/assets/asset.svg';
import { ReactComponent as CumEarning } from '@/assets/cum-earning.svg';
import { ReactComponent as CurrentEarning } from '@/assets/current-earning.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/ta';
import { toCNY, toSignedCNY } from '@/utils/number';
import { Divider, Modal, Space } from 'antd-mobile';
import { QuestionCircleFill } from 'antd-mobile-icons';

export default () => {
  const { data: totalAsset } = query.totalAsset();
  const { data: totalEarnings } = query.totalEarnings();

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="flex justify-between items-end px-6 text-white">
        <Space block direction="vertical">
          <span className="text-lg">总资产</span>
          <span className="text-2xl">{toCNY(totalAsset?.asset) || '-'}</span>
        </Space>
        <SvgWrapper component={Asset} />
      </div>

      <div className="bg-white rounded-4xl shadow-custom p-8">
        <div className="flex justify-between px-4">
          <Space block align="center" style={{ '--gap-horizontal': '2rem' }}>
            <SvgWrapper component={CurrentEarning} />
            <Space block direction="vertical" className="font-medium">
              <span>持有收益</span>
              <span
                className="text-lg leading-none"
                style={{ color: 'var(--adm-color-primary)' }}
              >
                {toSignedCNY(totalEarnings?.currentEarnings) || '-'}
              </span>
            </Space>
          </Space>
          <div
            className="flex justify-center items-center text-lg"
            onClick={() =>
              Modal.show({
                bodyStyle: {
                  borderRadius: '32px',
                  padding: '1.5rem 1.5rem 1rem 1.5rem',
                },
                closeOnAction: true,
                actions: [
                  {
                    key: 'ok',
                    text: '我知道了',
                    primary: true,
                    style: { borderRadius: '9999px' },
                  },
                ],
                title: (
                  <Space
                    block
                    direction="vertical"
                    style={{ '--gap-vertical': '1.5rem' }}
                  >
                    <div>
                      持有收益 = 持有份额资产净值 - 持有份额成本（不含认申购费）
                    </div>
                    <div>
                      统计期间投资者如发生非交易过户业务等，收益金额计算结果不准确。
                    </div>
                    <div>
                      页面展示的持有收益数据仅供投资者参考，不代表实际获得的收益。
                    </div>
                  </Space>
                ),
              })
            }
          >
            <QuestionCircleFill />
          </div>
        </div>

        {/* <Divider style={{ borderColor: '#c3c3c3' }} /> */}

        {/* <div className="flex justify-between px-4">
          <Space block align="center" style={{ '--gap-horizontal': '2rem' }}>
            <SvgWrapper component={CumEarning} />
            <Space block direction="vertical" className="font-medium">
              <span>累计收益</span>
              <span
                className="text-lg leading-none"
                style={{ color: 'var(--adm-color-primary)' }}
              >
                {toSignedCNY(totalEarnings?.cumulativeEarnings) || '-'}
              </span>
            </Space>
          </Space>
          <div
            className="flex justify-center items-center text-lg"
            onClick={() =>
              Modal.show({
                bodyStyle: {
                  borderRadius: '32px',
                  padding: '1.5rem 1.5rem 1rem 1.5rem',
                },
                closeOnAction: true,
                actions: [
                  {
                    key: 'ok',
                    text: '我知道了',
                    primary: true,
                    style: { borderRadius: '9999px' },
                  },
                ],
                title: (
                  <Space
                    block
                    direction="vertical"
                    style={{ '--gap-vertical': '1.5rem' }}
                  >
                    <div>
                      累计收益 = 期末日所持资产净值 + 期间净赎回款 +
                      现金分红金额 - 期初日所持资产净值 -
                      期间申购款（不含认申购费）
                    </div>
                    <div>
                      统计期间投资者如发生非交易过户业务等，收益金额计算结果不准确。
                    </div>
                    <div>
                      页面展示的累计收益数据仅供投资者参考，不代表实际获得的收益。
                    </div>
                  </Space>
                ),
              })
            }
          >
            <QuestionCircleFill />
          </div>
        </div> */}
      </div>
    </Space>
  );
};
