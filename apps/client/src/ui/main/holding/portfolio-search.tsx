import { createAtom } from '@/utils/zustand';
import { Input, InputProps } from 'antd-mobile';
import { debounce } from 'lodash';

export const searchAtom = createAtom<string | undefined>(undefined);
const setSearchDebounced = debounce(
  (value: string) => searchAtom.store.setState(value),
  500,
);

export default (props: InputProps) => {
  return (
    <Input
      {...props}
      defaultValue={searchAtom.store.getState()}
      placeholder="产品名称/代码"
      onChange={(value) => setSearchDebounced(value)}
    />
  );
};
