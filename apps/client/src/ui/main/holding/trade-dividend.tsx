import { ReactComponent as Dividend } from '@/assets/dividend.svg';
import { ReactComponent as Trade } from '@/assets/trade.svg';
import SvgWrapper from '@/components/svgWrapper';
import { Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  return (
    <div className="flex">
      <div
        className="bg-white shadow-custom rounded-3xl py-6 flex-1 mr-6"
        onClick={() => navigate('/secondary/ta/trade')}
      >
        <Space block direction="vertical" align="center">
          <SvgWrapper component={Trade} />
          <span
            className="text-xs"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            申赎记录
          </span>
        </Space>
      </div>

      <div
        className="bg-white shadow-custom rounded-3xl py-6 flex-1"
        onClick={() => navigate('/secondary/ta/dividend')}
      >
        <Space block direction="vertical" align="center">
          <SvgWrapper component={Dividend} />
          <span
            className="text-xs"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            分红记录
          </span>
        </Space>
      </div>
    </div>
  );
};
