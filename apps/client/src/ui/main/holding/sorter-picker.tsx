import { createAtom, createPopup } from '@/utils/zustand';
import {
  SortBy,
  sortByEntries,
  sortBySchema,
} from '@portfolio-service/schema/share';
import { Picker, PickerProps } from 'antd-mobile';

export const sorterPicker = createPopup();
export const sorterAtom = createAtom<SortBy>('currentEarnings');

const columns: PickerProps['columns'] = [
  sortByEntries.map(([value, label]) => ({ label, value })),
];

export default () => {
  const { open } = sorterPicker.use();

  const onConfirm = (value: string | number | null) => {
    const valueParsed = sortBySchema.safeParse(value?.toString());
    const result = valueParsed.success ? valueParsed.data : 'currentEarnings';

    sorterAtom.store.setState(result);
    sorterPicker.hide();
  };

  return (
    <Picker
      visible={open}
      columns={columns}
      onConfirm={([value]) => onConfirm(value)}
      onCancel={() => sorterPicker.hide()}
    />
  );
};
