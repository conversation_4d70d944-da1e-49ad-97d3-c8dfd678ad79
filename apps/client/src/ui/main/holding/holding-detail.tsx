import { ReactComponent as RightArrow } from '@/assets/right-arrow.svg';
import { ReactComponent as Search } from '@/assets/search.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/ta/share';
import { toCN, toSignedCNY } from '@/utils/number';
import { usePagination } from '@/utils/pagination';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Grid, InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { RouterOutput } from 'server/typings';
import { useNavigate } from 'umi';
import PortfolioSearch, { searchAtom } from './portfolio-search';
import SorterButton from './sorter-button';
import { sorterAtom } from './sorter-picker';

const pageSize = 5;

export default () => {
  const portfolio = searchAtom.use();
  const sortKey = sorterAtom.use();
  
  const { nextPage, page, prevPages, useHelpers } = usePagination({
    pageSize,
    resetDeps: [portfolio, sortKey],
  });

  const listQuery = query.list({ page, pageSize, portfolio, sortKey });
  const { data: shares } = listQuery;
  
  const { hasMore } = useHelpers(shares);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <div
      className="rounded-4xl px-2 pt-6"
      style={{ backgroundColor: 'var(--adm-color-box)' }}
    >
      <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
        <div className="flex px-4">
          <div className="flex-1 bg-white rounded-full px-4 py-[0.375rem] flex items-center mr-4">
            <span className="mr-2">
              <SvgWrapper component={Search} />
            </span>
            <PortfolioSearch style={{ '--font-size': '0.875rem' }} />
          </div>

          <SorterButton />
        </div>

        {prevPages.map((page) => (
          <Page key={page} params={{ page, pageSize, portfolio, sortKey }} />
        ))}
        {shares?.items?.map((it, index) => (
          <ListItem key={prevPages.length + index} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </div>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['share']['list']['items'][number];
}) => {
  const navigate = useNavigate();
  const netValueDateParsed = stringToDate.safeParse(data.netValueDate);
  const netValueDate = netValueDateParsed.success
    ? dayjs(netValueDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  const lastModifyParsed = stringToDate.safeParse(data.lastModify);
  const lastModify = lastModifyParsed.success
    ? dayjs(lastModifyParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <div
      className="bg-white rounded-4xl shadow-custom pl-6 py-6 flex"
      onClick={() => navigate(`/main/portfolio/${data.portfolioId}`)}
    >
      <Space
        block
        direction="vertical"
        style={{ '--gap': '1.5rem' }}
        className="flex-1 mr-6"
      >
        <span className="font-medium text-lg">{data.portfolioName}</span>
        <Space block justify="between" align="center">
          <span>持有收益</span>
          <span
            className="font-medium text-lg"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            {toSignedCNY(data?.currentEarnings) || '-'}
          </span>
        </Space>
        <Grid columns={2} gap={12}>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                净值日期
              </span>
              <span className="font-bold break-all">{netValueDate || '-'}</span>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                累计净值
              </span>
              <span className="font-bold break-all">
                {toCN(data.cumNetValue, 4) || ''}
              </span>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                份额日期
              </span>
              <span className="font-bold break-all">{lastModify || '-'}</span>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                持有份额
              </span>
              <span className="font-bold break-all">
                {toCN(data.realShares) || '-'}
              </span>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                持仓市值
              </span>
              <span className="font-bold break-all">
                {toCN(data.holding) || '-'}
              </span>
            </div>
          </Grid.Item>
          <Grid.Item>
            <div className="flex">
              <span
                className="mr-2 break-keep"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                基金净值
              </span>
              <div className="font-bold break-all">
                <span>{toCN(data.netValue, 4) || '-'}</span>
                <span> </span>
                <span style={{ color: 'var(--adm-color-weak)' }}>
                  ({netValueDate || '-'})
                </span>
              </div>
            </div>
          </Grid.Item>
        </Grid>
      </Space>
      <div className="basis-6 flex items-center">
        <SvgWrapper component={RightArrow} />
      </div>
    </div>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data: shares } = query.list(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
      {shares?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </Space>
  );
};
