import * as query from '@/queries/ta';
import { toCNY, toInt } from '@/utils/number';
import { Form, Button, Divider, Space, Picker, DatePicker, List } from 'antd-mobile';
import { useNavigate } from 'umi';
import dayjs, { Dayjs } from "dayjs";
import { tradeDateUtils, openDay as getOpenDay } from 'open-day';
import * as shareQuery from '@/queries/ta/share';
import * as tradeQuery from '@/queries/ta/trade';
import { useEffect, useMemo, useState } from 'react';
import moment, { Dayjs as Moment } from 'dayjs';
import { DownOutline } from 'antd-mobile-icons'
import { z } from 'zod';
type QueryParams = {
    productName: string | null;
    applyDate: string | null;
}

type TradeData = {
    agency: string;
    applyBalance: number;
    applyDate: string;
    applyShare: number;
    businessType: string;
    confBalance: number;
    confDate: string;
    confShare: number;
    confStatus: string;
    portfolioId: number;
    portfolioName: string;
    portfolioNo: string;
    transactionFee: number;
    transactionPrice: number;
    transferFee: number;
};

export const toSHow = (value: number | null | undefined): string | undefined => {
    const valueParsed = z.number().min(0).safeParse(value);
    if (!valueParsed.success) return;

    return valueParsed.data.toLocaleString('zh-CN', {
        style: 'decimal',
        minimumFractionDigits: 2,
        useGrouping: true
    });
};



export default () => {
    const navigate = useNavigate();
    const { data } = query.totalAsset();
    const userShareQuery = tradeQuery.userTradeData()
    const [validDates, setValidDates] = useState<Set<string>>(new Set());
    const getDividedDataByProductMuation = tradeQuery.getDividedDataByProductName()
    const mutation = tradeQuery.search()

    function listToDict<T, U>(data: T[], k: (x: T) => string, v: (x: T) => U): Record<string, U> {
        const result: Record<string, U> = {};
        if (data) {
            data.forEach(x => {
                result[k(x)] = v(x);
            });
        }
        return result;
    }

    const tradeDateQuery = tradeQuery.tradeDate();


    const dates = useMemo(() => {
        const list = tradeDateQuery.data
            ?.map(x => moment(x.date))
            .sort((a, b) => a.valueOf() - b.valueOf());

        const dict = listToDict(
            list || [],
            x => x.format('YYYYMMDD'),
            () => true
        );

        return { list, dict };
    }, [tradeDateQuery.data]);


    const utils = dates.list && tradeDateUtils(dates.list);




    // const dates = useCreation(() => {
    //     const list = tradeDateReq.data?.data?.map(x => moment(x.date)).sort((a, b) => a.valueOf() - b.valueOf());
    //     const dict = listToDict(
    //         list,
    //         x => x.format('YYYYMMDD'),
    //         () => true,
    //     );
    //     return { list, dict };
    // }, [tradeDateReq.data]);

    const [productVisible, setProductVisible] = useState<boolean>(false);

    const [timeSelectVisible, setTimeSelectVisible] = useState<boolean>(false);

    const [selectedDate, setSelectedDate] = useState<string | null>(null);

    const [openDayContent, setOpenDayContent] = useState<string | null>(null);

    const [selectedProduct, setSelectedProduct] = useState<string[]>([]);

    const [redemptionDates, setRedemptionDates] = useState<string[]>([]);

    const [resData, setResData] = useState<TradeData | null>(null)



    // const [selectedRedemptionDate, setSelectedRedemptionDate] = useState<Dayjs | null>(null);

    const shareData = userShareQuery.data ? userShareQuery.data : []

    const productColumns = [
        Array.from(new Set(shareData
            .map(item => item.portfolioName)
            .filter((name): name is string => Boolean(name)) // 类型守卫确保非空
        )).map(name => ({
            label: name,
            value: name
        }))
    ]


    const handleQuery = async () => {
        const params: QueryParams = {
            productName: selectedProduct[0],
            applyDate: selectedDate
        }
        const res = await mutation.trigger(params)
        setResData(res as TradeData)

    }


    return (
        <>
            <div style={{ backgroundColor: "rgb(255, 255, 255)", height: "100vh", marginTop: -20 }}>
                <Divider style={{
                    borderStyle: "hidden",
                    color: "#02588B",
                    fontSize: '16px',
                    fontFamily: 'PingFang SC',
                    fontWeight: 600,
                    lineHeight: '20px',
                    letterSpacing: 0,
                    textAlign: 'center',
                    paddingTop: 20
                }}>赎回计算器</Divider>
                <Form layout='horizontal' mode='card' style={{ '--border-inner': '0', '--border-bottom': '0', '--border-top': '0' }}>
                    <Form.Item label={<span style={{
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        letterSpacing: 0,
                        color: '#333333',
                        paddingLeft: "3%",
                        display: "block",
                        paddingTop: 5
                    }}>产品名称</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => setProductVisible(true)} style={{
                            width: "97%",
                            borderRadius: 29,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>


                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{
                                    paddingLeft: 10,
                                    width: 170,
                                    fontSize: 11,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    textAlign: 'left',
                                }}
                                >{selectedProduct[0] || '请选择'}</span>
                                <span style={{ paddingRight: 10 }}><DownOutline /></span>
                            </div>

                        </Button>
                        <Picker
                            columns={productColumns}
                            visible={productVisible}
                            onClose={() => setProductVisible(false)}
                            onConfirm={async (value) => {
                                const openDay = shareData.find(item => item.portfolioName === value[0])?.openDayContent;

                                const redemptionDates = await getDividedDataByProductMuation.trigger(value[0] as string)
                                setRedemptionDates(redemptionDates)
                                setSelectedProduct(value as string[])
                                setSelectedDate(null)
                                setProductVisible(false)
                                setResData(null)
                            }}
                        />
                    </Form.Item>
                    <Form.Item label={<span style={{
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        letterSpacing: 0,
                        color: '#333333',
                        paddingLeft: "3%",
                        display: "block",
                        paddingTop: 5
                    }}>赎回日</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => { setTimeSelectVisible(true) }} disabled={!selectedProduct.length || !redemptionDates.length} style={{
                            width: "97%",
                            borderRadius: 29,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>

                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{ paddingLeft: 10, fontSize: 11 }}>{selectedDate ? dayjs(selectedDate).format('YYYY-MM-DD') : '请选择'}</span>
                                <span style={{ paddingRight: 10 }}><DownOutline /></span>
                            </div>

                        </Button>
                        <Picker visible={timeSelectVisible} title="请选择时间" onConfirm={date => {
                            setSelectedDate(date[0] as string | null);
                            setTimeSelectVisible(false);
                        }}
                            columns={[
                                redemptionDates.map(date => ({
                                    label: dayjs(date).format('YYYY-MM-DD'),
                                    value: date
                                }))
                            ]}
                            onClose={() => setTimeSelectVisible(false)}
                        />
                    </Form.Item>
                </Form>

                <Button color='primary' fill='outline' block onClick={() => {
                    setSelectedProduct([])
                    setSelectedDate(null)
                    setResData(null)
                }} style={{
                    width: "87%",
                    borderRadius: 30,
                    margin: '0 auto',
                    display: 'block',
                    color: '#02588B',
                    fontWeight: 600,
                }}>
                    重置
                </Button>
                <Button color='primary' fill='solid' block
                    disabled={!selectedDate || !selectedProduct.length}
                    onClick={handleQuery}
                    style={{
                        width: "87%",
                        borderRadius: 30,
                        margin: '0 auto',
                        display: 'block',
                        marginTop: 23,
                        fontWeight: 600
                    }}
                >
                    查询
                </Button>

                <div
                    style={{
                        width: "87%",
                        // height: 60,
                        borderRadius: 20,
                        margin: '0 auto',
                        background: '#FFFFFF',
                        marginTop: 23,
                        marginBottom: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0px 4px 44px 5px rgba(9, 116, 179, 0.07)',
                    }}
                >
                    {
                        resData ?
                            (
                                <>
                                    <div style={{ width: "90%", height: "90%" }}>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>赎回份额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData.confShare}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>赎回开放日净值</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>
                                                {resData.transactionPrice}
                                            </div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>赎回费用</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData.transactionFee}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>业绩报酬费用</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData.transferFee}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>客户实际到手金额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData.confBalance}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>赎回类型</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData.businessType}</div>
                                        </div>
                                         <div style={{ color: '#02588B', fontSize: 11, marginTop: 15, fontWeight: 400, fontFamily: "PingFang SC", paddingBottom: 15 }}>
                                            注：本界面所展示的内容仅供投资者参考,不具法律约束力。<br />
                                            <span style={{ display: "block", marginTop: 5, marginLeft: 22, marginBottom: 3 }}>
                                                具体计算公式详见产品基金合同。
                                            </span>

                                        </div>
                                    </div>


                                </>
                            )
                            : (<>
                                <span
                                    style={{
                                        fontFamily: "PingFang SC",
                                        fontSize: 14,
                                        width: "187%",
                                        height: 60,
                                        borderRadius: 20,
                                        fontWeight: 1400,
                                        color: '#02588B',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        margin: '0 auto',
                                    }}
                                >
                                    暂无数据
                                </span>
                            </>)

                    }
                </div>

            </div>

        </>

    );
};
