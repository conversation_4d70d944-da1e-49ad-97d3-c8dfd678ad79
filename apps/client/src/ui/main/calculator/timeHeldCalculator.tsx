import * as query from '@/queries/ta';
import { toCNY, toInt } from '@/utils/number';
import { Button, Divider, Space, Form, DatePicker, Tag, Toast, List } from 'antd-mobile';
import { useState } from 'react';
import { useNavigate } from 'umi';
import { boolean } from 'zod';
import dayjs from 'dayjs';
import { DownOutline } from 'antd-mobile-icons'

export default () => {


    const [startTimeVisible, setStartTimeVisible] = useState<boolean>(false)
    const [endTimeVisible, setEndTimeVisible] = useState<boolean>(false)
    const [startDate, setStartDate] = useState<Date | null>(null)
    const [endDate, setEndDate] = useState<Date | null>(null)
    const [days, setDays] = useState<number | null>(null);
    const navigate = useNavigate();
    const { data } = query.totalAsset();
    const handleCalculate = () => {
        if (!startDate || !endDate) return;

        if (dayjs(endDate).isBefore(startDate)) {
            Toast.show({ content: '结束日期不能早于开始日期' });
            return
        }

        const daysDiff = dayjs(endDate).diff(startDate, 'day');

        setDays(daysDiff);
    }


    return (
        <>
            <div style={{ backgroundColor: "rgb(255, 255, 255)", height: "100vh", marginTop: -20 }}>
                <Divider style={{
                    borderStyle: "hidden",
                    color: "#02588B",
                    fontSize: '16px',
                    fontFamily: 'PingFang SC',
                    fontWeight: 600,
                    lineHeight: '20px',
                    letterSpacing: 0,
                    textAlign: 'center',
                    paddingTop: 20
                }}>基金持有时间计算器</Divider>
                <Form layout='horizontal' mode='card' style={{ '--border-inner': '0', '--border-bottom': '0', '--border-top': '0' }}>
                    <Form.Item label={<span style={{
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        letterSpacing: 0,
                        color: '#333333',
                        paddingLeft: "3%",
                        display: "block",
                        paddingTop: 5
                    }}>起始日期</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => setStartTimeVisible(true)} style={{
                            width: "97%",
                            borderRadius: 29,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>


                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{ paddingLeft: 10, fontSize: 11 }}>{startDate ? dayjs(startDate).format('YYYY-MM-DD') : '请选择'}</span>
                                <span style={{ paddingRight: 10 }}><DownOutline /></span>
                            </div>
                        </Button>
                        <DatePicker
                            visible={startTimeVisible}
                            onClose={() => setStartTimeVisible(false)}
                            onConfirm={(date) => {
                                setStartDate(date);
                                setStartTimeVisible(false);
                            }}
                        />
                    </Form.Item>
                    <Form.Item label={<span style={{
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        letterSpacing: 0,
                        color: '#333333',
                        paddingLeft: "3%",
                        display: "block",
                        paddingTop: 5
                    }}>中止日期</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => setEndTimeVisible(true)} style={{
                            width: "97%",
                            borderRadius: 29,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>

                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{ paddingLeft: 10, fontSize: 11 }}>{endDate ? dayjs(endDate).format('YYYY-MM-DD') : '请选择'}</span>
                                <span style={{ paddingRight: 10 }}><DownOutline /></span>
                            </div>
                        </Button>
                        <DatePicker
                            visible={endTimeVisible}
                            onClose={() => setEndTimeVisible(false)}
                            onConfirm={(date) => {
                                setEndDate(date);
                                setEndTimeVisible(false);
                            }}
                        // filter={
                        //     {
                        //         year: (year) => {
                        //             return year <= dayjs().year()
                        //         }
                        //     }
                        // }
                        />
                    </Form.Item>
                    {/* <Form.Item label='日期类型'>
                    <Tag round color='primary'> 自然日</Tag>
                </Form.Item>
                <Form.Item label='计算规则'>
                    <Tag round color='primary'> 算头不算尾</Tag>
                </Form.Item> */}
                </Form>

                <Button color='primary' fill='outline' block onClick={() => {
                    setStartDate(null)
                    setEndDate(null)
                    setDays(null)
                }} style={{
                    width: "87%",
                    borderRadius: 30,
                    margin: '0 auto',
                    display: 'block',
                    color: '#02588B',
                    fontWeight: 600,
                }}>
                    重置
                </Button>
                {/* <Divider /> */}
                <Button color='primary' fill='solid' block
                    disabled={!startDate || !endDate}
                    onClick={handleCalculate}
                    style={{
                        width: "87%",
                        borderRadius: 30,
                        margin: '0 auto',
                        display: 'block',
                        fontWeight: 600,
                        marginTop: 23
                    }}
                >
                    查询
                </Button>


                <div
                    style={{
                        width: "87%",
                        // height: 60,
                        borderRadius: 20,
                        margin: '0 auto',
                        background: '#FFFFFF',
                        marginTop: 23,
                        marginBottom: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0px 4px 44px 5px rgba(9, 116, 179, 0.07)',
                    }}
                >
                    {
                        days != null ?
                            (
                                <>
                                    <div style={{ width: "90%", height: "90%" }}>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>计算结果</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{days} 个自然日（{dayjs(startDate).format('YYYY-MM-DD')} 至 {dayjs(endDate).format('YYYY-MM-DD')}）</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15,marginBottom:15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>风险提示</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>
                                                本计算结果仅供参考
                                            </div>
                                        </div>
                                    </div>


                                </>
                            )
                            : (<>
                                <span
                                    style={{
                                        fontFamily: "PingFang SC",
                                        fontSize: 14,
                                        width: "187%",
                                        height: 60,
                                        borderRadius: 20,
                                        fontWeight: 1400,
                                        color: '#02588B',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        margin: '0 auto',
                                        boxShadow: '0px 4px 44px 5px rgba(9, 116, 179, 0.07)',
                                    }}
                                >
                                    暂无数据
                                </span>



                            </>)

                    }
                </div>

            </div>

        </>
    );
};
