import * as query from '@/queries/ta';
import { toCNY, toInt } from '@/utils/number';
import { Button, Divider, Form, Picker, Space, DatePicker, Radio, List } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { useNavigate } from 'umi';
import dayjs from 'dayjs';
import * as shareQuery from '@/queries/ta/share';
import * as portfolioQuery from '@/queries/portfolio';
import SvgWrapper from '@/components/svgWrapper';
import { ReactComponent as WorkingSVG } from '@/assets/正在施工方.svg';
import { Dayjs } from 'dayjs';
type StartDateType = {
    type: string;
    date?: string;
};

type calcProps = {
    selectedProductId: number;
    startDate: StartDateType;
    endDate: string;
    intervalType: "day" | "week";
    valueType: "accumulate" | "compound" | "unit";
    indexType: string
}

export interface CalcResult {
    /** 年化收益率 */
    annualizedReturn: number | null;
    /** 年化波动率 */
    annualizedStd: number | null;
    /** 卡玛比率 */
    kama: number | null;
    /** 最大回撤 */
    maxRetracement: number | null;
    /** 最大回撤持续天数 */
    maxRetracementDays: number | null;
    /** 最大回撤修复天数 */
    maxRetracementFixtureDays: number | null;
    /** 最大回撤修复区间 [开始日期, 结束日期] */
    maxRetracementFixtureInterval: [string, string] | null;
    /** 最大回撤发生区间 [开始日期, 结束日期] */
    maxRetracementInterval: [string, string] | null;
    /** 总收益率 */
    return: number | null;
    /** 夏普比率 */
    sharpe: number | null;
    /** 索提诺比率 */
    sortino: number | null;
}

export interface Portfolio {
    /** 产品ID */
    id: number;
    /** 产品名称 */
    name: string;
    /** A 类份额 ID */
    classAShareId: number | null;
    /** B 类份额 ID */
    classBShareId: number | null;
    /** C 类份额 ID */
    classCShareId: number | null;
    /** 托管人 */
    custodian: string;
    /** 产品编号 */
    no: string;
    /** 投资策略 */
    portfolioStrategy: string;
    /** 风险等级 */
    riskLevel: string;
    /** 产品类型 */
    type: string;
    /** 产品状态 */
    state: string;
    /** 管理开始日期 */
    fundManageBegin: string;
    /** 管理结束日期 */
    fundManageEnd: string | null;
    /** 基金经理 */
    fundManager: string;
    /** 备案日期 */
    publishDate: string;
    /** 建仓日期 */
    startDate: string;
    /** 投资日期 */
    investDate: string;
    /** 夏普比率无风险利率 */
    sharpeRf: number;
    /** 索提诺比率最低可接受回报率 */
    sortinoMar: number;
    /** 中证1000指增 */
    category?: string;
}

export default () => {
    const mutation = portfolioQuery.search()

    const navigate = useNavigate();
    const { data } = query.totalAsset();
    const [form] = Form.useForm()
    const [productVisible, setProductVisible] = useState<boolean>(false);
    const [selectedProductId, setSelectedProductId] = useState<number>()
    const selectData = portfolioQuery.getDataById(selectedProductId as number)
    const [pType, setPType] = useState<string>()
    const [indexType, setIndexType] = useState<string>()
    const [portfolio, setPortfolio] = useState<Portfolio>()
    useEffect(() => {
        if (selectData?.data?.type) {
            setPortfolio(selectData.data as Portfolio)

            setPType(selectData.data.type)
            if (!checkNeutral(selectData.data.type)) {
                setIndexType(getBenchmarkName(selectData.data.type))
            }
        }
    }, [selectData])

    const userShareQuery = shareQuery.userShareList()
    const shareData = userShareQuery.data ? userShareQuery.data : []
    const [dateType, setDateType] = useState<"establishDate" | "startDate" | "any">("establishDate");
    const [customDate, setCustomDate] = useState<string>();
    const [selectStartDateVisible, setSelectStartDateVisible] = useState<boolean>(false)

    const [selectEndDateVisible, setSelectEndDateVisible] = useState<boolean>(false)
    const [intervalTypeVisible, setIntervalTypeVisible] = useState<boolean>(false)
    const [intervalType, setIntervalType] = useState<"day" | "week">()
    const [resData, serResData] = useState<CalcResult>()
    const [valueTypeVisible, setValueTypeVisible] = useState<boolean>(false)
    const [endDate, setEndDate] = useState<string>()
    const [valueType, setValueType] = useState<"accumulate" | "compound" | "unit">()
    const productColumns = [
        Array.from(new Set(shareData
            .filter(item => item.portfolioName && item.portfolioId)
            .map(item => ({
                name: item.portfolioName,
                id: item.portfolioId
            }))))
            .map(({ name, id }) => ({
                label: name as string,
                value: id as number
            }))
    ]
    const productMap = new Map(
        shareData.map(item => [item.portfolioId, item.portfolioName])
    );
    function checkNeutral(type: string) {
        return !(
            type === '指增' ||
            type === '沪深300指增' ||
            type === '中证500指增' ||
            type === '中证1000指增' ||
            type === '小市值指增' ||
            type === '中证A500指增' ||
            type === '中证全指'
        );
    }

    function getBenchmarkName(type: string) {
        if (type == '沪深300指增') {
            return '沪深300';
        }
        if (type === '中证500指增') {
            return '中证500';
        }
        if (type == '中证1000指增') {
            return '中证1000';
        }
        if (type == '小市值指增') {
            return '小市值指数';
        }
        if (type == '中证全指') {
            return '中证全指';
        }
        if (type == '中证A500指增') {
            return '中证A500';
        }
        return '中证500';
    }
    const handleQuery = async () => {
        const params: calcProps = {
            selectedProductId: selectedProductId as number,
            startDate: { type: dateType, date: customDate },
            endDate: endDate as string,
            intervalType: intervalType as "day" | "week",
            valueType: valueType as "accumulate" | "compound" | "unit",
            indexType: indexType as string
        }
        const res = await mutation.trigger(params)
        serResData(res as CalcResult)
    }


    return (
        <>
            {/* <Divider>产品计算器</Divider>
            <Form layout='horizontal' mode='card' form={form}>
                <Form.Header>参数输入区</Form.Header>
                <Form.Item label='产品名称'
                >
                    <Button onClick={() => setProductVisible(true)}>
                        {selectedProductId ? productMap.get(selectedProductId) : '请选择'}
                    </Button>
                    <Picker
                        columns={productColumns}
                        visible={productVisible}
                        onClose={() => setProductVisible(false)}
                        onConfirm={(valueArr) => {
                            const value = valueArr[0] as number;
                            setSelectedProductId(value)
                            setProductVisible(false)
                        }}
                    />
                </Form.Item>
                <Form.Item label='开始时间' name="startDate">
                    <Radio.Group
                        value={dateType}
                        onChange={(type: any) => {
                            setDateType(type);
                            if (type != "any") {
                                setCustomDate('')
                            }
                        }}
                    >
                        <Space direction="vertical">
                            <Radio value="establishDate">产品成立日</Radio>
                            <Radio value="startDate">建仓日</Radio>
                            <Radio value="any">
                                {dateType === 'any' ? (
                                    <Button onClick={() => setSelectStartDateVisible(true)}>{customDate ? customDate : '请选择日期'}</Button>
                                ) : "请选择日期"}
                                <DatePicker
                                    visible={selectStartDateVisible}
                                    onClose={() => setSelectStartDateVisible(false)}
                                    onConfirm={(val: Date) => {
                                        const day = dayjs(val).format("YYYY-MM-DD");
                                        setSelectStartDateVisible(false);
                                        setCustomDate(day);
                                    }}
                                >
                                </DatePicker>
                            </Radio>
                        </Space>
                    </Radio.Group>
                </Form.Item>
                <Form.Item label="结束时间" name="endDate">
                    <Button onClick={() => {
                        setSelectEndDateVisible(true)
                    }}>
                        {endDate ? endDate : "请选择日期"}
                    </Button>
                    <DatePicker
                        visible={selectEndDateVisible}
                        onClose={() => setSelectEndDateVisible(false)}
                        onConfirm={(val: Date) => {

                            const day = dayjs(val).format('YYYY-MM-DD');
                            setEndDate(day)
                            setSelectEndDateVisible(false);

                        }}></DatePicker>
                </Form.Item>
                <Form.Item label="净值频率" name="intervalType">
                    <Button onClick={() => { setIntervalTypeVisible(true) }}>
                        {intervalType ? { day: '日', week: '周' }[intervalType] : "请选择"}
                    </Button>
                    <Picker
                        columns={[[{ label: '日', value: 'day' }, { label: '周', value: 'week' }]]}
                        visible={intervalTypeVisible}
                        onClose={() => setIntervalTypeVisible(false)}
                        onConfirm={(valueArr) => {
                            const value = valueArr[0] as "day" | "week";
                            setIntervalType(value);
                            setIntervalTypeVisible(false)
                        }}
                    />
                </Form.Item>
                <Form.Item label="净值分类" name="valueType">
                    <Button onClick={() => { setValueTypeVisible(true) }}>
                        {valueType ?
                            {
                                unit: '单位净值',
                                accumulate: '累计净值',
                                compound: '复权净值'
                            }[valueType] : "请选择"}
                    </Button>
                    <Picker
                        columns={[[{ label: '单位净值', value: 'unit' }, { label: '累计净值', value: 'accumulate' }, { label: "复权净值", value: "compound" }]]}
                        visible={valueTypeVisible}
                        onClose={() => setValueTypeVisible(false)}
                        onConfirm={(valueArr) => {
                            const value = valueArr[0] as "unit" | "accumulate" | "compound";
                            setValueType(value);
                            setValueTypeVisible(false)
                        }}
                    />
                </Form.Item>
                {
                    !checkNeutral(pType as string) && (
                        <Form.Item label="对标指数">
                            {getBenchmarkName(pType as string)}
                        </Form.Item>
                    )
                }
            </Form>
            <Button block color='primary' size='large' onClick={handleQuery}>
                查询
            </Button> */}

            {/* <List header='结果展示区'>
                {
                    resData ? (
                        <> <List.Item>产品名称:{portfolio?.name}</List.Item>
                            <List.Item>成立日:{dayjs(portfolio?.publishDate).format("YYYY-MM-DD")}</List.Item>
                            <List.Item>建仓日：{dayjs(portfolio?.startDate).format("YYYY-MM-DD")}</List.Item>
                            <List.Item>策略类型:{portfolio?.type}</List.Item>
                            <List.Item>统计区间:</List.Item>
                            <List.Item>期间产品收益率:</List.Item>
                            <List.Item>期间产品超额收益率：</List.Item>
                            <List.Item>期间产品年化收益率</List.Item>
                            <List.Item>期间产品年化超额收益率:</List.Item>
                            <List.Item>期间产品波动率：</List.Item>
                            <List.Item>期间产品年化波动率:</List.Item>
                            <List.Item>期间指数收益率:</List.Item>
                            <List.Item>期间指数年化收益率:</List.Item>
                            <List.Item>夏普比率：</List.Item>
                            <List.Item>最大回撤:</List.Item>
                            <List.Item>超额最大回撤:</List.Item>
                            <List.Item>期间超额月胜率：</List.Item>
                            <List.Item>期间超额周胜率:</List.Item>
                        </>
                    ) : (<>
                        <Button block size='large'>
                            暂无数据
                        </Button>

                    </>)
                }

            </List> */}
            <div style={{
                width: "100%",
                height: "100vh",
                backgroundColor: '#FFFFFF'
            }}>
                <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '80%',
                    backgroundColor: '#FFFFFF',
                }}>

                    <img
                        src={require('@/assets/work.jpg')}
                        style={{
                            width: '60%',
                        }}
                        alt="施工中"
                    />
                    <div style={{
                       display:"flex",
                       justifyContent:"space-between",
                       width:"35%"
                    }}>
                        <div style={{ fontFamily: "PingFang SC",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "#02588B",
                        textAlign: 'center',}}>正在开发</div><div style={{ fontFamily: "PingFang SC",
                        fontWeight: 500,
                        fontSize: 16,
                        color: "#02588B",
                        textAlign: 'center',}}>敬请期待</div>
                    </div>
                </div>
            </div>

        </>
    );
};
