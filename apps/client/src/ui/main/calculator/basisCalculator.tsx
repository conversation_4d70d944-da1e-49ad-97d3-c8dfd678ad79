import * as query from '@/queries/ta';
import { toCNY, toInt } from '@/utils/number';
import { Button, Space, List, Grid, Collapse, Picker, Divider, Popup } from 'antd-mobile';
import { useNavigate } from 'umi';
import * as aIndexQuery from '@/queries/aIndex';
import * as aIndexFuture from "@/queries/aIndexFutures"
import * as tradeDateQuery from '@/queries/ta/trade'
import * as userQuery from "@/queries/user/userConfig"
import * as accountQuery from "@/queries/account"
import * as echarts from 'echarts';
import { ReactComponent as returnSVG } from '@/assets/返回.svg';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { SWRResponse } from 'node_modules/swr/dist/_internal';
import { DownOutline } from 'antd-mobile-icons'
import SvgWrapper from '@/components/svgWrapper';
import { symbol } from 'zod';
const INDEX_NAME_MAP: Record<string, string> = {
    '399905.SZ': '中证500',
    '000852.SH': '中证1000',
    '000016.SH': '上证50',
    '000300.SH': '沪深300'
};

const indexMap: Record<string, string> = {
    IC: '中证500',
    IH: '上证50',
    IF: '沪深300',
    IM: '中证1000'
};



// const ALLOWED_NAMES = new Set([
//     '史正华', '吕东煜', '周紫怡', '姚瑶', '李英豪',
//     '秦梦妍', '胥烨', '许亦舒', '陈子莹', '顾王琴',
//     '魏征', '黄思敏頔', "市场部测试"
// ]);

const AVERAGE_COLUMNS = [
    { title: "类型", key: "type", width: 70 },
    { title: "平均基差", key: "value", width: 70 }
];

<style>
    {`.fixed-collapse .adm-collapse-content-box { 
  overflow: visible !important; 
  min-width: 100vw;
}`}
</style>
type ProcessedFuturesData = {
    OBJECT_ID?: string;
    S_INFO_WINDCODE: string | null;
    TRADE_DT: string | null;
    S_DQ_CLOSE: string | null;
    S_DQ_CHANGE: string | null;
    S_DQ_OI: string | null;
    delistDate: string;
    basis: number;
    annualizedBasis: number;
    remainingDays: number;
    priceChangeRate: number;
    // 新增图表需要字段
    xAxisData?: string[];
    seriesData?: Array<{
        name: string;
        type: string;
        data: number[];
    }>;
};

const cellStyle = (width: number) => ({
    // flex: ` ${width}`,
    width: width,
    // padding: '0 8px',
    fontSize: 12,
    lineHeight: '10px',
    display: 'flex',
    // alignItems: 'left',
    // overflow: 'hidden',
    // textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
    fontFamily: 'PingFang SC',
});

function CommonTable<T extends { OBJECT_ID?: string } | Record<string, any>>({
    data,
    columns,
    headerTitle
}: {
    data: T[],
    columns: { title: string, key: keyof T | string, width: number }[],
    headerTitle?: string
}) {
    return (
        <div style={{ width: "100%", overflowX: "auto" }}>
            {/* {headerTitle && (
                <div style={{ padding: '8px 12px', fontSize: 16, fontWeight: 600, backgroundColor: '#f5f5f5' }}>
                    {headerTitle}
                </div>
            )} */}
            <div style={{
                display: 'flex',
                // padding: '12px 8px',
                background: '#FFFFFF',
                // borderBottom: '1px solid #eee',
                // minWidth: columns.reduce((a, b) => a + b.width, 0),
            }}>
                {columns.map(col => (
                    <div key={col.key.toString()} style={{ color: "#999999", ...cellStyle(col.width), fontSize: 12, paddingLeft: 0, paddingBottom: 5 }}>
                        {col.title}
                    </div>
                ))}
            </div>

            <div>
                {data?.length ? (
                    data.map((item) => (
                        <div
                            key={item.OBJECT_ID}
                            style={{
                                display: 'flex',
                                padding: '12px 4px',
                                paddingLeft: 0
                                // borderBottom: '1px solid #f0f0f0',
                            }}>
                            {columns.map(col => (
                                <div
                                    key={col.key.toString()}
                                    style={{
                                        ...cellStyle(col.width),
                                        // width: "20%",
                                        // marginLeft: 10,

                                        color: "rgba(102, 102, 102, 1)",
                                        fontSize: 12
                                    }}>
                                    {col.key === 'S_DQ_PCTCHANGE' ?
                                        `${(Number(item[col.key as keyof T]).toFixed(2))}%` :
                                        (['basis', 'annualizedBasis', 'priceChangeRate', "S_DQ_CLOSE"].includes(col.key.toString()) ?
                                            `${(Number(item[col.key])).toFixed(2)}${col.key == 'priceChangeRate' ? '%' : ''}` :
                                            item[col.key])
                                    }
                                </div>
                            ))}
                        </div>
                    ))
                ) : (
                    <div style={{
                        padding: '20px',
                        textAlign: 'center',
                        color: '#999',
                        width: "100%",
                        backgroundColor: "red",
                        height: 300
                    }}>
                        暂无数据
                    </div>

                )}
            </div>


            {headerTitle?.includes("平均") && (  // 仅在平均值表格显示
                <div style={{
                    padding: '8px 12px',
                    fontSize: 12,
                    color: 'rgba(2, 88, 139, 1)',
                    background: '#FFFFFF',
                    paddingLeft: 0
                }}>
                    注:该平均值非真实产品基差数据，仅供参考
                </div>
            )}
            {/* </List> */}
        </div>
    );
}

function NewCommonTable<T extends { OBJECT_ID?: string } | Record<string, any>>({
    data,
    columns,
    headerTitle
}: {
    data: T[];
    columns: { title: string; key: keyof T | string; width: number }[];
    headerTitle?: string;
}) {
    return (
        <div style={{
            width: "100%",
            overflowX: "auto",
            backgroundColor: 'rgba(255, 255, 255, 1)',
            WebkitOverflowScrolling: 'touch',
            margin: '16px 0'
        }}>
            {headerTitle && (
                <div style={{
                    padding: '16px',
                    fontSize: 11,
                    fontWeight: 600,
                    color: 'rgba(153, 153, 153, 1)',
                }}>
                    {headerTitle}
                </div>
            )}

            {/* 表格容器 - 使用table布局 */}
            <div style={{
                display: 'table',
                width: '100%',
                borderCollapse: 'collapse'
            }}>
                {/* 表头行 - 使用table-row */}
                <div style={{ display: 'table-row', background: 'rgba(255, 255, 255, 1)' }}>
                    {columns.map(col => (
                        <div
                            key={col.key.toString()}
                            style={{
                                display: 'table-cell',
                                padding: '12px 16px',
                                whiteSpace: 'nowrap',
                                textAlign: 'center',
                                verticalAlign: 'middle', // 垂直居中
                                // borderBottom: '1px solid #f0f0f0'
                                fontSize: 11,
                                fontWeight: 600,
                                color: 'rgba(153, 153, 153, 1)',
                            }}
                        >
                            {col.title}
                        </div>
                    ))}
                </div>

                {/* 数据行 */}
                {!!data?.length && (
                    data.map((item, index) => (
                        <div
                            key={item.OBJECT_ID || index}
                            style={{
                                display: 'table-row',
                                backgroundColor: "rgba(255, 255, 255, 1)",
                            }}
                        >
                            {columns.map(col => (
                                <div
                                    key={col.key.toString()}
                                    style={{
                                        display: 'table-cell',
                                        padding: '10px 16px',
                                        textAlign: 'center',
                                        verticalAlign: 'middle', // 垂直居中
                                        color: 'rgba(102, 102, 102, 1)',
                                        fontSize: 14,
                                        whiteSpace: "nowrap",
                                        fontWeight: 400,
                                        ...(col.key === 'S_DQ_PRECLOSE' ? { fontFamily: 'monospace' } : {})
                                    }}
                                >
                                    {col.key === 'S_DQ_PCTCHANGE' ? (
                                        <span style={{
                                            color: "rgba(102, 102, 102, 1)",
                                            fontSize: 14,
                                            fontWeight: 400,
                                            whiteSpace: 'nowrap',
                                        }}>
                                            {Number(item[col.key as keyof T]).toFixed(2)}%
                                        </span>
                                    ) : (
                                        ['basis', 'annualizedBasis', 'priceChangeRate', 'S_DQ_CLOSE',].includes(col.key.toString()) ? (
                                            <span style={{
                                                color: "rgba(102, 102, 102, 1)",
                                                fontSize: 14,
                                                fontWeight: 400,
                                                whiteSpace: 'nowrap',
                                            }}>
                                                {Number(item[col.key]).toFixed(2)}
                                                {['annualizedBasis', 'priceChangeRate'].includes(col.key.toString()) ? '%' : ''}
                                            </span>
                                        ) : (
                                            <span style={{
                                                color: "rgba(102, 102, 102, 1)",
                                                fontSize: 14,
                                                fontWeight: 400,
                                                whiteSpace: 'nowrap',
                                            }}>{item[col.key]}</span>
                                        )
                                    )}
                                </div>
                            ))}
                        </div>
                    ))
                )}
            </div>

            {!data?.length && <div style={{
                display: 'flex',
                justifyContent: "center",
                alignItems: "center",
                height: 130,
                width: "100%",
            }}>
                <span style={{ color: '#999' }}>暂无数据</span>

            </div>}
        </div>
    );
}


type ColumnType<T> = {
    title: string;
    key: keyof T | string;
    width: number;
    render?: (value: any, record: T) => React.ReactNode;
};

type CommTableProps<T extends Record<string, any>> = {
    data: T[];
    columns: ColumnType<T>[];
    headerTitle?: string;
    className?: string;
};

const NewCommTable = <T extends Record<string, any>>({
    data,
    columns,
    headerTitle,
    className
}: CommTableProps<T>) => {
    // 计算表格最小宽度
    const tableMinWidth = columns.reduce((acc, col) => acc + col.width, 0);

    return (
        <div
            className={className}
            style={{
                width: '100%',
                overflowX: 'auto',
                WebkitOverflowScrolling: 'touch'
            }}
        >
            <div style={{
                minWidth: tableMinWidth,
                position: 'relative'
            }}>
                {/* 表头 */}
                <div style={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${columns.length}, 1fr)`,
                    backgroundColor: '#fafafa',
                    borderBottom: '1px solid #f0f0f0'
                }}>
                    {columns.map(col => {


                        return (
                            <div
                                key={col.key.toString()}
                                style={{
                                    padding: '12px 8px',
                                    fontSize: 14,
                                    color: '#999999',
                                    whiteSpace: 'nowrap',
                                    fontFamily: 'PingFang SC',
                                    fontWeight: 500
                                }}
                            >
                                {col.title}
                            </div>
                        )
                    }

                    )}
                </div>

                {/* 表格内容 */}
                <div style={{
                    display: 'grid',
                    gridTemplateRows: `repeat(${data.length}, auto)`
                }}>
                    {data.map((item, index) => {

                        return (
                            <div
                                key={item.OBJECT_ID || index}
                                style={{
                                    display: 'grid',
                                    gridTemplateColumns: `repeat(${columns.length}, 1fr)`,
                                    borderBottom: '1px solid #f0f0f0',
                                }}
                            >
                                {columns.map(col => (
                                    <div
                                        key={col.key.toString()}
                                        style={{
                                            padding: '12px 8px',
                                            fontSize: 14,
                                            color: '#666666',
                                            whiteSpace: 'nowrap',
                                            fontFamily: 'PingFang SC'
                                        }}
                                    >
                                        {col.render
                                            ? col.render(item[col.key], item)
                                            : item[col.key]}
                                    </div>
                                ))}
                            </div>
                        )
                    }


                    )}
                </div>
            </div>
        </div>
    );
};

// function CommonTable<T extends { OBJECT_ID?: string } | Record<string, any>>({
//     data,
//     columns,
//     headerTitle
// }: {
//     data: T[],
//     columns: { title: string, key: keyof T | string, width: number }[],
//     headerTitle?: string
// }) {
//     // 计算最小宽度
//     const tableWidth = columns.reduce((acc, col) => acc + (col.width || 100), 0);

//     return (
//         <div style={{
//             width: '100%',
//             overflowX: 'auto',
//             border: '1px solid #f0f0f0',
//             borderRadius: 4,
//             WebkitOverflowScrolling: 'touch', 
//         }}>
//             <div style={{
//                 minWidth: tableWidth,
//                 position: 'relative'
//             }}>
//                 {/* 表头 */}
//                 <div style={{
//                     display: 'grid',
//                     gridTemplateColumns: `repeat(${columns.length}, 1fr)`,
//                     backgroundColor: '#fafafa',
//                     borderBottom: '1px solid #f0f0f0'
//                 }}>
//                     {columns.map(col => (
//                         <div key={col.key.toString()} style={{
//                             padding: '12px 8px',
//                             fontSize: 14,
//                             color: '#666',
//                             whiteSpace: 'nowrap',
//                             overflow: 'hidden',
//                             textOverflow: 'ellipsis'
//                         }}>
//                             {col.title}
//                         </div>
//                     ))}
//                 </div>

//                 {/* 表格内容 */}
//                 <div style={{
//                     display: 'grid',
//                     gridTemplateRows: `repeat(${data.length}, auto)`
//                 }}>
//                     {data?.length ? (
//                         data.map((item) => (
//                             <div key={item.OBJECT_ID} style={{
//                                 display: 'grid',
//                                 gridTemplateColumns: `repeat(${columns.length}, 1fr)`,
//                                 borderBottom: '1px solid #f0f0f0',
//                                 '&:last-child': {
//                                     borderBottom: 'none'
//                                 }
//                             }}>
//                                 {columns.map(col => (
//                                     <div
//                                         key={col.key.toString()}
//                                         style={{
//                                             padding: '12px 8px',
//                                             fontSize: 14,
//                                             color: '#666',
//                                             whiteSpace: 'nowrap',
//                                             overflow: 'hidden',
//                                             textOverflow: 'ellipsis'
//                                         }}>
//                                         {col.key === 'S_DQ_PCTCHANGE' ?
//                                             `${(Number(item[col.key as keyof T]))}%` :
//                                             (['basis', 'annualizedBasis', 'priceChangeRate', "S_DQ_CLOSE"].includes(col.key.toString()) ?
//                                                 `${(Number(item[col.key])).toFixed(2)}${col.key == 'priceChangeRate' ? '%' : ''}` :
//                                                 item[col.key])}
//                                     </div>
//                                 ))}
//                             </div>
//                         ))
//                     ) : (
//                         <div style={{
//                             padding: 20,
//                             textAlign: 'center',
//                             color: '#999'
//                         }}>
//                             暂无数据
//                         </div>
//                     )}
//                 </div>
//             </div>
//         </div>
//     );
// }

// 期货模块通用配置
const FUTURES_COLUMNS = [
    { title: "合约", key: "S_INFO_WINDCODE", width: "25%" },
    { title: '结算价', key: 'S_DQ_CLOSE', width: "25%" },
    { title: "较前日涨幅", key: 'priceChangeRate', width: "25%" },
    { title: '基差', key: 'basis', width: "25%" },
    { title: '年化基差比率', key: 'annualizedBasis', width: "25%" },
    { title: '剩余天数', key: 'remainingDays', width: "25%" },
    { title: "到期日", key: "delistDate", width: "25%" }
];

const ArrowIcon = () => (
    <div style={{
        width: 13,
        height: 13,
        borderRight: '2px solid #CCCCCC',
        borderTop: '2px solid #CCCCCC',
        transform: 'rotate(45deg)',
        marginRight: 16
    }} />
);

// 图表组件
function BasisChart({ data }: {
    data: {
        xAxisData?: string[];
        seriesData?: Array<{ name: string; type: string; data: number[] }>;
    }
}) {
    const chartRef = useRef<HTMLDivElement>(null);

    if (!data.xAxisData?.length || !data.seriesData?.length) {
        return <div style={{
            // width: '600px',
            height: '400px',
            marginTop: '20px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999'
        }}>
            <h3 style={{ marginBottom: 16, fontSize: 16 }}>年化基差走势</h3>
            暂无数据
        </div>;
    }

    const processedSeries = data?.seriesData?.map(series => ({
        ...series,
        name: series.name.replace(/\.CFE$/, ''),
        symbol: 'none',
    })) || [];

    useEffect(() => {
        if (!chartRef.current || !data) return;
        const myChart = echarts.init(chartRef.current, null, {
            useCoarsePointer: true,
            pointerSize: 10,
            useDirtyRect: true
        });

        const option = {
            title: {
                text: '年化基差走势', left: '0%',
                textStyle: {  // 新增字体样式配置
                    fontSize: 18,
                    fontFamily: 'PingFang SC',
                    fontWeight: 600,
                    color: 'rgba(51, 51, 51, 1)',
                }
            },
            tooltip: { trigger: 'axis', valueFormatter: (value: number) => value ? `${value}%` : '', z: -10000 },
            legend: { data: data.seriesData?.map(s => s.name.replace(/\.CFE$/, '')), top: 30 },
            xAxis: { type: 'category', data: data.xAxisData },
            yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
            series: processedSeries
        };

        myChart.setOption(option);
        return () => myChart.dispose();
    }, [data.xAxisData, data.seriesData]);

    return <div ref={chartRef} style={{ width: '100%', height: '450px', marginTop: '20px', position: "relative", zIndex: 1 }} />;
}

function LineChart({ xAxisData, yAxisData, title }: {
    xAxisData: string[],
    yAxisData: number[],
    title: string
}) {

    const chartRef = useRef<HTMLDivElement>(null);

    const hasData = xAxisData?.length && yAxisData?.length

    // if (!xAxisData?.length || !yAxisData?.length) {
    //     return <div style={{
    //         width: '600px',
    //         height: '400px',
    //         marginTop: '20px',
    //         display: 'flex',
    //         alignItems: 'center',
    //         justifyContent: 'center',
    //         color: '#999'
    //     }}>
    //         暂无数据
    //     </div>;
    // }

    useEffect(() => {
        if (!chartRef.current || !xAxisData) return;
        const myChart = echarts.init(chartRef.current);

        const option = {
            title: {
                text: title, left: '0%', textStyle: {  // 新增字体样式配置
                    fontSize: 18,
                    fontFamily: 'PingFang SC',
                    fontWeight: 600,
                    color: 'rgba(51, 51, 51, 1)',
                }
            },
            tooltip: {
                trigger: 'axis', formatter: (params: any) => {
                    const data = params[0]
                    return `${data.name}：${data.value}%`;
                }
            },
            xAxis: { type: 'category', data: xAxisData },
            yAxis: { type: 'value', axisLabel: { formatter: '{value}%' } },
            series: [{
                name: title,
                type: 'line',
                smooth: true,
                symbol: "none",
                data: yAxisData
            }]
        };

        myChart.setOption(option);
        return () => myChart.dispose();
    }, [xAxisData, yAxisData]);

    return <div ref={chartRef} style={{ width: '100%', height: '400px', marginTop: '20px', position: "relative", zIndex: 1 }} />;
}


const removeCFESuffix = (data: ProcessedFuturesData[]) => {
    return data?.map(item => ({
        ...item,
        S_INFO_WINDCODE: item.S_INFO_WINDCODE?.replace(/\.CFE$/, '') || null
    })) || [];
};


// 各合约模块
function FuturesModule({
    futureRes,
    basisTrendRes,
    hedgeBasisData,
    weekAverage,
    monthAverage,
    yearAverage
}: {
    futureRes: SWRResponse<ProcessedFuturesData[]>,
    basisTrendRes?: SWRResponse<{ xAxisData: string[], seriesData: any[] }>,
    hedgeBasisData?: { xAxisData: string[], yAxisData: number[] },
    weekAverage?: number | null,
    monthAverage?: number | null,
    yearAverage?: number | null
}) {

    const averageData = [
        { OBJECT_ID: 'week', type: '本周平均', value: weekAverage?.toFixed(2) + '%' || '--' },
        { OBJECT_ID: 'month', type: '本月平均', value: monthAverage?.toFixed(2) + '%' || '--' },
        { OBJECT_ID: 'year', type: '本年平均', value: yearAverage?.toFixed(2) + '%' || '--' }
    ];
    const accountData = accountQuery.user()
    const contractType = futureRes.data?.[0]?.S_INFO_WINDCODE?.slice(0, 2) || 'IC';
    const indexName = indexMap[contractType] || '中证500';
    return (
        <>

            {basisTrendRes?.isLoading ? (
                <div style={{
                    textAlign: 'center',
                    padding: '20px',
                    color: '#999'
                }}>
                    加载中...
                </div>
            ) : (
                <>

                    {/* <NewCommonTable
                        data={removeCFESuffix(futureRes.data)}
                        columns={FUTURES_COLUMNS}
                    />
                    <Divider></Divider> */}
                    {basisTrendRes?.data && <BasisChart data={basisTrendRes.data} />}
                    <Divider style={{ marginTop: -25 }}></Divider>
                    {hedgeBasisData && monthAverage && yearAverage && (
                        <LineChart
                            xAxisData={hedgeBasisData.xAxisData}
                            yAxisData={hedgeBasisData.yAxisData}
                            title="近三年加权跨期年化基差走势"
                        />
                    )}
                    <Divider style={{ marginTop: -25 }}></Divider>
                    <h2 style={{ fontWeight: 600, fontSize: 18, color: "rgba(51, 51, 51, 1)", marginBottom: 17, marginTop: 8 }}>
                        加权跨期年化基差平均值
                    </h2>
                    <CommonTable
                        data={averageData}
                        columns={AVERAGE_COLUMNS}
                        headerTitle="加权跨期年化基差平均值"
                    />
                    <div style={{
                        marginTop: 5,
                        background: '#FFFFFF',
                        // borderRadius: 20,
                        // border: '1px solid #eee',
                        fontSize: 12,
                        lineHeight: 2,
                    }}>
                        <div style={{ fontWeight: 600, marginBottom: 8, color: "rgba(51, 51, 51, 1)" }}>计算说明：</div>
                        <span style={{ display: "block", marginBottom: -18 }}>各{contractType}合约按市场持仓量加权年化基差⽐率：</span><br />
                        1. 计算{contractType}<sub>当⽉</sub>、{contractType}<sub>下⽉</sub>、{contractType}<sub>下季</sub>、{contractType}<sub>隔季</sub>合约与{indexName}指数的年化基差<br />
                        <span style={{ display: "block", marginBottom: -18 }}>2. 按各合约市场持仓量权重加总，计算公式：</span><br />
                        <div>
                            [ ({contractType}<sub>当⽉</sub>结算价 - 指数收盘价)/指数收盘价 × 252 ÷ 剩余交易⽇ × 持仓量 +
                            &nbsp;&nbsp;({contractType}<sub>下⽉</sub>结算价 - 指数收盘价)/指数收盘价 × 252 ÷ 剩余交易⽇ × 持仓量 +
                            &nbsp;&nbsp;({contractType}<sub>下季</sub>结算价 - 指数收盘价)/指数收盘价 × 252 ÷ 剩余交易⽇ × 持仓量 +
                            &nbsp;&nbsp;({contractType}<sub>隔季</sub>结算价 - 指数收盘价)/指数收盘价 × 252 ÷ 剩余交易⽇ × 持仓量 ]
                            ÷ (总持仓量)
                        </div>
                    </div>

                </>
            )}
        </>
    );
}

// 主组件
export default () => {
    const tradeDateRes = tradeDateQuery.tradeDate()
    const [selectedDate, setSelectedDate] = useState<string>('');
    const [selectedDateVisible, setSelectedDateVisible] = useState(false);


    const [icVisible, setICVisible] = useState(false)

    const [iHVisible, setIHVisible] = useState(false)

    const [iFvisible, setIFVisible] = useState(false)

    const [iMvisible, setIMVisible] = useState(false)
    const navigate = useNavigate();
    useEffect(() => {
        if (tradeDateRes.data?.length) {
            const today = dayjs().format('YYYY-MM-DD');
            const findNearestTradeDate = (date: string, days = 0): string => {
                const targetDate = dayjs(date).subtract(days, 'day').format('YYYY-MM-DD');
                const found = tradeDateRes.data?.find(d => d.date === targetDate);
                if (found) return found.date;
                if (days > 30) return tradeDateRes.data?.[0]?.date || '';
                return findNearestTradeDate(date, days + 1);
            };

            setSelectedDate(findNearestTradeDate(today));
        }
    }, [tradeDateRes.data]);
    const tradeDateOptions = [
        (tradeDateRes.data || [])
            .filter(item => !dayjs(item.date).isAfter(dayjs(), "day"))
            .slice()
            .sort((a, b) => dayjs(b.date).unix() - dayjs(a.date).unix()).
            map((item) => ({
                label: item.date,
                value: item.date
            }))
    ]

    const tradeAIndexRes = aIndexQuery.getTradeDateData(selectedDate);


    const [landscapeWidth, setLandscapeWidth] = useState(0);

    useEffect(() => {
        if (typeof window === 'undefined') return;

        const handleResize = () => {
            if (window.innerWidth > window.innerHeight) {
                setLandscapeWidth(window.innerWidth);
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);


    // 现货表格列配置
    const SPOT_COLUMNS = [
        { title: '代码', key: "S_INFO_WINDCODE", width: "20%" },
        { title: "名称", key: "name", width: "25%" },
        { title: '收盘价', key: "S_DQ_CLOSE", width: "20%" },
        { title: '涨跌额', key: "S_DQ_CHANGE", width: "20%" },
        { title: '较前日涨跌幅', key: "S_DQ_PCTCHANGE", width: "20%" },
    ];

    return (
        <>
            <Divider style={{
                borderStyle: "hidden",
                color: "#02588B",
                fontSize: '16px',
                fontFamily: 'PingFang SC',
                fontWeight: 600,
                lineHeight: '20px',
                letterSpacing: 0,
                textAlign: 'center'
            }}>基差计算器</Divider>
            <div style={
                {
                    width: "100%",
                    display: 'flex',
                    flexDirection: "column",
                    alignItems: "center",
                    backgroundColor: ""
                }
            }>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16, width: '90%', alignItems: 'center' }}>
                    <span style={{
                        fontFamily: "PingFang SC",
                        fontWeight: 600,
                        fontSize: 14,
                        whiteSpace: 'nowrap',
                    }}>交易日时间</span>
                    <Button onClick={() => { setSelectedDateVisible(true) }} style={{
                        width: "70%",
                        borderRadius: 29,
                        color: '#333333',
                        backgroundColor: '#F5F5F5',
                    }}>

                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: "100%",
                            height: "100%"
                        }}>
                            <span style={{ paddingLeft: 10, fontSize: 11 }}>{selectedDate ? dayjs(selectedDate).format('YYYY-MM-DD') : '请选择'}</span>
                            <span style={{ paddingRight: 10 }}><DownOutline /></span>
                        </div>
                    </Button>
                </div>
            </div>

            <div style={
                {
                    width: "100%",
                    display: 'flex',
                    flexDirection: "column",
                    alignItems: "center",
                    backgroundColor: ""
                }
            }>
                <div style={{ width: "90%", backgroundColor: "#FFFFFF", borderRadius: 20, boxShadow: "0px 4px 44px 5px rgba(9, 116, 179, 0.07)" }}>
                    <h3 style={{ fontFamily: "PingFang SC", fontSize: 16, marginLeft: 15, marginTop: 20, marginBottom: 20, fontWeight: 600 }}>现货模块</h3>
                    {tradeAIndexRes.isLoading ? <div style={{
                        textAlign: 'center',
                        padding: '20px',
                        color: '#999'
                    }}>
                        加载中...
                    </div> : (
                        <div style={{ width: "100%", display: "flex", justifyContent: "center", marginTop: -20 }}>
                            <div style={{ width: "93%" }}>   <NewCommonTable
                                data={tradeAIndexRes.data?.map(d => ({
                                    ...d,
                                    name: INDEX_NAME_MAP[d.S_INFO_WINDCODE as string] || '--'
                                })) ?? []}
                                columns={SPOT_COLUMNS}
                            /></div>
                        </div>


                    )}
                </div>
                {/* <Collapse defaultActiveKey='1' accordion>

                        <Collapse.Panel key='1' title={<span style={{ fontFamily: "PingFang SC", fontSize: 16, fontWeight: 600 }}>IC模块</span>} style={{
                            borderRadius: 20, backgroundColor: "#FFFFFF", paddingLeft: 20,
                            boxShadow: '0px 4px 44px 5px rgba(9, 116, 179, 0.07)',
                        }}
                        >
                            <div style={{ backgroundColor: "#FFFFFF", borderRadius: 20, width: "100%", marginTop: -11, }}>
                                <FuturesModule
                                    futureRes={aIndexFuture.getICDayData(selectedDate)}
                                    basisTrendRes={aIndexFuture.getICAnnualizedBasisTrend(selectedDate)}
                                    hedgeBasisData={{
                                        xAxisData: aIndexFuture.getIC3YearsData(selectedDate).data?.xAxisData || [],
                                        yAxisData: (aIndexFuture.getIC3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                    }}
                                    weekAverage={aIndexFuture.getICWeekBasisAverage(selectedDate).data}
                                    monthAverage={aIndexFuture.getICMonthBasisAverage(selectedDate).data}
                                    yearAverage={aIndexFuture.getICYearBasisAverage(selectedDate).data}
                                />
                            </div>
                        </Collapse.Panel>
                        <Collapse.Panel key='4' title={<span style={{ fontFamily: "PingFang SC", fontSize: 16, fontWeight: 600 }}>IH模块</span>} style={{ borderRadius: 20, backgroundColor: "#FFFFFF", paddingLeft: 20 }}>
                            <div style={{ backgroundColor: "#FFFFFF", borderRadius: 20 }}><FuturesModule futureRes={aIndexFuture.getIHDayData(selectedDate)} basisTrendRes={aIndexFuture.getIHAnnualizedBasisTrend(selectedDate)}
                                hedgeBasisData={{
                                    xAxisData: aIndexFuture.getIH3YearsData(selectedDate).data?.xAxisData || [],
                                    yAxisData: (aIndexFuture.getIH3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                }}
                                weekAverage={aIndexFuture.getIHWeekBasisAverage(selectedDate).data}
                                monthAverage={aIndexFuture.getIHMonthBasisAverage(selectedDate).data}
                                yearAverage={aIndexFuture.getIHYearBasisAverage(selectedDate).data}
                            /></div>

                        </Collapse.Panel>
                        <Collapse.Panel key='5' title={<span style={{ fontFamily: "PingFang SC", fontSize: 16, fontWeight: 600 }}>IF模块</span>} style={{ borderRadius: 20, backgroundColor: "#FFFFFF", paddingLeft: 20 }}>
                            <div style={{ backgroundColor: "#FFFFFF", borderRadius: 20 }}><FuturesModule futureRes={aIndexFuture.getIFDayData(selectedDate)} basisTrendRes={aIndexFuture.getIFAnnualizedBasisTrend(selectedDate)}
                                hedgeBasisData={{
                                    xAxisData: aIndexFuture.getIF3YearsData(selectedDate).data?.xAxisData || [],
                                    yAxisData: (aIndexFuture.getIF3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                }}
                                weekAverage={aIndexFuture.getIFWeekBasisAverage(selectedDate).data}
                                monthAverage={aIndexFuture.getIFMonthBasisAverage(selectedDate).data}
                                yearAverage={aIndexFuture.getIFYearBasisAverage(selectedDate).data}
                            /></div>

                        </Collapse.Panel>
                        <Collapse.Panel key='6' title={<span style={{ fontFamily: "PingFang SC", fontSize: 16, fontWeight: 600 }}>IM模块</span>} style={{ borderRadius: 20, backgroundColor: "#FFFFFF", paddingLeft: 20 }}>
                            <div style={{ backgroundColor: "#FFFFFF", borderRadius: 20 }}> <FuturesModule futureRes={aIndexFuture.getIMDayData(selectedDate)} basisTrendRes={aIndexFuture.getIMAnnualizedBasisTrend(selectedDate)}
                                hedgeBasisData={{
                                    xAxisData: aIndexFuture.getIM3YearsData(selectedDate).data?.xAxisData || [],
                                    yAxisData: (aIndexFuture.getIM3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                }}
                                weekAverage={aIndexFuture.getIMWeekBasisAverage(selectedDate).data}
                                monthAverage={aIndexFuture.getIMMonthBasisAverage(selectedDate).data}
                                yearAverage={aIndexFuture.getIMYearBasisAverage(selectedDate).data}
                            /></div>

                        </Collapse.Panel>
                    </Collapse> */}
                <div style={{ width: "90%", height: 60, borderRadius: 20, backgroundColor: "rgba(255, 255, 255, 1)", display: "flex", boxShadow: "box-shadow: 0px 4px 44px 5px rgba(9, 116, 179, 0.07)", justifyContent: "space-between", alignItems: "center", marginTop: 15 }} onClick={() => {
                    setICVisible(true)
                }}>
                    <div style={{ fontSize: 16, fontWeight: 600, paddingLeft: 20 }}>
                        IC模块
                    </div>
                    <ArrowIcon />
                </div>
                <div style={{ width: "90%", height: 60, borderRadius: 20, backgroundColor: "rgba(255, 255, 255, 1)", display: "flex", boxShadow: "box-shadow: 0px 4px 44px 5px rgba(9, 116, 179, 0.07)", justifyContent: "space-between", alignItems: "center", marginTop: 15 }} onClick={() => {
                    setIFVisible(true)
                }}>
                    <div style={{ fontSize: 16, fontWeight: 600, paddingLeft: 20 }}>
                        IF模块
                    </div>
                    <ArrowIcon />
                </div>
                <div style={{ width: "90%", height: 60, borderRadius: 20, backgroundColor: "rgba(255, 255, 255, 1)", display: "flex", boxShadow: "box-shadow: 0px 4px 44px 5px rgba(9, 116, 179, 0.07)", justifyContent: "space-between", alignItems: "center", marginTop: 15 }} onClick={() => {
                    setIHVisible(true)
                }}>
                    <div style={{ fontSize: 16, fontWeight: 600, paddingLeft: 20 }}>
                        IH模块
                    </div>
                    <ArrowIcon />
                </div>
                <div style={{ width: "90%", height: 60, borderRadius: 20, backgroundColor: "rgba(255, 255, 255, 1)", display: "flex", boxShadow: "box-shadow: 0px 4px 44px 5px rgba(9, 116, 179, 0.07)", justifyContent: "space-between", alignItems: "center", marginTop: 15 }} onClick={() => {
                    setIMVisible(true)
                }}>
                    <div style={{ fontSize: 16, fontWeight: 600, paddingLeft: 20 }}>
                        IM模块
                    </div>
                    <ArrowIcon />
                </div>
            </div>

            <Popup
                destroyOnClose={true}
                visible={icVisible}
                onMaskClick={() => {
                    setICVisible(false)
                }}
                onClose={() => {
                    setICVisible(false)
                }}
                bodyStyle={{
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                    height: '85vh',
                    minHeight: '85vh',
                }}
            >
                {
                    <>
                        <div style={{ width: "100%", display: "flex", justifyContent: "center", height: 50, alignContent: "center", position: "fixed", top: 0, zIndex: 2, backgroundColor: "rgba(255, 255, 255, 1)", borderTopLeftRadius: 30, borderTopRightRadius: 30 }} onClick={() => { setICVisible(false) }}>
                            <SvgWrapper component={returnSVG} style={{ width: 45, height: 45, color: "rgba(204, 204, 204, 1)", fill: "rgba(204, 204, 204, 1)", marginTop: 1 }}></SvgWrapper>
                        </div>
                        <div style={{ overflowY: "auto", height: "100%", width: "100%", display: "flex", justifyContent: "center", backgroundColor: "rgba(255, 255, 255, 1)", borderRadius: "inherit" }}>
                            <div style={{ width: "90%", height: "100%" }}>
                                <div style={{ height: 50 }}>

                                </div>
                                <h2
                                    style={{ fontFamily: "PingFang SC", fontSize: 18, fontWeight: 600, color: 'rgba(51, 51, 51, 1)', }}
                                >IC模块
                                </h2>
                                <NewCommonTable
                                    data={removeCFESuffix(aIndexFuture.getICDayData(selectedDate).data)}
                                    columns={FUTURES_COLUMNS}
                                />
                                <Divider></Divider>
                                <FuturesModule
                                    futureRes={aIndexFuture.getICDayData(selectedDate)}
                                    basisTrendRes={aIndexFuture.getICAnnualizedBasisTrend(selectedDate)}
                                    hedgeBasisData={{
                                        xAxisData: aIndexFuture.getIC3YearsData(selectedDate).data?.xAxisData || [],
                                        yAxisData: (aIndexFuture.getIC3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                    }}
                                    weekAverage={aIndexFuture.getICWeekBasisAverage(selectedDate).data}
                                    monthAverage={aIndexFuture.getICMonthBasisAverage(selectedDate).data}
                                    yearAverage={aIndexFuture.getICYearBasisAverage(selectedDate).data}
                                />

                            </div>

                        </div>
                    </>



                }
            </Popup>


            <Popup
                destroyOnClose={true}
                visible={iFvisible}
                onMaskClick={() => {
                    setIFVisible(false)
                }}
                onClose={() => {
                    setIFVisible(false)
                }}
                bodyStyle={{
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                    height: '85vh',
                    minHeight: '85vh',
                }}
            >
                {
                    <>
                        <div style={{ width: "100%", display: "flex", justifyContent: "center", height: 50, alignContent: "center", position: "fixed", top: 0, zIndex: 2, backgroundColor: "rgba(255, 255, 255, 1)", borderTopLeftRadius: 30, borderTopRightRadius: 30 }} onClick={() => { setIFVisible(false) }}>
                            <SvgWrapper component={returnSVG} style={{ width: 45, height: 45, color: "rgba(204, 204, 204, 1)", fill: "rgba(204, 204, 204, 1)", marginTop: 1 }}></SvgWrapper>
                        </div>
                        <div style={{ overflowY: "auto", height: "100%", width: "100%", display: "flex", justifyContent: "center", backgroundColor: "rgba(255, 255, 255, 1)", borderRadius: "inherit" }}>
                            <div style={{ width: "90%", height: "100%" }}>
                                <div style={{ height: 50 }}>

                                </div>

                                <h2
                                    style={{ fontFamily: "PingFang SC", fontSize: 18, fontWeight: 600, color: 'rgba(51, 51, 51, 1)', }}
                                >IF模块
                                </h2>
                                <NewCommonTable
                                    data={removeCFESuffix(aIndexFuture.getIFDayData(selectedDate).data)}
                                    columns={FUTURES_COLUMNS}
                                />
                                <Divider></Divider>
                                <FuturesModule
                                    futureRes={aIndexFuture.getIFDayData(selectedDate)}
                                    basisTrendRes={aIndexFuture.getIFAnnualizedBasisTrend(selectedDate)}
                                    hedgeBasisData={{
                                        xAxisData: aIndexFuture.getIF3YearsData(selectedDate).data?.xAxisData || [],
                                        yAxisData: (aIndexFuture.getIF3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                    }}
                                    weekAverage={aIndexFuture.getIFWeekBasisAverage(selectedDate).data}
                                    monthAverage={aIndexFuture.getIFMonthBasisAverage(selectedDate).data}
                                    yearAverage={aIndexFuture.getIFYearBasisAverage(selectedDate).data}
                                />

                            </div>

                        </div>
                    </>



                }
            </Popup>


            <Popup
                destroyOnClose={true}
                visible={iHVisible}
                onMaskClick={() => {
                    setIHVisible(false)
                }}
                onClose={() => {
                    setIHVisible(false)
                }}
                bodyStyle={{
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                    height: '85vh',
                    minHeight: '85vh',
                }}
            >
                {
                    <> <div style={{ width: "100%", display: "flex", justifyContent: "center", height: 50, alignContent: "center", position: "fixed", top: 0, zIndex: 2, backgroundColor: "rgba(255, 255, 255, 1)", borderTopLeftRadius: 30, borderTopRightRadius: 30 }} onClick={() => { setIHVisible(false) }}>
                        <SvgWrapper component={returnSVG} style={{ width: 45, height: 45, color: "rgba(204, 204, 204, 1)", fill: "rgba(204, 204, 204, 1)", marginTop: 1 }}></SvgWrapper>
                    </div>
                        <div style={{ overflowY: "auto", height: "100%", width: "100%", display: "flex", justifyContent: "center", backgroundColor: "rgba(255, 255, 255, 1)", borderRadius: "inherit" }}>
                            <div style={{ width: "90%", height: "100%" }}>
                                <div style={{ height: 50 }}>

                                </div>

                                <h2
                                    style={{ fontFamily: "PingFang SC", fontSize: 18, fontWeight: 600, color: 'rgba(51, 51, 51, 1)', }}
                                >IH模块
                                </h2>
                                <NewCommonTable
                                    data={removeCFESuffix(aIndexFuture.getIHDayData(selectedDate).data)}
                                    columns={FUTURES_COLUMNS}
                                />
                                <Divider></Divider>
                                <FuturesModule
                                    futureRes={aIndexFuture.getIHDayData(selectedDate)}
                                    basisTrendRes={aIndexFuture.getIHAnnualizedBasisTrend(selectedDate)}
                                    hedgeBasisData={{
                                        xAxisData: aIndexFuture.getIH3YearsData(selectedDate).data?.xAxisData || [],
                                        yAxisData: (aIndexFuture.getIH3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                    }}
                                    weekAverage={aIndexFuture.getIHWeekBasisAverage(selectedDate).data}
                                    monthAverage={aIndexFuture.getIHMonthBasisAverage(selectedDate).data}
                                    yearAverage={aIndexFuture.getIHYearBasisAverage(selectedDate).data}
                                />

                            </div>

                        </div>
                    </>



                }
            </Popup>


            <Popup
                destroyOnClose={true}
                visible={iMvisible}
                onMaskClick={() => {
                    setIMVisible(false)
                }}
                onClose={() => {
                    setIMVisible(false)
                }}
                bodyStyle={{
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                    height: '85vh',
                    minHeight: '85vh',
                }}
            >
                {
                    <>
                        <div style={{ width: "100%", display: "flex", justifyContent: "center", height: 50, alignContent: "center", position: "fixed", top: 0, zIndex: 2, backgroundColor: "rgba(255, 255, 255, 1)", borderTopLeftRadius: 30, borderTopRightRadius: 30 }} onClick={() => { setIMVisible(false) }}>
                            <SvgWrapper component={returnSVG} style={{ width: 45, height: 45, color: "rgba(204, 204, 204, 1)", fill: "rgba(204, 204, 204, 1)", marginTop: 1 }}></SvgWrapper>
                        </div>
                        <div style={{ overflowY: "auto", height: "100%", width: "100%", display: "flex", justifyContent: "center", backgroundColor: "rgba(255, 255, 255, 1)", borderRadius: "inherit" }}>
                            <div style={{ width: "90%", height: "100%" }}>
                                <div style={{ height: 50 }}>

                                </div>

                                <h2
                                    style={{ fontFamily: "PingFang SC", fontSize: 18, fontWeight: 600, color: 'rgba(51, 51, 51, 1)', }}
                                >IM模块
                                </h2>
                                <NewCommonTable
                                    data={removeCFESuffix(aIndexFuture.getIMDayData(selectedDate).data)}
                                    columns={FUTURES_COLUMNS}
                                />
                                <Divider></Divider>
                                <FuturesModule
                                    futureRes={aIndexFuture.getIMDayData(selectedDate)}
                                    basisTrendRes={aIndexFuture.getIMAnnualizedBasisTrend(selectedDate)}
                                    hedgeBasisData={{
                                        xAxisData: aIndexFuture.getIM3YearsData(selectedDate).data?.xAxisData || [],
                                        yAxisData: (aIndexFuture.getIM3YearsData(selectedDate).data?.yAxisData || []).filter(v => v != null) as number[]
                                    }}
                                    weekAverage={aIndexFuture.getIMWeekBasisAverage(selectedDate).data}
                                    monthAverage={aIndexFuture.getIMMonthBasisAverage(selectedDate).data}
                                    yearAverage={aIndexFuture.getIMYearBasisAverage(selectedDate).data}
                                />

                            </div>

                        </div>
                    </>



                }
            </Popup>


            <Picker
                columns={tradeDateOptions}
                onConfirm={(value) => {
                    setSelectedDate(value[0] as string)
                    setSelectedDateVisible(false)
                }}
                onCancel={() => { setSelectedDateVisible(false) }}
                visible={selectedDateVisible}
                value={selectedDate ? [selectedDate] : []}
            >
            </Picker>
        </>
    );
};
