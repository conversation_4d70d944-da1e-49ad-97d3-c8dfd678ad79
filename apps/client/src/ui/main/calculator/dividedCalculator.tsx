import * as query from '@/queries/ta';

import { toCNY, toInt } from '@/utils/number';
import { Button, CapsuleTabs, Divider, Space, DatePicker, Picker, List } from 'antd-mobile';
import { useNavigate } from 'umi';
import { Form, Input, Tag } from 'antd-mobile'
import { useState } from 'react';
import dayjs from 'dayjs';
import * as shareQuery from '@/queries/ta/share';
import * as tradeQuery from "@/queries/ta/trade"
import * as dvQuery from "@/queries/ta/dividend";
import { DownOutline } from 'antd-mobile-icons'
import { z } from 'zod';
type QueryParams = {
    reinvestmentDistributionDate: string | null;
    agency: string | null;
    portfolioName: string | null;
}

type DividendResult = {
    dividendBalancePuf: number | null;
    reinvestmentDistributionDate: string;
    profitRewardRate: number;
    dividendMethod: string;
    dividendIndeed: string;
    reinvestmentShares: string;
    performanceAmount: string;
};

export const toSHow = (value: number | null | undefined): string | undefined => {
    const valueParsed = z.number().min(0).safeParse(value);
    if (!valueParsed.success) return;

    return valueParsed.data.toLocaleString('zh-CN', {
        style: 'decimal',
        minimumFractionDigits: 2,
        useGrouping: true
    });
};



export default () => {
    const mutation = dvQuery.searchByDividendCalculator()
    const userShareQuery = tradeQuery.userTradeData()
    const shareDownloadMutation = shareQuery.downLoadDividendCalculator()
    const getDividedDataMutation = shareQuery.getDividedDataByProductName()

    const shareData = userShareQuery.data ? userShareQuery.data : []

    const [timeSelectVisible, setTimeSelectVisible] = useState(false)
    const [productVisible, setProductVisible] = useState(false)
    const [institutionVisible, setInstitutionVisible] = useState(false)
    const [selectedProduct, setSelectedProduct] = useState<string[]>([])

    const showDownloadButton = shareQuery.checkDividendRecordExistence(selectedProduct[0] ?? '')

    const [selectedInstitution, setSelectedInstitution] = useState<string[]>([])
    const [selectedDate, setSelectedDate] = useState<string | null>(null)
    const [resData, setResData] = useState<DividendResult | null>(null);
    const [distributionDates, setDistributionDates] = useState<(string | null)[]>([])
    const [agencies, setAgencies] = useState<string[]>([])
    const navigate = useNavigate();
    const { data } = query.totalAsset();

    const handleDownload = async () => {
        const res = await shareDownloadMutation.trigger(selectedProduct[0] ?? '')

        window.open(`/api/static?id=${encodeURIComponent(res)}`);
    };


    const handleQuery = async () => {
        const params: QueryParams = {
            reinvestmentDistributionDate: selectedDate ? dayjs(selectedDate).format('YYYY-MM-DD') : null,
            agency: selectedInstitution[0] || null,
            portfolioName: selectedProduct[0] || null
        }
        const res = await mutation.trigger(params)
        if (res.length > 1) {
        const baseData = res[0];
        const mergedData = {
            ...baseData,
            dividendIndeed: res.reduce((sum, item) => sum + parseFloat(item.dividendIndeed || '0'), 0),
            reinvestmentShares: res.reduce((sum, item) => sum + parseFloat(item.reinvestmentShares || '0'), 0),
            performanceAmount: res.reduce((sum, item) => sum + parseFloat(item.performanceAmount || '0'), 0)
        };
        
        setResData(mergedData);
    } else {
        setResData(res[0]);
    }
    }

    const productColumns = [
        Array.from(new Set(shareData.map(item => item.portfolioName))).filter(Boolean).map(name => ({ label: name as string, value: name as string }))
    ]

    const institutionColumns = [
        Array.from(new Set(shareData.map(item => item.agency))).filter(agency => agency).map(agency => ({ label: agency, value: agency }))
    ]

    return (
        <>
            <div style={{ backgroundColor: "rgb(255, 255, 255)", height: "110vh", marginTop: -20 }}>
                <Divider style={{
                    borderStyle: "hidden",
                    color: "#02588B",
                    fontSize: '16px',
                    fontFamily: 'PingFang SC',
                    fontWeight: 600,
                    letterSpacing: 0,
                    textAlign: 'center',
                    paddingTop: 20
                }}>分红计算器</Divider>
                <Form layout='horizontal' mode='card' style={{ '--border-inner': '0', '--border-bottom': '0', '--border-top': '0', backgroundColor: "rgb(255, 255, 255)" }}>
                    <Form.Item label={<span style={{
                        display: "block",
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        color: "#333333",
                        letterSpacing: 0,
                        paddingLeft: "3%",
                        paddingTop: 5
                    }}>产品名称</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>

                        <Button onClick={() => setProductVisible(true)} style={{
                            width: "97%",
                            borderRadius: 29,
                            // fontSize: 10,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%",
                                overflow: 'hidden',
                            }}>
                                <span style={{
                                    paddingLeft: 10,
                                    width: 170,
                                    fontSize: 11,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    textAlign: 'left',
                                }}>{selectedProduct[0] || '请选择'}</span>
                                <span style={{ paddingRight: 5, marginTop: -1 }}><DownOutline /></span>
                            </div>

                            {/* <DownOutline fontSize={12} style={{ color: '#999' }} /> */}
                        </Button>
                        <Picker
                            columns={productColumns}
                            visible={productVisible}
                            title="请选择产品"
                            onClose={() => setProductVisible(false)}
                            onConfirm={async (value) => {
                                setSelectedProduct(value)
                                setProductVisible(false)
                                const res = await getDividedDataMutation.trigger(value[0] as string)
                                setDistributionDates(res.dates || [])
                                setAgencies(res.agencies || [])
                                setResData(null)
                                setSelectedDate(null)
                                setSelectedInstitution([])
                            }}
                        />
                    </Form.Item>
                    <Form.Item label={<span style={{
                        display: "block",
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        color: "#333333",
                        letterSpacing: 0,
                        paddingLeft: "3%",
                        paddingTop: 5
                    }} >收益分配日</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => { setTimeSelectVisible(true) }} disabled={!distributionDates.length} style={{
                            width: "97%",
                            borderRadius: 29,
                            // fontSize: 10,
                            color: '#333333',
                            backgroundColor: '#F5F5F5',
                        }}>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{ paddingLeft: 10, fontSize: 11 }}>{selectedDate ? dayjs(selectedDate).format('YYYY-MM-DD') : '请选择'}</span>
                                <span style={{ paddingRight: 5, marginTop: -1 }}><DownOutline /></span>
                            </div>

                        </Button>
                        <Picker visible={timeSelectVisible} title="请选择时间" onConfirm={date => {
                            setSelectedDate(date);
                            setTimeSelectVisible(false);
                        }}
                            onClose={() => setTimeSelectVisible(false)}
                            columns={[distributionDates.map(a => ({ label: dayjs(a).format('YYYY-MM-DD'), value: a }))]}
                        />
                    </Form.Item>
                    <Form.Item label={<span style={{
                        display: "block",
                        fontFamily: 'PingFang SC',
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: '20px',
                        color: "#333333",
                        letterSpacing: 0,
                        paddingLeft: "3%",
                        paddingTop: 5
                    }}
                    >募集机构</span>} style={{ backgroundColor: "rgb(255, 255, 255)" }}>
                        <Button onClick={() => { setInstitutionVisible(true) }} disabled={!agencies.length}
                            style={{
                                width: "97%",
                                borderRadius: 29,
                                color: '#333333',
                                backgroundColor: '#F5F5F5',
                                // marginRight:8
                                // fontSize: 10,
                            }}
                        >
                            <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: "100%",
                                height: "100%"
                            }}>
                                <span style={{ paddingLeft: 10, fontSize: 11 }}>{selectedInstitution[0] || '请选择'}</span>
                                <span style={{ paddingRight: 5, marginTop: -1 }}><DownOutline /></span>
                            </div>

                        </Button>
                        <Picker
                            columns={[agencies.map(a => ({ label: a, value: a }))]}
                            visible={institutionVisible}
                            onClose={() => setInstitutionVisible(false)}
                            onConfirm={(value) => {
                                setSelectedInstitution(value)
                                setInstitutionVisible(false)
                            }}
                        />
                    </Form.Item>
                </Form>
                <Button color='primary' fill='outline' block onClick={() => {
                    setSelectedDate(null)
                    setSelectedInstitution([])
                    setSelectedProduct([])
                    setResData(null)
                }} style={{
                    width: "87%",
                    borderRadius: 30,
                    margin: '0 auto',
                    display: 'block',
                    color: '#02588B',
                    fontWeight: 600,
                    fontSize: 14
                }}>
                    重置
                </Button>
                {/* <Divider /> */}
                <Button color='primary' fill='solid' block
                    disabled={!selectedDate || !selectedInstitution.length || !selectedProduct.length}
                    onClick={handleQuery}

                    style={{
                        width: "87%",
                        borderRadius: 30,
                        margin: '0 auto',
                        display: 'block',
                        marginTop: 23,
                        fontWeight: 500,
                        fontSize: 14
                    }}
                >
                    查询
                </Button>

                <div
                    style={{
                        width: "87%",
                        // height: 60,
                        borderRadius: 20,
                        margin: '0 auto',
                        background: '#FFFFFF',
                        marginTop: 23,
                        marginBottom: 16,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0px 4px 44px 5px rgba(9, 116, 179, 0.07)',
                    }}
                >
                    {
                        resData ?
                            (
                                <>
                                    <div style={{ width: "90%", height: "90%" }}>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>每单位分红金额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData?.dividendBalancePuf}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>本次业绩报酬计提日期</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>
                                                {dayjs(resData?.reinvestmentDistributionDate).format("YYYY-MM-DD")}
                                            </div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>业绩报酬计提比例</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{(resData?.profitRewardRate ?? 0) * 100}%</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>收益分配方式</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{resData?.dividendMethod}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>现金分红金额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{toSHow(resData?.dividendIndeed)}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>红利再投份额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{toSHow(resData?.reinvestmentShares)}</div>
                                        </div>
                                        <div style={{ display: 'flex', justifyContent: "space-between", marginTop: 15 }}>
                                            <div style={{ color: '#999999', fontSize: 12 }}>业绩报酬计提金额</div>
                                            <div style={{ color: '#666666', fontSize: 12 }}>{toSHow(resData?.performanceAmount)}</div>
                                        </div>
                                        <div style={{ color: '#02588B', fontSize: 11, marginTop: 15, fontWeight: 400, fontFamily: "PingFang SC", paddingBottom: 15 }}>
                                            注：本界面所展示的内容仅供投资者参考,不具法律约束力。<br />
                                            <span style={{ display: "block", marginTop: 5, marginLeft: 22, marginBottom: 3 }}>
                                                具体计算公式详见产品基金合同。
                                            </span>

                                        </div>
                                        {
                                            selectedProduct.length != 0 && showDownloadButton.data && (
                                                <div style={{ width: "100%", marginBottom: 20 }}>
                                                    <Button
                                                        style={{
                                                            marginLeft: 'auto',
                                                            display: 'block',
                                                            width: "102%",
                                                            fontSize: 10,
                                                            color: "rgba(255, 255, 255, 1)"
                                                        }}
                                                        color='primary'
                                                        shape='rounded'
                                                        onClick={handleDownload}
                                                    >
                                                        下载明细
                                                    </Button>

                                                </div>

                                            )
                                        }
                                    </div>


                                </>
                            )
                            : (<>
                                <span
                                    style={{
                                        fontFamily: "PingFang SC",
                                        fontSize: 14,
                                        width: "187%",
                                        height: 60,
                                        borderRadius: 20,
                                        fontWeight: 1400,
                                        color: '#02588B',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        margin: '0 auto',

                                    }}
                                >
                                    暂无数据
                                </span>



                            </>)

                    }
                </div>
            </div >

            {/* <List >
                {
                    resData ? (
                        <> <List.Item>每单位分红金额:{resData.dividendBalancePuf}</List.Item>
                            <List.Item>本次业绩报酬计提日期:{dayjs(resData.reinvestmentDistributionDate).format("YYYY-MM-DD")}</List.Item>
                            <List.Item>业绩报酬计提比例：{resData.profitRewardRate}</List.Item>
                            <List.Item>收益分配方式:{resData.dividendMethod}</List.Item>
                            <List.Item>现金分红金额:{resData.dividendIndeed}</List.Item>
                            <List.Item>红利再投份额:{resData.reinvestmentShares}</List.Item>
                            <List.Item>业绩报酬计提金额：{resData.performanceAmount}</List.Item>
                            <List.Item>注：本表计算结果仅供参考，请以托管外包清算数据为准。具体计算公式详见对应产品合同</List.Item>
                        </>
                    ) : (<>
                        <Button
                            block
                            size='large'
                            style={{
                                width: 335,
                                height: 60,
                                borderRadius: 20,
                                color: '#02588B',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                margin: '0 auto'
                            }}
                        >
                            暂无数据
                        </Button>

                    </>)
                }

            </List> */}
            {/* <div style={{ textAlign: 'center', margin: '16px 0' }}>
                <a
                    href="https://mp.weixin.qq.com/s/bxzAsiYtJ2SbkSz0IcZvEA?from=industrynews&nwr_flag=1&color_scheme=light#wechat_redirect"
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                        color: '#02588B',
                        cursor: 'pointer',
                        fontFamily: 'PingFang SC',
                        fontWeight: 400,
                        fontSize: 10,
                        lineHeight: '20px',
                        letterSpacing: 0,
                    }}
                >
                    豹豹说关于分红/业绩报酬
                </a>
            </div> */}

        </>

    );
};
