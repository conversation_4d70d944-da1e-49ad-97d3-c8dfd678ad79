import { ReactComponent as Contract } from '@/assets/contract.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as redeemProcess from '@/queries/redeemProcess';
import * as subscribeProcess from '@/queries/subscribeProcess';
import * as supplementProcess from '@/queries/supplementProcess';
import { Badge, Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const { data: subscribeAmount } = subscribeProcess.activeAmount();
  const { data: redeemAmount } = redeemProcess.activeAmount();
  const { data: supplementAmount } = supplementProcess.activeAmount();

  const amount =
    (subscribeAmount || 0) + (redeemAmount || 0) + (supplementAmount || 0);

  return (
    <Space
      direction="vertical"
      align="center"
      onClick={() => navigate('/secondary/contract')}
    >
      <div className="bg-white shadow-custom h-20 w-20 rounded-3xl flex justify-center items-center">
        <Badge content={amount || undefined}>
          <SvgWrapper component={Contract} />
        </Badge>
      </div>
      <span>电子签</span>
    </Space>
  );
};
