import { ReactComponent as Appropriateness } from '@/assets/appropriateness.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/appropriatenessProcess';
import { Badge, Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();

  const ownQuery = query.own();
  const { item } = ownQuery.data || {};

  const active = !ownQuery.isLoading && (!item || item.status === 'active');

  return (
    <Space
      direction="vertical"
      align="center"
      onClick={() => navigate('/secondary/appropriateness')}
    >
      <div className="bg-white shadow-custom h-20 w-20 rounded-3xl flex justify-center items-center">
        <Badge content={active ? '1' : undefined}>
          <SvgWrapper component={Appropriateness} />
        </Badge>
      </div>
      <span>适当性</span>
    </Space>
  );
};
