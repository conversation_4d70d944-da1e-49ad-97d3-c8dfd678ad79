import * as query from '@/queries/ta';
import { toCNY, toInt } from '@/utils/number';
import { Button, Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const { data } = query.totalAsset();

  return (
    <div className="rounded-4xl shadow-custom bg-white p-8">
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <Space block direction="vertical">
          <span className="text-lg font-bold">总资产</span>
          <span
            className="text-2xl font-bold leading-none"
            style={{ color: 'var(--adm-color-primary)' }}
          >
            {toCNY(data?.asset) || '-'}
          </span>
        </Space>

        <Space style={{ '--gap-horizontal': '1rem' }}>
          <span>持有产品数量</span>
          <span style={{ color: 'var(--adm-color-primary)' }}>
            {toInt(data?.portfolioAmount)}
          </span>
        </Space>

        <Button
          block
          color="primary"
          shape="rounded"
          onClick={() => navigate('/main/holding')}
        >
          持仓资产详情
        </Button>
      </Space>
    </div>
  );
};
