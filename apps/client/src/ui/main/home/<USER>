import { ReactComponent as Notification } from '@/assets/notification.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/notification';
import { Badge, Space } from 'antd-mobile';
import { useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const { data } = query.unreadAmount();

  return (
    <Space
      direction="vertical"
      align="center"
      onClick={() => navigate('/secondary/notification')}
    >
      <div className="bg-white shadow-custom h-20 w-20 rounded-3xl flex justify-center items-center">
        <Badge content={data?.amount || undefined}>
          <SvgWrapper component={Notification} />
        </Badge>
      </div>
      <span>通知</span>
    </Space>
  );
};
