import { useParams } from "umi";
import { Divider, CapsuleTabs, <PERSON><PERSON>, Picker<PERSON>iew } from "antd-mobile";
import { useState } from "react";

export default () => {
    const params = useParams();
    const id = params.id ? parseInt(params.id) : undefined;
    const [activeKey, setActiveKey] = useState('performance')

    const basicColumns = [
        [
            { label: '一月', value: 'Mon' },
            { label: '二月', value: 'Tues' },
            { label: '三月', value: 'Wed' },
            { label: '四月', value: 'Thur' },
            { label: '五月', value: 'Fri' },
            { label: "六月", value: "six" },
            { label: "七月", value: "seven" },
            { label: "八月", value: "eight" },
            { label: "九月", value: "nine" },
            { label: "十月", value: "ten" },
            { label: "十一月", value: "eleven" },
            { label: "十二月", value: "twelve" },
        ],
        [
            { label: '上午', value: 'am' },
            { label: '下午', value: 'pm' },
        ],
    ];

    const renderContent = () => {
        switch (activeKey) {
            case 'performance':
                return <>
                    业绩分析
                </>;
            case 'disclosure':
                return <>
                    信披备份
                </>;
            default:
                return null;
        }
    }

    return (
        <>
            <Divider
                style={{
                    color: '#1677ff',
                    borderColor: '#1677ff',
                    borderStyle: 'dashed',
                }}
            >
                定期报告
            </Divider>

            <CapsuleTabs activeKey={activeKey} onChange={setActiveKey}>
                <CapsuleTabs.Tab title='业绩分析' key='performance' />
                <CapsuleTabs.Tab title='信披备份' key='disclosure' />
            </CapsuleTabs>

            <Tabs>
                <Tabs.Tab
                    title='周报'
                    key='weekly'
                    disabled={activeKey === 'disclosure'}
                />
                <Tabs.Tab title='月报' key='monthly' />
                <Tabs.Tab
                    title='季报'
                    key='quarterly'
                    disabled={activeKey === 'performance'}
                />
                <Tabs.Tab title='年报' key='yearly' />
            </Tabs>
            <PickerView columns={basicColumns} />
            {renderContent()}
        </>
    )
}