import * as query from '@/queries/register';
import { toastSuccess } from '@/utils/antd';
import { Button, Form, Grid, Input, Space } from 'antd-mobile';
import { guideDialog } from '../tos/guide';
import { processStore } from './store';

export default () => {
  const [form] = Form.useForm();

  const { trigger, isMutating } = query.codeValidate();

  const onFinish = async () => {
    const { code } = form.getFieldsValue();

    await trigger({ data: { code } });
    toastSuccess('提交成功');
    processStore.next();
  };

  return (
    <Form
      className="w-full"
      mode="card"
      form={form}
      onFinish={onFinish}
      footer={
        <Space block direction="vertical" style={{ '--gap-vertical': '1rem' }}>
          <Grid columns={2} gap={24}>
            <Grid.Item>
              <Button
                shape="rounded"
                className="w-full flex-1"
                size="large"
                onClick={processStore.prev}
              >
                上一步
              </Button>
            </Grid.Item>
            <Grid.Item>
              <Button
                className="w-full flex-1"
                color="primary"
                fill="solid"
                type="submit"
                shape="rounded"
                size="large"
                loading={isMutating}
              >
                下一步
              </Button>
            </Grid.Item>
          </Grid>
          <Space block justify="center">
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={() => guideDialog.show()}
            >
              查看登录注册指引
            </a>
          </Space>
        </Space>
      }
    >
      <Form.Header>审核</Form.Header>
      <Form.Item label="关联码" name="code" rules={[{ required: true }]}>
        <Input placeholder="请输入 8 位关联码" clearable maxLength={8} />
      </Form.Item>
    </Form>
  );
};
