import * as query from '@/queries/register';
import { toastError, toastSuccess } from '@/utils/antd';
import { useTimer } from '@/utils/timer';
import { phone } from '@portfolio-service/schema/utils';
import { Button, DotLoading, Form, Grid, Input, Space } from 'antd-mobile';
import { z } from 'zod';
import { guideDialog } from '../tos/guide';
import { processStore } from './store';

export default () => {
  const [form] = Form.useForm();

  const { trigger, isMutating } = query.register();

  const onFinish = async () => {
    const { name, code } = form.getFieldsValue();

    let data: Parameters<typeof trigger>[0]['data'] | undefined = undefined;
    if (z.string().email().safeParse(name).success) {
      data = { type: 'email', code, email: name };
    } else if (phone.safeParse(name).success) {
      data = { type: 'phone', code, phone: name };
    }

    if (!data) {
      toastError('格式不正确');
      return;
    }

    await trigger({ data });
    toastSuccess('提交成功');
  };

  return (
    <Form
      className="w-full"
      mode="card"
      form={form}
      onFinish={onFinish}
      footer={
        <Space block direction="vertical" style={{ '--gap-vertical': '1rem' }}>
          <Grid columns={2} gap={24}>
            <Grid.Item>
              <Button
                shape="rounded"
                className="w-full flex-1"
                size="large"
                onClick={processStore.prev}
              >
                上一步
              </Button>
            </Grid.Item>
            <Grid.Item>
              <Button
                shape="rounded"
                className="w-full flex-1"
                color="primary"
                fill="solid"
                type="submit"
                size="large"
                loading={isMutating}
              >
                提交
              </Button>
            </Grid.Item>
          </Grid>
          <Space block justify="center">
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={() => guideDialog.show()}
            >
              查看登录注册指引
            </a>
          </Space>
        </Space>
      }
    >
      <Form.Header>注册</Form.Header>
      <Form.Item label="手机号/邮箱" name="name" rules={[{ required: true }]}>
        <Input placeholder="请输入" clearable />
      </Form.Item>
      <Form.Item
        label="验证码"
        name="code"
        rules={[{ required: true }]}
        extra={<SendCodeButton form={form} />}
      >
        <Input placeholder="请输入" clearable maxLength={6} />
      </Form.Item>
    </Form>
  );
};

const SendCodeButton = ({
  form,
}: {
  form: ReturnType<typeof Form.useForm>[0];
}) => {
  const { trigger, isMutating } = query.sendCode();
  const timer = useTimer();

  const sendCode = async () => {
    await form.validateFields(['name']);
    const name = form.getFieldValue('name');
    let data: Parameters<typeof trigger>[0]['data'] | undefined = undefined;

    if (z.string().email().safeParse(name).success) {
      data = { type: 'email', email: name };
    } else if (phone.safeParse(name).success) {
      data = { type: 'phone', phone: name };
    }

    if (!data) {
      toastError('格式不正确');
      return;
    }

    await trigger({ data });
    toastSuccess('发送成功');
    timer.start(60);
  };

  if (isMutating) {
    return <DotLoading />;
  }

  if (timer.status === 'active') {
    return <span>再次发送（{timer.countDown}）</span>;
  }

  return (
    <a style={{ color: 'var(--adm-color-primary)' }} onClick={sendCode}>
      <span>发送验证码</span>
    </a>
  );
};
