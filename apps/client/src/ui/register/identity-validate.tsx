import Checkbox from '@/components/checkbox';
import PickerFormItem from '@/components/picker-form-item';
import * as query from '@/queries/register';
import TOSButtons from '@/ui/tos/tos-buttons';
import { toastSuccess } from '@/utils/antd';
import {
  individualIdentityTypeEnum,
  organizationIdentityTypeEnum,
} from '@portfolio-service/schema/userType';
import { Button, Form, Input, Modal, Space } from 'antd-mobile';
import first from 'lodash/first';
import React, { useEffect } from 'react';
import { useNavigate } from 'umi';
import { guideDialog } from '../tos/guide';
import { processStore } from './store';

export default () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [userType] = Form.useWatch('userType', form) || [];

  const registerUser = query.user().data;

  const identityTypes =
    userType === '个人'
      ? individualIdentityTypeEnum
      : organizationIdentityTypeEnum;

  const { trigger, isMutating } = query.identityValidate();

  const checkCheckbox = () => {
    return new Promise<boolean>((resolve) => {
      if (form.getFieldValue('agreeTos')) {
        resolve(true);
        return;
      }

      Modal.show({
        title: (
          <span>
            <span>请阅读并同意</span>
            <TOSButtons />
          </span>
        ),
        bodyStyle: {
          borderRadius: '32px',
          padding: '1.5rem 1.5rem 1rem 1.5rem',
        },
        closeOnAction: true,
        actions: [
          {
            key: 'confirm',
            text: '同意',
            primary: true,
            style: { borderRadius: '9999px' },
            onClick: () => {
              form.setFieldValue('agreeTos', true);
              resolve(true);
            },
          },
          {
            key: 'cancel',
            text: '不同意',
            onClick: () => resolve(false),
          },
        ],
      });
    });
  };

  const onFinish = async () => {
    if (!(await checkCheckbox())) return;

    const {
      userType,
      identityType,
      representativeIdentityType,
      agreeTos,
      ...rest
    } = form.getFieldsValue();

    await trigger({
      data: {
        userType: first(userType),
        identityType: first(identityType),
        representativeIdentityType: first(representativeIdentityType),
        ...rest,
      },
    });
    toastSuccess('提交成功');
    processStore.next();
  };

  useEffect(() => {
    if (!registerUser) {
      return;
    }

    const { userType, identityType, representativeIdentityType, ...rest } =
      registerUser;

    form.setFieldsValue(rest);
    form.setFields([
      { name: 'userType', value: [userType] },
      { name: 'identityType', value: [identityType] },
    ]);
    if (!!representativeIdentityType) {
      form.setFieldValue('representativeIdentityType', [
        representativeIdentityType,
      ]);
    }
  }, [registerUser]);

  return (
    <Form
      className="w-full"
      mode="card"
      form={form}
      onFinish={onFinish}
      initialValues={{ agreeTos: true }}
      footer={
        <Space block direction="vertical" style={{ '--gap-vertical': '1rem' }}>
          <Button
            block
            color="primary"
            fill="solid"
            type="submit"
            size="large"
            shape="rounded"
            loading={isMutating}
          >
            下一步
          </Button>
          <Space block justify="center">
            <span>已有账号？</span>
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={() => navigate('/auth')}
            >
              登录
            </a>
          </Space>
          <Space block justify="center">
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={() => guideDialog.show()}
            >
              查看登录注册指引
            </a>
          </Space>
        </Space>
      }
    >
      <Form.Header>身份认证</Form.Header>
      <PickerFormItem
        label="类型"
        name="userType"
        rules={[{ required: true }]}
        pickerProps={{
          columns: [
            [
              { label: '个人', value: '个人' },
              { label: '机构', value: '机构' },
            ],
          ],
        }}
      />

      {!!userType && (
        <React.Fragment>
          <Form.Item label="名称" name="name" rules={[{ required: true }]}>
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <PickerFormItem
            label="证件类型"
            name="identityType"
            rules={[{ required: true }]}
            pickerProps={{
              columns: [identityTypes.map((it) => ({ label: it, value: it }))],
            }}
          />
          <Form.Item
            label="证件号码"
            name="identityNumber"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
        </React.Fragment>
      )}

      {userType === '机构' && (
        <React.Fragment>
          <Form.Item
            label="法人名称"
            name="representativeName"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <PickerFormItem
            label="法人证件类型"
            name="representativeIdentityType"
            rules={[{ required: true }]}
            pickerProps={{
              columns: [
                individualIdentityTypeEnum.map((it) => ({
                  label: it,
                  value: it,
                })),
              ],
            }}
          />

          <Form.Item
            label="法人证件号码"
            name="representativeIdentityNumber"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
        </React.Fragment>
      )}
      <Form.Item name="agreeTos">
        <Checkbox>
          <span className="text-xs" style={{ color: 'var(--adm-color-weak)' }}>
            <span>我已阅读并同意</span>
            <TOSButtons />
          </span>
        </Checkbox>
      </Form.Item>
    </Form>
  );
};
