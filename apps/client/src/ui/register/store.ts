import { $produce } from '@/utils/immer';
import { createAtom } from '@/utils/zustand';

type RegisterState = 'identityValidate' | 'codeValidate' | 'register';

type RegisterProcess = {
  state: RegisterState;
  data: Partial<Record<RegisterState, any>>;
};

const createProcessStore = (initialValues: RegisterProcess) => {
  const processAtom = createAtom<RegisterProcess>(initialValues);

  const reset = () =>
    processAtom.store.setState(
      $produce((draft) => (draft.state = 'identityValidate')),
    );

  const next = () => {
    const { state } = processAtom.store.getState();

    switch (state) {
      case 'identityValidate':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'codeValidate')),
        );
        break;
      case 'codeValidate':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'register')),
        );
        break;
    }
  };

  const prev = () => {
    const { state } = processAtom.store.getState();

    switch (state) {
      case 'codeValidate':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'identityValidate')),
        );
        break;
      case 'register':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'codeValidate')),
        );
        break;
    }
  };

  const useState = () => {
    return processAtom.use(({ state }) => state);
  };

  const setState = (state: RegisterState) => {
    processAtom.store.setState($produce((draft) => (draft.state = state)));
  };

  const useStep = () => {
    return processAtom.use(({ state }) => {
      switch (state) {
        case 'identityValidate':
          return 0;
        case 'codeValidate':
          return 1;
        case 'register':
          return 2;
      }
    });
  };

  const useData = <T>(): T | undefined => {
    return processAtom.use(({ data, state }) => data[state]);
  };

  const setData = (data: any) => {
    processAtom.store.setState(
      $produce((draft) => (draft.data[draft.state] = data)),
    );
  };

  const getData = <T>(): T | undefined => {
    const { state, data } = processAtom.store.getState();
    return data[state];
  };

  return {
    ...processAtom,
    reset,
    next,
    prev,
    useState,
    setState,
    useStep,
    useData,
    setData,
    getData,
  };
};

export const processStore = createProcessStore({
  state: 'identityValidate',
  data: {},
});
