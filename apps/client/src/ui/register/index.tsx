import * as query from '@/queries/register';
import { toastError } from '@/utils/antd';
import { useEffect } from 'react';
import CodeValidate from './code-validate';
import IdentityValidate from './identity-validate';
import Register from './register';
import RegisterStep from './register-step';
import { processStore } from './store';

export default () => {
  const state = processStore.useState();
  const registerUser = query.user().data;

  useEffect(() => {
    if (!registerUser) {
      return;
    }

    if (registerUser.redoIdentityVerification) {
      toastError('身份认证审核未通过，请重新填写认证信息');
      return;
    }

    processStore.setState(
      registerUser.validationStatus ? 'register' : 'codeValidate',
    );
  }, [registerUser]);

  useEffect(() => {
    return () => {
      processStore.reset();
    };
  }, []);

  return (
    <div className="h-full py-10">
      <div>
        <RegisterStep />
      </div>
      <div
        className="flex justify-center items-center px-6"
        style={{ height: 'calc(100% - 3.84375rem)' }}
      >
        {state === 'identityValidate' && <IdentityValidate />}
        {state === 'codeValidate' && <CodeValidate />}
        {state === 'register' && <Register />}
      </div>
    </div>
  );
};
