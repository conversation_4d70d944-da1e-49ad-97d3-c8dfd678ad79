import * as subscribeProcessQuery from '@/queries/subscribeProcess';
import * as ttd from '@/queries/ttd';
import { toastSuccess } from '@/utils/antd';
import { isProdEnv, isWechatEnv } from '@/utils/env';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, Modal } from 'antd-mobile';
import { useContext } from 'react';
import { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';
import { ContractDetailContext } from './context';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const { videoConfigTargetCode } = data || {};

  const { mutate } = useSWRConfig();

  const mutation = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { id: number } }) => {
      await trpc.subscribeProcess.videoRecord.mutate({ id: arg.id });
    },
    {
      onSuccess: () => {
        mutate(subscribeProcessQuery.queryKeyPredicate);
        mutate(ttd.queryKeyPredicate);
        toastSuccess('确认成功');
      },
    },
  );

  const linkQuery = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { targetCode: string } }) => {
      const response = await trpc.videoConfig.linkByTargetCode.query({
        targetCode: arg.targetCode,
      });
      return response.link;
    },
    {
      onSuccess: (link) => {
        const listener = () => {
          if (document.hidden) return;

          document.removeEventListener('visibilitychange', listener);

          setTimeout(() => {
            Modal.show({
              title: '确定已完成双录？',
              bodyStyle: {
                borderRadius: '32px',
                padding: '1.5rem 1.5rem 1rem 1.5rem',
              },
              closeOnAction: true,
              actions: [
                {
                  key: 'confirm',
                  text: '确定',
                  primary: true,
                  style: { borderRadius: '9999px' },
                  onClick: () => mutation.trigger({ id: data.id }),
                },
                {
                  key: 'cancel',
                  text: '再次双录',
                  onClick: () => {
                    document.addEventListener('visibilitychange', listener);
                    location.href = link;
                  },
                },
              ],
            });
          }, 1000);
        };

        document.addEventListener('visibilitychange', listener);
        location.href = link;

        if (!isProdEnv() && !isWechatEnv()) {
          listener();
        }
      },
    },
  );

  return (
    <Button
      size="small"
      color="primary"
      shape="rounded"
      disabled={!videoConfigTargetCode || !!data.videoRecordStatus}
      loading={linkQuery.isMutating}
      onClick={() => linkQuery.trigger({ targetCode: videoConfigTargetCode! })}
    >
      前往双录
    </Button>
  );
};
