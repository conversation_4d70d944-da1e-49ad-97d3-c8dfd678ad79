import { Card, Space } from 'antd-mobile';
import { FileOutline } from 'antd-mobile-icons';
import { RouterOutput } from 'server/typings';
import { ContractDetailContext } from './context';
import InnerDetail from './inner-detail';

type Props = {
  data: RouterOutput['subscribeProcess']['ownList']['items'][number];
};

const statusTransform: Record<Props['data']['status'], string> = {
  active: '进行中',
  done: '已签署',
  error: '出错',
  stopped: '已中止',
};

export default ({ data }: Props) => {
  const status = data.tradeApplyProcess?.status || data.status;

  return (
    <ContractDetailContext.Provider value={data}>
      <Card
        title={
          <Space align="center">
            <FileOutline />
            <span>{statusTransform[status]}</span>
          </Space>
        }
      >
        <InnerDetail />
      </Card>
    </ContractDetailContext.Provider>
  );
};
