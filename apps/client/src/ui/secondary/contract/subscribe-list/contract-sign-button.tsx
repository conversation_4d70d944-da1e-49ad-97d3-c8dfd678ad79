import { SignButton } from '@/components/sign-button';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { useContext } from 'react';
import useSWRMutation from 'swr/mutation';
import { ContractDetailContext } from './context';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const { id, orderNumber } = data;

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      { arg }: { arg: { id: number; orderNumber: string; password: string } },
    ) => {
      await trpc.signCheck.create.mutate({
        entityId: arg.id,
        type: 'subscribe',
      });

      const { id, orderNumber, password } = arg;
      const redirectUrl = `${location.origin}/secondary/contract/sign-redirect/subscribe/contract/${id}`;

      const { url } = await trpc.ttdUrl.batchSignUrl.query({
        orderNumber,
        redirectUrl,
        password,
      });

      return url;
    },
    { onSuccess: (url) => (location.href = url) },
  );

  return (
    <SignButton
      from="contract"
      size="small"
      color="primary"
      shape="rounded"
      disabled={id == null || !orderNumber || !!data.contractStatus}
      loading={linkQuery.isMutating}
      onSubmit={(password) =>
        linkQuery.trigger({ id, orderNumber: orderNumber!, password })
      }
    >
      前往签署
    </SignButton>
  );
};
