import { Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { useContext } from 'react';
import { ContractDetailContext } from './context';
import ContractViewButton from './contract-view-button';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const { updateTime } = data;
  const date = updateTime ? dayjs(updateTime).format('YYYY-MM-DD') : '-年-月-日';

  return (
    <Space block justify="between" align="center">
      <span>{`${date} 签署`}</span>
      <ContractViewButton />
    </Space>
  );
};
