import { toastError } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, ButtonProps } from 'antd-mobile';
import React, { useContext } from 'react';
import useSWRMutation from 'swr/mutation';
import { history } from 'umi';
import { ContractDetailContext } from './context';

export default (props: ButtonProps) => {
  const data = useContext(ContractDetailContext)!;

  const linkQuery = useSWRMutation(
    voidKey,
    (_, { arg }: { arg: { id: number } }) => {
      return trpc.subscribeProcess.contractLinkById.query({
        id: arg.id,
      });
    },
    {
      onSuccess: ({ url, fileUploadId }) => {
        if (!!url) {
          location.href = url;
        } else if (!!fileUploadId) {
          history.push(`/file-viewer?id=${fileUploadId}`);
        } else {
          toastError('未找到文件');
        }
      },
    },
  );

  if (data.type !== '首次申购') {
    return <React.Fragment />;
  }

  return (
    <Button
      size="small"
      color="primary"
      shape="rounded"
      loading={linkQuery.isMutating}
      onClick={() => linkQuery.trigger({ id: data.id })}
      {...props}
    >
      查看合同
    </Button>
  );
};
