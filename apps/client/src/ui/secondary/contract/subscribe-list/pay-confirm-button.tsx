import * as subscribeProcessQuery from '@/queries/subscribeProcess';
import { toastSuccess } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, Modal } from 'antd-mobile';
import { useContext } from 'react';
import { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';
import { ContractDetailContext } from './context';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const { mutate } = useSWRConfig();

  const mutation = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { id: number } }) => {
      await trpc.subscribeProcess.pay.mutate({ id: arg.id });
    },
    {
      onSuccess: () => {
        mutate(subscribeProcessQuery.queryKeyPredicate);
        toastSuccess('确认成功');
      },
    },
  );

  return (
    <Button
      size="small"
      color="primary"
      shape="rounded"
      onClick={() =>
        Modal.show({
          bodyStyle: {
            borderRadius: '32px',
            padding: '1.5rem 1.5rem 1rem 1.5rem',
          },
          closeOnAction: true,
          actions: [
            {
              key: 'confirm',
              text: '确定',
              primary: true,
              style: { borderRadius: '9999px' },
              onClick: () => mutation.trigger({ id: data.id }),
            },
            {
              key: 'cancel',
              text: '取消',
            },
          ],
          title: '确认已打款？',
        })
      }
    >
      确认打款
    </Button>
  );
};
