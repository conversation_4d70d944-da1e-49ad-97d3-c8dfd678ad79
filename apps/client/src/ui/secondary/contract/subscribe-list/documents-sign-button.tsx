import { SignButton } from '@/components/sign-button';
import { useTtdUserNo } from '@/utils/account';
import { toastError } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { useContext } from 'react';
import useSWRMutation from 'swr/mutation';
import { ContractDetailContext } from './context';

export default () => {
  const userNo = useTtdUserNo();

  const data = useContext(ContractDetailContext)!;

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      { arg }: { arg: { id: number; userNo: string; password: string } },
    ) => {
      const fileIds = await trpc.subscribeProcess.ttdFileIdsById.query({
        id: arg.id,
      });
      if (!fileIds?.length) {
        toastError('未找到需要签署的文件');
        return;
      }

      await trpc.signCheck.create.mutate({
        entityId: arg.id,
        type: 'subscribe',
      });

      const redirectUrl = `${location.origin}/secondary/contract/sign-redirect/subscribe/documents/${arg.id}`;
      const { url } = await trpc.ttdUrl.batchSignUrl.query({
        fileIds,
        redirectUrl,
        password: arg.password,
      });
      return url;
    },
    { onSuccess: (url) => !!url && (location.href = url) },
  );

  return (
    <SignButton
      from="contract"
      size="small"
      color="primary"
      shape="rounded"
      disabled={!userNo || !!data.documentsStatus}
      loading={linkQuery.isMutating}
      onSubmit={(password) =>
        linkQuery.trigger({ id: data.id, userNo: userNo!, password })
      }
    >
      前往签署
    </SignButton>
  );
};
