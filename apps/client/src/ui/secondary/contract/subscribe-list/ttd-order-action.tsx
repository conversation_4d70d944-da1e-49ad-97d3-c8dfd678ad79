import space from '@/styles/space.less';
import { Space } from 'antd-mobile';
import { CheckCircleOutline, ClockCircleOutline } from 'antd-mobile-icons';
import { useContext } from 'react';
import { ContractDetailContext } from './context';
import DocumentsSignButton from './documents-sign-button';
import VideoRecordButton from './video-record-button';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const showVideoRecord = !!data.videoConfigTargetCode;

  return (
    <Space block direction="vertical">
      <Space block justify="between" className={space.wrap}>
        <Space align="center">
          {!!data.documentsStatus ? (
            <CheckCircleOutline color="var(--adm-color-success)" />
          ) : (
            <ClockCircleOutline color="var(--adm-color-warning)" />
          )}
          <span>交易申请、风险匹配告知</span>
        </Space>
        <DocumentsSignButton />
      </Space>
      {showVideoRecord && (
        <Space block justify="between" className={space.wrap}>
          <Space align="center">
            {!!data.videoRecordStatus ? (
              <CheckCircleOutline color="var(--adm-color-success)" />
            ) : (
              <ClockCircleOutline color="var(--adm-color-warning)" />
            )}

            <span>双录</span>
          </Space>
          <VideoRecordButton />
        </Space>
      )}
    </Space>
  );
};
