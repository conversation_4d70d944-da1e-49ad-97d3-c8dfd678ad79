import { Space } from 'antd-mobile';
import React, { useContext } from 'react';
import TradeApplySignButton from '../trade-apply-sign-button';
import { ContractDetailContext } from './context';
import ContractSignButton from './contract-sign-button';
import ContractViewButton from './contract-view-button';
import PayConfirmButton from './pay-confirm-button';
import TtdOrderAction from './ttd-order-action';

export default () => {
  const data = useContext(ContractDetailContext)!;

  if (!!data.tradeApplyProcess) {
    switch (data.tradeApplyProcess.state) {
      case 'tradeApplySign':
      case 'tradeApplySignRedo':
        return (
          <Space block justify="end">
            <TradeApplySignButton id={data.tradeApplyProcessId} />
          </Space>
        );
    }

    return <React.Fragment />;
  }

  switch (data.state) {
    case 'ttdOrder':
    case 'ttdOrderRedo':
      return <TtdOrderAction />;

    case 'contractSign':
    case 'contractSignRedo':
      return (
        <Space block justify="end">
          <ContractSignButton />
        </Space>
      );

    case 'pay':
      return (
        <Space block justify="end">
          <ContractViewButton color="default" />
          <PayConfirmButton />
        </Space>
      );
    case 'payReview':
      return (
        <Space block justify="end">
          <ContractViewButton />
        </Space>
      );
  }

  return <React.Fragment />;
};
