import * as query from '@/queries/subscribeProcess';
import { usePagination } from '@/utils/pagination';
import { InfiniteScroll, List, Space, Tag } from 'antd-mobile';
import React from 'react';
import { RouterOutput } from 'server/typings';
import ContractDetail from './contract-detail';

const pageSize = 10;

export default () => {
  const { page, prevPages, useHelpers, nextPage } = usePagination({ pageSize });

  const listQuery = query.list({ page, pageSize });
  const { data } = listQuery;

  const { hasMore } = useHelpers(data);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <React.Fragment>
      <List mode="card" header="合同列表">
        {prevPages.map((page) => (
          <Page key={page} params={{ page, pageSize }} />
        ))}
        {data?.items?.map((it, index) => (
          <ListItem key={index + prevPages.length} data={it} />
        ))}
      </List>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['subscribeProcess']['ownList']['items'][number];
}) => {
  return (
    <List.Item>
      <Space block direction="vertical">
        <Space>{data.portfolioName}</Space>
        <Space block align="center" className="leading-none">
          <span className="leading-none">{data.portfolioNo}</span>
          <Tag color="primary" className="rounded-full">{data.type}</Tag>
        </Space>
        <ContractDetail data={data} />
      </Space>
    </List.Item>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data } = query.list(params);

  return (
    <React.Fragment>
      {data?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </React.Fragment>
  );
};