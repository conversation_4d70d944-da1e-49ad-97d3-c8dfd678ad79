import { toCNY } from '@/utils/number';
import { Type } from '@portfolio-service/schema/subscribeProcess';
import { Space } from 'antd-mobile';
import { useContext } from 'react';
import ActiveAction from './active-action';
import { ContractDetailContext } from './context';

export const getStateText = (state: string, type: Type) => {
  switch (state) {
    case 'ttdOrder':
    case 'ttdOrderRedo':
      return type === '首次申购' ? '双录、签署材料' : '签署材料';

    case 'ttdOrderReview':
      return type === '首次申购' ? '双录、材料审核中' : '材料审核中';

    case 'contractSign':
    case 'contractSignRedo':
      return '合同签署';

    case 'contractReview':
      return '合同审核中';

    case 'pay':
      return '打款';

    case 'payReview':
      return '打款审核中';

    case 'tradeApplySign':
      return '签署交易申请书';
    case 'tradeApplySignRedo':
      return '签署交易申请书';
    case 'tradeApplyReview':
      return '交易申请书审核中';
  }
};

export default () => {
  const data = useContext(ContractDetailContext)!;
  const state = data.tradeApplyProcess?.state || data.state;
  const type: Type = data.tradeApplyProcess ? '追加申购' : data.type;

  return (
    <Space block direction="vertical">
      <span>
        <span>当前进度：</span>
        <span>{getStateText(state, type)}</span>
        {data.state === 'pay' && <span>，金额：{toCNY(data.amount)}</span>}
      </span>
      <ActiveAction />
    </Space>
  );
};
