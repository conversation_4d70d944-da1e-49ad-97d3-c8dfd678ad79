import RedeemList from './redeem-list';
import SubscribeList from './subscribe-list';
import SupplementList from './supplement-list';
import Tabs, { tabAtom } from './tabs';

export default () => {
  const tab = tabAtom.use();

  return (
    <div>
      <Tabs />
      {tab === '认申购单' && <SubscribeList />}
      {tab === '赎回单' && <RedeemList />}
      {tab === '补充协议' && <SupplementList />}
    </div>
  );
};
