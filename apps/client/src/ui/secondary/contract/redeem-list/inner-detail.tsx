import { useContext } from 'react';
import Active from './active';
import { ContractDetailContext } from './context';
import Done from './done';
import Error from './error';
import Stopped from './stopped';

export default () => {
  const data = useContext(ContractDetailContext)!;
  const status = data.tradeApplyProcess?.status || data.status;

  switch (status) {
    case 'active':
      return <Active />;
    case 'done':
      return <Done />;
    case 'stopped':
      return <Stopped />;
  }

  return <Error />;
};
