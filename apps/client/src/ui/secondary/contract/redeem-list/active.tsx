import { Space } from 'antd-mobile';
import { useContext } from 'react';
import ActiveAction from './active-action';
import { ContractDetailContext } from './context';

const stateTransform: Record<string, string> = {
  documentsSign: '签署材料',
  documentsSignRedo: '签署材料',
  documentsReview: '材料审核中',
  tradeApplySign: '签署交易申请书',
  tradeApplySignRedo: '签署交易申请书',
  tradeApplyReview: '交易申请书审核中',
};

export default () => {
  const data = useContext(ContractDetailContext)!;
  const state = data.tradeApplyProcess?.state || data.state;

  return (
    <Space block direction="vertical">
      <span>
        <span>当前进度：</span>
        <span>{stateTransform[state]}</span>
      </span>
      <ActiveAction />
    </Space>
  );
};
