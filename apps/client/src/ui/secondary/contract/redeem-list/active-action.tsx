import { Space } from 'antd-mobile';
import React, { useContext } from 'react';
import TradeApplySignButton from '../trade-apply-sign-button';
import { ContractDetailContext } from './context';
import DocumentsSignButton from './documents-sign-button';

export default () => {
  const data = useContext(ContractDetailContext);

  if (!!data?.tradeApplyProcess) {
    switch (data.tradeApplyProcess.state) {
      case 'tradeApplySign':
      case 'tradeApplySignRedo':
        return (
          <Space block justify="end">
            <TradeApplySignButton id={data.tradeApplyProcessId} />
          </Space>
        );
    }

    return <React.Fragment />;
  }

  switch (data?.state) {
    case 'documentsSign':
    case 'documentsSignRedo':
      return (
        <Space block justify="end">
          <DocumentsSignButton />
        </Space>
      );
  }

  return <React.Fragment />;
};
