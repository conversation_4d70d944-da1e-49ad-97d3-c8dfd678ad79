import { Space } from 'antd-mobile';
import React, { useContext } from 'react';
import { ContractDetailContext } from './context';
import SupplementSignButton from './supplement-sign-button';

export default () => {
  const data = useContext(ContractDetailContext)!;

  switch (data.state) {
    case 'supplementSign':
    case 'supplementSignRedo':
      return (
        <Space block justify="end">
          <SupplementSignButton />
        </Space>
      );
  }

  return <React.Fragment />;
};
