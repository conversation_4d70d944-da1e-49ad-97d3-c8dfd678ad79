import { Space } from 'antd-mobile';
import { useContext } from 'react';
import ActiveAction from './active-action';
import { ContractDetailContext } from './context';

const stateTransform: Record<string, string> = {
  supplementSign: '签署补充协议',
  supplementSignRedo: '签署补充协议',
  supplementReview: '补充协议审核中',
};

export default () => {
  const data = useContext(ContractDetailContext)!;

  return (
    <Space block direction="vertical">
      <span>
        <span>当前进度：</span>
        <span>{stateTransform[data.state]}</span>
      </span>
      <ActiveAction />
    </Space>
  );
};
