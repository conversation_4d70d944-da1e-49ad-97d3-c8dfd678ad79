import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, ButtonProps } from 'antd-mobile';
import { useContext } from 'react';
import useSWRMutation from 'swr/mutation';
import { ContractDetailContext } from './context';

export default (props: ButtonProps) => {
  const data = useContext(ContractDetailContext)!;

  const linkQuery = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { id: number } }) => {
      const response = await trpc.supplementProcess.supplementLinksById.query({
        id: arg.id,
      });

      return response.urls[0];
    },
    { onSuccess: (url) => (location.href = url) },
  );

  return (
    <Button
      size="small"
      color="primary"
      shape="rounded"
      loading={linkQuery.isMutating}
      onClick={() => linkQuery.trigger({ id: data.id })}
      {...props}
    >
      查看补充协议
    </Button>
  );
};
