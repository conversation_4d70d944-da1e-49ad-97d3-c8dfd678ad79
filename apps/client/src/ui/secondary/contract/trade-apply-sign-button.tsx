import { SignButton } from '@/components/sign-button';
import { useTtdUserNo } from '@/utils/account';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import useSWRMutation from 'swr/mutation';

export default ({ id }: { id: number | null }) => {
  const userNo = useTtdUserNo();

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      { arg }: { arg: { id: number; userNo: string; password: string } },
    ) => {
      const fileIds = await trpc.tradeApplyProcess.ttdFileIdsById.query({
        id: arg.id,
      });

      const redirectUrl = `${location.origin}/secondary/contract/sign-redirect/trade-apply/${arg.id}`;
      const { url } = await trpc.ttdUrl.batchSignUrl.query({
        fileIds,
        redirectUrl,
        password: arg.password,
      });
      return url;
    },
    { onSuccess: (url) => (location.href = url) },
  );

  return (
    <SignButton
      from="contract"
      size="small"
      color="primary"
      shape="rounded"
      disabled={!userNo || !id}
      loading={linkQuery.isMutating}
      onSubmit={(password) =>
        linkQuery.trigger({ id: id!, userNo: userNo!, password })
      }
    >
      前往签署
    </SignButton>
  );
};
