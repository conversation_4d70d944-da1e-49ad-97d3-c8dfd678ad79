import * as redeemProcess from '@/queries/redeemProcess';
import * as subscribeProcess from '@/queries/subscribeProcess';
import * as supplementProcess from '@/queries/supplementProcess';
import { createAtom } from '@/utils/zustand';
import { Badge, Tabs } from 'antd-mobile';
import { useEffect } from 'react';

export const tabs = ['认申购单', '赎回单', '补充协议'] as const;
type Tab = (typeof tabs)[number];

export const tabAtom = createAtom<Tab>('认申购单');

export default () => {
  const tab = tabAtom.use();
  const { data: subscribeAmount } = subscribeProcess.activeAmount();
  const { data: redeemAmount } = redeemProcess.activeAmount();
  const { data: supplementAmount } = supplementProcess.activeAmount();

  useEffect(() => {
    tabAtom.store.setState('认申购单');
  }, []);

  return (
    <Tabs
      activeKey={tab}
      onChange={(key) => tabAtom.store.setState(key as any)}
    >
      {tabs.map((it) => {
        let badgeContent: number | undefined;
        if (it === '认申购单') {
          badgeContent = subscribeAmount;
        } else if (it === '赎回单') {
          badgeContent = redeemAmount;
        } else if (it === '补充协议') {
          badgeContent = supplementAmount;
        }

        return (
          <Tabs.Tab
            key={it}
            title={<Badge content={badgeContent || undefined}>{it}</Badge>}
          />
        );
      })}
    </Tabs>
  );
};
