import { ReactComponent as RightArrow } from '@/assets/right-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/ta/dividend';
import { toCNY } from '@/utils/number';
import { usePagination } from '@/utils/pagination';
import { stringToDate } from '@portfolio-service/schema/utils';
import { InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React from 'react';
import { RouterOutput } from 'server/typings';
import { dividendDetailDialog } from './dividend-detail';
import { filterAtom } from './filter-picker';
import { searchAtom } from './portfolio-search';

const pageSize = 10;

export default () => {
  const search = searchAtom.use();
  const dividendMethod = filterAtom.use();

  const { page, nextPage, prevPages, useHelpers } = usePagination({
    pageSize,
    resetDeps: [search, dividendMethod],
  });

  const listQuery = query.list({
    page,
    pageSize,
    portfolio: search,
    dividendMethod,
  });
  const { data: trades } = listQuery;
  const { hasMore } = useHelpers(trades);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <React.Fragment>
      <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
        {prevPages.map((page) => (
          <Page
            key={page}
            params={{ page, pageSize, portfolio: search, dividendMethod }}
          />
        ))}
        {trades?.items?.map((it, index) => (
          <ListItem key={index + prevPages.length} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['dividend']['list']['items'][number];
}) => {
  const dividendDateParsed = stringToDate.safeParse(data.dividendDate);
  const dividendDate = dividendDateParsed.success
    ? dayjs(dividendDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <div
      className="bg-white shadow-custom pl-6 py-6 rounded-4xl flex"
      onClick={() => dividendDetailDialog.show({ data })}
    >
      <div className="flex-1 flex mr-6">
        <div
          className="flex flex-col justify-center text-lg mr-6"
          style={{ color: 'var(--adm-color-primary)' }}
        >
          {data.dividendMethod?.split('')?.map((char, index) => (
            <span key={index} className="leading-tight">
              {char}
            </span>
          ))}
        </div>
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '0.75rem' }}
          className="flex-1"
        >
          <span className="font-medium text-lg">
            {data.portfolioName || '-'}
          </span>
          <Space block justify="between">
            <span
              className="text-sm font-medium"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              {toCNY(data.dividendBalance) || '-'}
            </span>
            <span
              className="text-xs"
              style={{ color: 'var(--adm-color-weak)' }}
            >
              {dividendDate || '-'}
            </span>
          </Space>
        </Space>
      </div>
      <div className="basis-6 flex items-center">
        <SvgWrapper component={RightArrow} />
      </div>
    </div>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data: dividends } = query.list(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {dividends?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </Space>
  );
};
