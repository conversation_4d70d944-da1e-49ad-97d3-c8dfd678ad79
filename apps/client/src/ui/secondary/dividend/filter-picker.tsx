import { createAtom, createPopup } from '@/utils/zustand';
import {
  DividendMethod,
  dividendMethodEnum,
  dividendMethodSchema,
} from '@portfolio-service/schema/dividend';
import { Picker, PickerProps } from 'antd-mobile';

export const filterPicker = createPopup();
export const filterAtom = createAtom<DividendMethod | undefined>(undefined);

const columns: PickerProps['columns'] = [
  [{ label: '全部', value: '' }].concat(
    dividendMethodEnum.map((it) => ({ label: it, value: it })),
  ),
];

export default () => {
  const open = filterPicker.use((state) => state.open);

  const onConfirm = (value: string | number | null) => {
    const valueParsed = dividendMethodSchema.safeParse(value?.toString());
    filterAtom.store.setState(
      valueParsed.success ? valueParsed.data : undefined,
    );

    filterPicker.hide();
  };

  return (
    <Picker
      defaultValue={[filterAtom.store.getState() || '']}
      visible={open}
      columns={columns}
      onConfirm={([value]) => onConfirm(value)}
      onCancel={() => filterPicker.hide()}
    />
  );
};
