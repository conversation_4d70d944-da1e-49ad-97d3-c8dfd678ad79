import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import { toCN, toCNY } from '@/utils/number';
import { createPopup } from '@/utils/zustand';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Divider, Popup, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React from 'react';
import { RouterOutput } from 'server/typings';

export const dividendDetailDialog =
  createPopup<RouterOutput['dividend']['list']['items'][number]>();

export default () => {
  const { open, data } = dividendDetailDialog.use();

  const dividendDateParsed = stringToDate.safeParse(data?.dividendDate);
  const dividendDate = dividendDateParsed.success
    ? dayjs(dividendDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <Popup
      visible={open}
      onClose={() => dividendDetailDialog.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: 'calc(50vh + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        onClick={() => dividendDetailDialog.hide()}
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      >
        <SvgWrapper component={DownArrow} />
      </div>
      <div className="text-lg font-medium">分红详情</div>
      <Divider className="my-2" />

      <Space
        block
        direction="vertical"
        style={{ '--gap-vertical': '1.5rem' }}
        className="mt-6"
      >
        <div className="bg-white rounded-4xl shadow-custom p-6">
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '1.5rem' }}
          >
            <span
              className="font-medium text-sm"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              分红确认
            </span>
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '0.75rem' }}
            >
              <Item label="产品名称" value={data?.portfolioName} />
              <Item label="产品代码" value={data?.portfolioNo} />
              <Item label="分红方式" value={data?.dividendMethod} />
              <Item label="分红日期" value={dividendDate} />
            </Space>
          </Space>
        </div>
        <div className="bg-white rounded-4xl shadow-custom p-6">
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '1.5rem' }}
          >
            <span
              className="font-medium text-sm"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              分红详情
            </span>
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '0.75rem' }}
            >
              <Item label="份额基数" value={toCN(data?.dividendBasicShares)} />
              <Item label="红利总金额" value={toCNY(data?.dividendBalance)} />
              <OtherDetails data={data} />
              <Item label="业绩报酬" value={toCNY(data?.performanceAmount)} />
            </Space>
          </Space>
        </div>
      </Space>
    </Popup>
  );
};

const OtherDetails = ({
  data,
}: {
  data: RouterOutput['dividend']['list']['items'][number] | undefined;
}) => {
  switch (data?.dividendMethod) {
    case '现金红利':
      return <Item label="实发红利金额" value={toCNY(data?.dividendIndeed)} />;
    case '红利再投':
      const dateParsed = stringToDate.safeParse(
        data?.reinvestmentDistributionDate,
      );
      const date = dateParsed.success
        ? dayjs(dateParsed.data).format('YYYY-MM-DD')
        : undefined;

      return (
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '0.75rem' }}
        >
          <Item label="再投资发放日期" value={date} />
          <Item label="再投资金额" value={toCNY(data?.reinvestmentBalance)} />
          <Item label="再投资份额" value={toCN(data?.reinvestmentShares)} />
          <Item label="再投资单位净值" value={toCN(data?.reinvestmentNavpu, 4)} />
        </Space>
      );

    default:
      return <React.Fragment />;
  }
};

type ItemProps = {
  label: React.ReactNode;
  value: React.ReactNode;
};

const Item = (props: ItemProps) => {
  return (
    <div className="flex justify-between text-xs">
      <span
        className="mr-6 break-keep"
        style={{ color: 'var(--adm-color-weak)' }}
      >
        {props.label}
      </span>
      <span
        className="break-all text-right"
        style={{ color: 'var(--adm-color-text-secondary)' }}
      >
        {props.value || '-'}
      </span>
    </div>
  );
};
