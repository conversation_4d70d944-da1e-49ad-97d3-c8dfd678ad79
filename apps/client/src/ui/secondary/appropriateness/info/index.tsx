import Loading from '@/components/loading';
import * as account from '@/queries/account';
import * as query from '@/queries/appropriatenessProcess/baseInfo';
import { getBirthDayFromIdentityNumber } from '@/utils/misc';
import { Space } from 'antd-mobile';

export default () => {
  const { data: user } = account.user();

  if (user?.type === '个人') {
    return <Individual />;
  }

  if (user?.type === '机构') {
    return <Organization />;
  }

  return <Loading />;
};

const Individual = () => {
  const { data: user } = account.user();
  const info = query.own().data?.item?.formData;

  const identityNumberDisplay = user?.identityNumber
    ? user.identityNumber.slice(0, 3) +
      '***********' +
      user.identityNumber.slice(14)
    : undefined;

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">基本信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="姓名" value={user?.name} />
            <Item label="性别" value={info?.gender} />
            <Item
              label="出生日期"
              value={getBirthDayFromIdentityNumber(user?.identityNumber)}
            />
            <Item label="国籍" value={info?.nationality} />
          </Space>
        </Space>
      </div>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">证件信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="证件类型" value={user?.identityType} />
            <Item label="证件号码" value={identityNumberDisplay} />
          </Space>
        </Space>
      </div>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">其他信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="职业" value={info?.profession} />
            <Item label="职务" value={info?.job} />
            <Item label="座机" value={info?.landline} />
            <Item label="移动电话" value={info?.phone} />
            <Item label="邮编" value={info?.postCode} />
            <Item label="电子邮箱" value={info?.email} />
            <Item label="住址" value={info?.residency} />
          </Space>
        </Space>
      </div>
    </Space>
  );
};

const Organization = () => {
  const { data: user } = account.user();
  const info = query.own().data?.item?.formData;

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">基本信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="机构名称" value={user?.name} />
            <Item label="机构类型" value={info?.type} />
            <Item label="机构资质证明" value={info?.endowment} />
            <Item label="资质证明编号" value={info?.endowmentNumber} />
          </Space>
        </Space>
      </div>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">证件信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="证件类型" value={user?.identityType} />
            <Item label="证件号码" value={info?.identityNumber} />
            <Item label="证件到期日" value={info?.identityExpireDate} />
          </Space>
        </Space>
      </div>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">其他信息</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="经营范围" value={info?.businessRange} />
            <Item label="注册地址" value={info?.registerAddress} />
            <Item label="办公地址" value={info?.operationAddress} />
            <Item label="注册资本（万元）" value={info?.registerAsset} />
          </Space>
        </Space>
      </div>
    </Space>
  );
};

type ItemProps = {
  label: React.ReactNode;
  value: React.ReactNode;
};

const Item = (props: ItemProps) => {
  return (
    <div className="flex justify-between">
      <span
        className="break-keep mr-6"
        style={{ color: 'var(--adm-color-weak)' }}
      >
        {props.label}
      </span>
      <span
        className="break-all text-right"
        style={{ color: 'var(--adm-color-text-secondary)' }}
      >
        {props.value || '-'}
      </span>
    </div>
  );
};
