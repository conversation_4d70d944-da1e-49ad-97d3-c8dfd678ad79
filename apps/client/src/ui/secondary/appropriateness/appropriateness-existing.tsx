import Loading from '@/components/loading';
import * as query from '@/queries/appropriatenessProcess';
import { Button, Modal, Space } from 'antd-mobile';
import AppropriatenessTabs from './appropriateness-tabs';
import ExistingFirst from './existing-first';
import ExistingSecond from './existing-second';
import { tabAtom } from './store';

export default () => {
  const ownQuery = query.own();
  const { data } = ownQuery;

  const documentsQuery = query.documentsById(data?.item?.id);
  const mutation = query.create();

  const tab = tabAtom.use();

  const onConfirm = async () => {
    await mutation.trigger();
  };

  const loading = ownQuery.isLoading || documentsQuery.isLoading;
  if (loading) {
    return <Loading />;
  }

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
      <AppropriatenessTabs />
      {tab === '特定对象认定' && <ExistingFirst />}
      {tab === '合格投资者认定' && <ExistingSecond />}
      <Button
        block
        color="primary"
        shape="rounded"
        onClick={() =>
          Modal.show({
            bodyStyle: {
              borderRadius: '32px',
              padding: '1.5rem 1.5rem 1rem 1.5rem',
            },
            closeOnAction: true,
            actions: [
              {
                key: 'confirm',
                text: '确定',
                primary: true,
                style: { borderRadius: '9999px' },
                onClick: onConfirm,
              },
              {
                key: 'cancel',
                text: '取消',
              },
            ],
            title: '确定要重新认定吗？',
          })
        }
      >
        重新认定
      </Button>
    </Space>
  );
};
