import Loading from '@/components/loading';
import * as account from '@/queries/account';
import { Button, Space } from 'antd-mobile';
import { range } from 'lodash';
import React from 'react';
import { useNavigate } from 'umi';

type Props = {
  forceNew?: boolean;
};

export default (props: Props) => {
  const userQuery = account.user();
  const { data: user, isLoading } = userQuery;

  if (props.forceNew) {
    return <New />;
  }

  if (isLoading) {
    return <Loading />;
  }

  return user?.riskLevel ? <Redo /> : <New />;
};

const New = () => {
  const navigate = useNavigate();

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">风险承受能力评估</div>
          <Button
            color="primary"
            shape="rounded"
            block
            onClick={() => navigate('/secondary/appropriateness/risk-test')}
          >
            前往测评
          </Button>
        </Space>
      </div>
    </Space>
  );
};

const Redo = () => {
  const navigate = useNavigate();

  const userQuery = account.user();
  const user = userQuery.data;

  if (!user) {
    return <React.Fragment />;
  }

  const level = parseInt((user.riskLevel as string)[1]);
  const portfolioLevels = range(0, level)
    .map((it) => `R${it + 1}`)
    .join('/');

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">风险承受能力评估结果</div>
          <Space
            block
            direction="vertical"
            justify="center"
            align="center"
            style={{ '--gap-vertical': '1.5rem' }}
          >
            <div
              className="w-24 h-24 rounded-full flex justify-center items-center"
              style={{
                background:
                  'conic-gradient(from 90deg, white, var(--adm-color-primary))',
              }}
            >
              <div className="w-16 h-16 rounded-full bg-white flex justify-center items-center">
                <span
                  className="text-2xl font-medium"
                  style={{ color: 'var(--adm-color-primary)' }}
                >
                  {user.riskLevel}
                </span>
              </div>
            </div>
            <Button
              shape="rounded"
              color="primary"
              onClick={() => navigate('/secondary/risk-test')}
            >
              重新测评
            </Button>
            <div style={{ color: 'var(--adm-color-weak)' }}>
              <span>
                您的风险承受能力为 {user.riskLevel}，适合 {portfolioLevels}
              </span>
              <span> 风险评级的基金产品。</span>
            </div>
          </Space>
        </Space>
      </div>
    </Space>
  );
};
