import { Tabs } from 'antd-mobile';
import { useEffect } from 'react';
import { tabAtom, tabs } from './store';

export default () => {
  const tab = tabAtom.use();

  useEffect(() => {
    tabAtom.store.setState('特定对象认定');
  }, []);

  return (
    <Tabs
      activeKey={tab}
      onChange={(key) => tabAtom.store.setState(key as any)}
    >
      {tabs.map((it) => (
        <Tabs.Tab key={it} title={it} />
      ))}
    </Tabs>
  );
};
