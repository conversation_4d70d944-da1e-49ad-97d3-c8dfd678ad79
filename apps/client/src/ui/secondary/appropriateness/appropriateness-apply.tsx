import Loading from '@/components/loading';
import * as query from '@/queries/appropriatenessProcess';
import React from 'react';
import ApplyClassification from './apply-classification';
import ApplyDocumentsSign from './apply-documents-sign';
import ApplyReview from './apply-review';
import ApplyRiskTest from './apply-risk-test';

export default () => {
  const ownQuery = query.own();
  const { data, isLoading } = ownQuery;

  if (isLoading) {
    return <Loading />;
  }

  if (!data?.item?.state) {
    return <ApplyClassification />;
  }

  switch (data?.item?.state) {
    case 'classification':
    case 'classificationRedo':
      return <ApplyClassification />;

    case 'riskTest':
    case 'riskTestRedo':
      return (
        <div className="mx-3 py-3">
          <ApplyRiskTest forceNew />
        </div>
      );

    case 'documentsSign':
    case 'documentsSignRedo':
      return (
        <div className="mx-3 py-3">
          <ApplyDocumentsSign />
        </div>
      );

    case 'classificationReview':
    case 'riskTestReview':
    case 'documentsReview':
      return <ApplyReview />;
  }

  return <React.Fragment />;
};
