import { SignButton } from '@/components/sign-button';
import * as query from '@/queries/appropriatenessProcess';
import { useTtdUserNo } from '@/utils/account';
import { toastError } from '@/utils/antd';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Space } from 'antd-mobile';
import useSWRMutation from 'swr/mutation';

export default () => {
  const userNo = useTtdUserNo();

  const ownQuery = query.own();
  const { item } = ownQuery.data || {};

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      { arg }: { arg: { id: number; userNo: string; password: string } },
    ) => {
      const fileIds = await trpc.appropriatenessProcess.ttdFileIdsById.query({
        id: arg.id,
      });
      if (!fileIds?.length) {
        toastError('未找到需要签署的文件');
        return;
      }

      await trpc.signCheck.create.mutate({
        entityId: arg.id,
        type: 'appropriateness',
      });

      const redirectUrl = `${location.origin}/secondary/appropriateness/sign-redirect/${arg.id}`;
      const { url } = await trpc.ttdUrl.batchSignUrl.query({
        fileIds,
        redirectUrl,
        password: arg.password,
      });
      return url;
    },
    { onSuccess: (url) => !!url && (location.href = url) },
  );

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">签署文件</div>
          <SignButton
            block
            from="appropriateness"
            color="primary"
            shape="rounded"
            disabled={!userNo}
            onSubmit={(password) =>
              linkQuery.trigger({ id: item!.id, userNo: userNo!, password })
            }
            loading={linkQuery.isMutating}
          >
            前往签署
          </SignButton>
        </Space>
      </div>
    </Space>
  );
};
