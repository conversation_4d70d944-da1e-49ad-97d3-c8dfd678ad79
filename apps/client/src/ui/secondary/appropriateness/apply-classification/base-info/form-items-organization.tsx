import DatePickerFormItem from '@/components/date-picker-form-item';
import PickerFormItem from '@/components/picker-form-item';
import * as account from '@/queries/account';
import { individualIdentityTypeEnum } from '@portfolio-service/schema/userType';
import { Form, Input, Selector } from 'antd-mobile';
import first from 'lodash/first';
import React from 'react';

export default () => {
  const userQuery = account.user();
  const { data: user } = userQuery;

  return (
    <React.Fragment>
      <Form.Item label="机构名称">
        <span>{user?.name}</span>
      </Form.Item>
      <Form.Item label="证件类型">
        <span>{user?.identityType}</span>
      </Form.Item>
      <Form.Item label="证件号码">
        <span>{user?.identityNumber}</span>
      </Form.Item>
      <Form.Item name="type" label="机构类型">
        <Input />
      </Form.Item>
      <DatePickerFormItem name="identityExpireDate" label="证件有效期至" />
      <Form.Item name="endowment" label="机构资质证明">
        <Input />
      </Form.Item>
      <Form.Item name="endowmentNumber" label="资质证明编号">
        <Input />
      </Form.Item>
      <Form.Item name="businessRange" label="经营范围">
        <Input />
      </Form.Item>
      <Form.Item name="registerAddress" label="注册地址">
        <Input />
      </Form.Item>
      <Form.Item name="operationAddress" label="办公地址">
        <Input />
      </Form.Item>
      <Form.Item name="registerAsset" label="注册资本（万元）">
        <Input />
      </Form.Item>
      <Form.Item name="controller" label="股东或实际控制人">
        <Input />
      </Form.Item>
      <Form.Item name="representativeGender" label="法人性别">
        <Selector
          columns={3}
          options={['男', '女', '其他'].map((it) => ({
            label: it,
            value: it,
          }))}
        />
      </Form.Item>
      <Form.Item name="representativeAge" label="法人年龄">
        <Input type="number" min={1} />
      </Form.Item>
      <Form.Item name="representativeJob" label="法人职务">
        <Input />
      </Form.Item>
      <Form.Item name="representativeEmail" label="法人电子邮箱">
        <Input type="email" />
      </Form.Item>
      <DatePickerFormItem
        name="representativeIdentityExpireDate"
        label="法人证件有效期至"
      />
      <Form.Item name="representativeLandline" label="法人电话-座机">
        <Input />
      </Form.Item>
      <Form.Item name="representativePhone" label="法人电话-移动电话">
        <Input />
      </Form.Item>
      <Form.Item name="representativePostCode" label="法人办公邮编">
        <Input />
      </Form.Item>
      <Form.Item name="representativeAddress" label="法人办公地址">
        <Input />
      </Form.Item>

      <Form.Item name="assigneeName" label="指定授权经办人">
        <Input />
      </Form.Item>
      <Form.Item name="assigneeGender" label="经办人性别">
        <Selector
          columns={3}
          options={['男', '女', '其他'].map((it) => ({
            label: it,
            value: it,
          }))}
        />
      </Form.Item>
      <Form.Item name="assigneeAge" label="经办人年龄">
        <Input type="number" min={1} />
      </Form.Item>
      <PickerFormItem
        label="经办人证件类型"
        name="assigneeIdentityType"
        pickerProps={{
          columns: [
            individualIdentityTypeEnum.map((it) => ({ label: it, value: it })),
          ],
        }}
      />
      <Form.Item name="assigneeIdentityNumber" label="经办人证件号码">
        <Input />
      </Form.Item>
      <Form.Item name="assigneeJob" label="经办人职务">
        <Input />
      </Form.Item>
      <Form.Item name="assigneeEmail" label="经办人电子邮箱">
        <Input type="email" />
      </Form.Item>
      <DatePickerFormItem
        name="assigneeIdentityExpireDate"
        label="经办人证件有效期至"
      />
      <Form.Item name="assigneeLandline" label="经办人电话-座机">
        <Input />
      </Form.Item>
      <Form.Item name="assigneePhone" label="经办人电话-移动电话">
        <Input />
      </Form.Item>
      <Form.Item name="assigneePostCode" label="经办人办公邮编">
        <Input />
      </Form.Item>
      <Form.Item name="assigneeAddress" label="经办人办公地址">
        <Input />
      </Form.Item>
      <Form.Item name="assigneeRelation" label="经办人与机构关系">
        <Input />
      </Form.Item>

      <Form.Item name="controller" label="是否存在控制关系">
        <Selector
          columns={2}
          options={[
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['controller']}>
        {({ controller }) => {
          const value = first(controller);

          return (
            !!value && (
              <div className="pl-6">
                <Form.Item name="controllerReason" label="是，请说明">
                  <Input />
                </Form.Item>
              </div>
            )
          );
        }}
      </Form.Subscribe>
      <Form.Item name="targetOfInterest" label="实际受益人">
        <Selector
          columns={2}
          options={[
            { label: '本人', value: 1 },
            { label: '他人', value: 0 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['targetOfInterest']}>
        {({ targetOfInterest }) => {
          const value = first(targetOfInterest);

          return value === 0 ? (
            <div className="pl-6">
              <Form.Item name="targetOfInterestReason" label="他人，请说明">
                <Input />
              </Form.Item>
            </div>
          ) : (
            <React.Fragment />
          );
        }}
      </Form.Subscribe>
      <Form.Item name="creditDishonour" label="是否有不良诚信记录">
        <Selector
          columns={2}
          options={[
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['creditDishonour']}>
        {({ creditDishonour }) => {
          const value = first(creditDishonour);

          return (
            !!value && (
              <div className="pl-6">
                <Form.Item name="creditDishonourReason" label="是，请说明">
                  <Input />
                </Form.Item>
              </div>
            )
          );
        }}
      </Form.Subscribe>
    </React.Fragment>
  );
};
