import * as account from '@/queries/account';
import { phone } from '@portfolio-service/schema/utils';
import { Form, Input, Selector } from 'antd-mobile';
import first from 'lodash/first';
import React from 'react';

export default () => {
  const userQuery = account.user();
  const { data: user } = userQuery;

  return (
    <React.Fragment>
      <Form.Item label="名称" rules={[{ required: true }]}>
        <span>{user?.name}</span>
      </Form.Item>
      <Form.Item label="证件类型" rules={[{ required: true }]}>
        <span>{user?.identityType}</span>
      </Form.Item>
      <Form.Item label="证件号码" rules={[{ required: true }]}>
        <span>{user?.identityNumber}</span>
      </Form.Item>
      <Form.Item name="gender" label="性别" rules={[{ required: true }]}>
        <Selector
          columns={3}
          options={['男', '女', '其他'].map((it) => ({
            label: it,
            value: it,
          }))}
        />
      </Form.Item>
      <Form.Item name="age" label="年龄" rules={[{ required: true }]}>
        <Input type="number" min={1} />
      </Form.Item>
      <Form.Item name="nationality" label="国籍" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item name="profession" label="职业" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item name="job" label="职务" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item name="landline" label="电话-座机">
        <Input />
      </Form.Item>
      <Form.Item
        name="phone"
        label="电话-移动电话"
        required
        rules={[
          {
            validator: async (_, value) => {
              if (!phone.safeParse(value).success) {
                throw new Error('格式不正确');
              }
            },
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item name="postCode" label="邮编">
        <Input />
      </Form.Item>
      <Form.Item name="email" label="电子邮箱" rules={[{ required: true }]}>
        <Input type="email" />
      </Form.Item>
      <Form.Item name="residency" label="住址" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item
        name="controller"
        label="是否存在控制关系"
        rules={[{ required: true }]}
      >
        <Selector
          columns={2}
          options={[
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['controller']}>
        {({ controller }) => {
          const value = first(controller);

          return !!value ? (
            <div className="pl-6">
              <Form.Item
                name="controllerReason"
                label="是，请说明"
                rules={[{ required: true }]}
              >
                <Input />
              </Form.Item>
            </div>
          ) : (
            <React.Fragment />
          );
        }}
      </Form.Subscribe>
      <Form.Item
        name="targetOfInterest"
        label="实际受益人"
        rules={[{ required: true }]}
      >
        <Selector
          columns={2}
          options={[
            { label: '本人', value: 1 },
            { label: '他人', value: 0 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['targetOfInterest']}>
        {({ targetOfInterest }) => {
          const value = first(targetOfInterest);

          return (
            value === 0 && (
              <div className="pl-6">
                <Form.Item
                  name="targetOfInterestReason"
                  label="他人，请说明"
                  rules={[{ required: true }]}
                >
                  <Input />
                </Form.Item>
              </div>
            )
          );
        }}
      </Form.Subscribe>
      <Form.Item
        name="creditDishonour"
        label="是否有不良诚信记录"
        rules={[{ required: true }]}
      >
        <Selector
          columns={2}
          options={[
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ]}
        />
      </Form.Item>
      <Form.Subscribe to={['creditDishonour']}>
        {({ creditDishonour }) => {
          const value = first(creditDishonour);

          return (
            !!value && (
              <div className="pl-6">
                <Form.Item
                  name="creditDishonourReason"
                  label="是，请说明"
                  rules={[{ required: true }]}
                >
                  <Input />
                </Form.Item>
              </div>
            )
          );
        }}
      </Form.Subscribe>
    </React.Fragment>
  );
};
