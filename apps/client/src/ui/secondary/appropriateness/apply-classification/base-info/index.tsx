import * as account from '@/queries/account';
import * as query from '@/queries/appropriatenessProcess/baseInfo';
import { getAgeFromIdentityNumber } from '@/utils/misc';
import { Button, Card, Form } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { processStore } from '../store';
import FormItemsIndividual from './form-items-individual';
import FormItemsOrganization from './form-items-organization';

export default () => {
  const [form] = Form.useForm();
  const { data: user } = account.user();

  const age = useMemo(
    () => getAgeFromIdentityNumber(user?.identityNumber),
    [user?.identityNumber],
  );

  const { data } = query.own();
  const mutation = query.update();

  const onFinish = async () => {
    const data = form.getFieldsValue();
    await mutation.trigger(data);
    processStore.next();
  };

  useEffect(() => {
    form.resetFields();

    if (!data?.item?.formData) return;
    const {
      identityExpireDate,
      representativeIdentityExpireDate,
      assigneeIdentityExpireDate,
      ...rest
    } = data.item.formData;

    const fieldValues = {
      ...rest,
      identityExpireDate: identityExpireDate
        ? dayjs(identityExpireDate).toDate()
        : undefined,
      representativeIdentityExpireDate: representativeIdentityExpireDate
        ? dayjs(representativeIdentityExpireDate).toDate()
        : undefined,
      assigneeIdentityExpireDate: assigneeIdentityExpireDate
        ? dayjs(assigneeIdentityExpireDate).toDate()
        : undefined,
    };

    for (const [key, value] of Object.entries(fieldValues)) {
      if (value == null) {
        fieldValues[key] = undefined;
        continue;
      }

      if (
        [
          'gender',
          'controller',
          'targetOfInterest',
          'creditDishonour',
          'representativeGender',
          'assigneeGender',
        ].includes(key) &&
        !Array.isArray(value)
      ) {
        fieldValues[key] = [value];
      }
    }

    form.setFieldsValue(fieldValues);
  }, [data]);

  return (
    <Card title="基本信息采集">
      <Form
        form={form}
        mode="card"
        onFinish={onFinish}
        initialValues={{ age }}
        footer={
          <Button block color="primary" shape="rounded" type="submit">
            下一步
          </Button>
        }
      >
        {user?.type === '个人' && <FormItemsIndividual />}
        {user?.type === '机构' && <FormItemsOrganization />}
      </Form>
    </Card>
  );
};
