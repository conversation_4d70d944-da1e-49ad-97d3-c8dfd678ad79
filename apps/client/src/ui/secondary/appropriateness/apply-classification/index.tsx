import { useEffect } from 'react';
import ApplyClassificationStep from './apply-classification-step';
import BaseInfo from './base-info';
import Documents from './documents';
import { processStore } from './store';
import TaxDeclaration from './tax-declaration';

export default () => {
  const state = processStore.useState();

  useEffect(() => {
    return () => {
      processStore.reset();
    };
  }, []);

  return (
    <div>
      <div>
        <ApplyClassificationStep />
      </div>
      <div>
        {state === 'baseInfo' && <BaseInfo />}
        {state === 'taxDeclaration' && <TaxDeclaration />}
        {state === 'documents' && <Documents />}
      </div>
    </div>
  );
};
