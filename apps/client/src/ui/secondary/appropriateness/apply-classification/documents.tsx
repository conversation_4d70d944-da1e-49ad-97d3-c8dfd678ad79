import * as query from '@/queries/appropriatenessProcess';
import * as fileUpload from '@/queries/fileUpload';
import { toastError, toastSuccess } from '@/utils/antd';
import { DocumentType } from '@portfolio-service/schema/document';
import { classificationEnum } from '@portfolio-service/schema/userType';
import { Button, Card, Form, Selector, Space } from 'antd-mobile';
import React from 'react';
import { processStore } from './store';

export default () => {
  const [form] = Form.useForm();
  const { data } = query.own();

  const mutation = query.classification();

  const onFinish = async () => {
    const { classification, ...rest } = form.getFieldsValue();

    const documents: query.ClassificationParams['documents'] = [];

    const promises = Object.entries(rest).flatMap(([key, file]) => {
      const files: File[] = Array.isArray(file) ? file : [file];

      return files.map(async (file) => {
        const fileUploadId = await fileUpload.uploadRequset(file as File);
        documents.push({ fileUploadId, type: key as DocumentType });
      });
    });

    try {
      await Promise.all(promises);
    } catch {
      toastError('上传材料失败');
      return;
    }

    await mutation.trigger({
      id: data?.item?.id,
      classification: classification[0],
      documents,
    });
    toastSuccess('提交成功');
  };

  return (
    <Card title="选择投资人类型">
      <Space block direction="vertical" justify="center">
        <Form
          form={form}
          mode="card"
          onFinish={onFinish}
          footer={
            <div className="flex gap-6">
              <Button block shape="rounded" onClick={processStore.prev}>
                上一步
              </Button>
              <Button
                block
                color="primary"
                shape="rounded"
                type="submit"
                loading={mutation.isMutating}
              >
                提交
              </Button>
            </div>
          }
        >
          <Form.Item
            name="classification"
            label="投资人类型"
            rules={[{ required: true, message: '请选择投资人类型' }]}
          >
            <Selector
              columns={2}
              options={classificationEnum.map((it) => ({
                label: it,
                value: it,
              }))}
            />
          </Form.Item>
          <Form.Subscribe to={['classification']}>
            {(data) => {
              const classification = data.classification?.[0];

              if (classification === '普通投资者') {
                return <NormalFormItems />;
              }

              if (classification === '专业投资者') {
                return <ProfessionalFormItems />;
              }

              return <React.Fragment />;
            }}
          </Form.Subscribe>
        </Form>
      </Space>
    </Card>
  );
};

const Upload = (props: { value?: File; onChange?: (value?: File) => void }) => {
  const { onChange } = props;

  return (
    <input type="file" onChange={(e) => onChange?.(e.target.files?.[0])} />
  );
};

// const UploadMultiple = (props: {
//   value?: File[];
//   onChange?: (value?: File[]) => void;
// }) => {
//   const { onChange } = props;

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const { files } = e.target;

//     const validFiles: File[] = [];
//     if (!!files?.length) {
//       for (const file of files) {
//         validFiles.push(file);
//       }
//     }

//     onChange?.(validFiles);
//   };

//   return <input type="file" onChange={handleChange} multiple />;
// };


const UploadMultiple = (props: {
  value?: File[];
  onChange?: (value?: File[]) => void;
}) => {
  const { onChange, value = [] } = props;
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    const newFiles: File[] = [];

    if (files && files.length) {
      for (let i = 0; i < files.length; i++) {
        newFiles.push(files[i]);
      }
    }

    const updatedFiles = [...value, ...newFiles];
    onChange?.(updatedFiles);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    const newFiles = [...(value || [])];
    newFiles.splice(index, 1);
    onChange?.(newFiles);
  };

  return (
    <div>
      <input 
        type="file" 
        ref={fileInputRef}
        onChange={handleChange} 
        multiple 
      />
      {value.length > 0 && (
        <div style={{ marginTop: 10 }}>
          <strong>已选择文件:</strong>
          <ul >
            {value.map((file, index) => (
              <li key={`${file.name}-${index}`} style={{ marginBottom: 5 }}>
                {file.name} 
                <Button 
                  size="mini" 
                  style={{ marginLeft: 10 }}
                  onClick={() => removeFile(index)}
                >
                  移除
                </Button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};


const NormalFormItems = () => {
  return (
    <React.Fragment>
      <Form.Item
        name="身份证正面"
        label="身份证正面"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="身份证反面"
        label="身份证反面（国徽面）"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="银行卡材料"
        label="银行卡照片/扫描件"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="资产证明"
        label={
          <div>
            <div>资产规模证明</div>
            <div style={{ color: 'var(--adm-color-weak)' }} className="text-sm">
              <div>
                根据《私募投资基金募集行为管理办法》第二十八条规定，投资者购买私募基金必须满足合格投资者标准，请您根据下方提示上传符合标准的相应文件（二选一）：
              </div>
              <div>
                1. 收入证明：最近3年个人年均收入不低于50万元的个人收入证明；
              </div>
              <div>2. 资产证明：不低于300万元的金融资产证明。</div>
            </div>
          </div>
        }
        required
        rules={[{ validator }]}
      >
        <UploadMultiple />
      </Form.Item>
    </React.Fragment>
  );
};

const ProfessionalFormItems = () => {
  return (
    <React.Fragment>
      <Form.Item
        name="身份证正面"
        label="身份证正面"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="身份证反面"
        label="身份证反面（国徽面）"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="银行卡材料"
        label="银行卡照片/扫描件"
        rules={[{ validator }]}
        required
      >
        <Upload />
      </Form.Item>
      <Form.Item
        name="资产证明"
        label={
          <div>
            <div>资产规模证明</div>
            <div style={{ color: 'var(--adm-color-weak)' }} className="text-sm">
              <div>
                根据《私募投资基金募集行为管理办法》第二十八条规定、《证券期货投资者适当性管理办法》第八条规定，投资者购买私募基金必须满足合格投资者标准，请您根据下方提示上传符合专业投资者标准的相应文件（二选一）：
              </div>
              <div>
                1. 收入证明：最近3年个人年均收入不低于50万元的个人收入证明；
              </div>
              <div>2. 资产证明：不低于500万元的金融资产证明。</div>
            </div>
          </div>
        }
        rules={[{ validator }]}
        required
      >
        <UploadMultiple />
      </Form.Item>
      <Form.Item
        name="投资经历证明材料"
        label={
          <div>
            <div>投资经历证明</div>
            <div style={{ color: 'var(--adm-color-weak)' }} className="text-sm">
              根据《证券期货投资者适当性管理办法》第八条规定，不同类型专业投资者需满足相应的专业投资者标准，请您根据下方提示上传符合标准的相应文件。投资经历证明：具有
              2 年以上证券、基金、期货、黄金、外汇等投资经历证明。
            </div>
          </div>
        }
        rules={[{ validator }]}
        required
      >
        <UploadMultiple />
      </Form.Item>
    </React.Fragment>
  );
};

const validator = async (_: unknown, value: File | File[]) => {
  const files = (Array.isArray(value) ? value : [value]).filter(Boolean);

  if (!files) {
    throw new Error('请上传材料');
  }

  for (const file of files) {
    if (file.size > 5 * 1024 * 1024) {
      throw new Error('请上传单个为 5MB 以内的文件');
    }
  }
};
