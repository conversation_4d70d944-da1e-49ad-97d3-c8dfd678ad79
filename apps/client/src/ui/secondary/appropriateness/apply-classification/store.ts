import { $produce } from '@/utils/immer';
import { createAtom } from '@/utils/zustand';

type ApplyClassificationState = 'baseInfo' | 'taxDeclaration' | 'documents';

type RegisterProcess = {
  state: ApplyClassificationState;
  data: Partial<Record<ApplyClassificationState, any>>;
};

const createProcessStore = (initialValues: RegisterProcess) => {
  const processAtom = createAtom<RegisterProcess>(initialValues);

  const reset = () =>
    processAtom.store.setState($produce((draft) => (draft.state = 'baseInfo')));

  const next = () => {
    const { state } = processAtom.store.getState();

    switch (state) {
      case 'baseInfo':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'taxDeclaration')),
        );
        break;
      case 'taxDeclaration':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'documents')),
        );
        break;
    }
  };

  const prev = () => {
    const { state } = processAtom.store.getState();

    switch (state) {
      case 'taxDeclaration':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'baseInfo')),
        );
        break;
      case 'documents':
        processAtom.store.setState(
          $produce((draft) => (draft.state = 'taxDeclaration')),
        );
        break;
    }
  };

  const useState = () => {
    return processAtom.use(({ state }) => state);
  };

  const setState = (state: ApplyClassificationState) => {
    processAtom.store.setState($produce((draft) => (draft.state = state)));
  };

  const useStep = () => {
    return processAtom.use(({ state }) => {
      switch (state) {
        case 'baseInfo':
          return 0;
        case 'taxDeclaration':
          return 1;
        case 'documents':
          return 2;
      }
    });
  };

  const useData = <T>(): T | undefined => {
    return processAtom.use(({ data, state }) => data[state]);
  };

  const setData = (data: any) => {
    processAtom.store.setState(
      $produce((draft) => (draft.data[draft.state] = data)),
    );
  };

  const getData = <T>(): T | undefined => {
    const { state, data } = processAtom.store.getState();
    return data[state];
  };

  return {
    ...processAtom,
    reset,
    next,
    prev,
    useState,
    setState,
    useStep,
    useData,
    setData,
    getData,
  };
};

export const processStore = createProcessStore({
  state: 'baseInfo',
  data: {},
});
