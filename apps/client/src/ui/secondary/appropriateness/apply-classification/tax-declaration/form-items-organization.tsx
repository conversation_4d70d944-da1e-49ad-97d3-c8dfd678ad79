import { Form, Input, Selector } from 'antd-mobile';
import first from 'lodash/first';
import React from 'react';
import FormItemsIndividual from './form-items-individual';

export default () => (
  <React.Fragment>
    <Form.Item name="organizationType" label="机构类别"
     rules={[{required:true,message:"请选择机构类别"}]}
    >
      <Selector
        columns={2}
        options={[
          { label: '消极非金融机构', value: 'pessimistic' },
          { label: '其他非金融机构', value: 'other' },
        ]}
      />
    </Form.Item>

    <Form.Subscribe to={['organizationType']}>
      {({ organizationType }) => {
        const value = first(organizationType);

        if (!value || value === 'other') {
          return <React.Fragment />;
        }

        return <FormItemsIndividual />;
      }}
    </Form.Subscribe>

    <Form.Item name="organizationTaxType" label="机构税收居民身份" rules={[{required:true,message:"请选择机构税收居民身份"}]}>
      <Selector
        columns={1}
        options={[
          { label: '仅为中国税收居民', value: 'chineseTaxResident' },
          { label: '仅为非居民', value: 'nonChineseTaxResident' },
          {
            label: '既是中国税收居民又是其他国家（地区）税收居民',
            value: 'both',
          },
        ]}
      />
    </Form.Item>
    <Form.Subscribe to={['organizationTaxType']}>
      {({ organizationTaxType }) => {
        const value = first(organizationTaxType);

        if (!value || value === 'chineseTaxResident') {
          return <React.Fragment />;
        }

        return <ExtraFormItems />;
      }}
    </Form.Subscribe>
  </React.Fragment>
);

const ExtraFormItems = () => (
  <React.Fragment>
    <Form.Item label="机构名称（英文）" name="organizaitonNameEN">
      <Input />
    </Form.Item>

    <Form.Item label="机构地址（英文或拼音）" name="organizationAddressEN">
      <Input />
    </Form.Item>
    <Form.Item
      label="机构地址（中文，境外机构可不填）"
      name="organizationAddressCN"
    >
      <Input />
    </Form.Item>

    <Form.Item
      label="机构税收居民国（地区）及纳税人识别号"
      name="organizationTaxIdentityNumber"
    >
      <Input />
    </Form.Item>
    <Form.Item label="2（如有）" name="organizationTaxIdentityNumber2">
      <Input />
    </Form.Item>
    <Form.Item label="3（如有）" name="organizationTaxIdentityNumber3">
      <Input />
    </Form.Item>

    <Form.Item
      label="如不能提供税收居民国（地区）及纳税人识别号，请选择原因"
      name="organizationNoTaxIdentityNumberCause"
    >
      <Selector
        columns={1}
        options={[
          {
            label: '居民国（地区）不发放纳税人识别号',
            value: 'distribution',
          },
          { label: '账户持有人未取得纳税人识别号', value: 'obtain' },
        ]}
      />
    </Form.Item>
    <Form.Subscribe to={['organizationNoTaxIdentityNumberCause']}>
      {({ organizationNoTaxIdentityNumberCause }) => {
        const value = first(organizationNoTaxIdentityNumberCause);

        return value === 'obtain' ? (
          <div className="pl-6">
            <Form.Item
              name="organizationNoTaxIdentityNumberCauseReason"
              label="如未取得，请解释具体原因"
            >
              <Input />
            </Form.Item>
          </div>
        ) : (
          <React.Fragment />
        );
      }}
    </Form.Subscribe>
  </React.Fragment>
);
