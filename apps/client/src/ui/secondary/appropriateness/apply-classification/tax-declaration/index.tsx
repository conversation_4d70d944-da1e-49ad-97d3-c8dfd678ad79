import * as account from '@/queries/account';
import * as query from '@/queries/appropriatenessProcess/taxDeclaration';
import { <PERSON>ton, Card, Form, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { processStore } from '../store';
import FormItemsIndividual from './form-items-individual';
import FormItemsOrganization from './form-items-organization';

export default () => {
  const [form] = Form.useForm();
  const { data: user } = account.user();

  const { data } = query.own();
  const mutation = query.update();

  const onFinish = async () => {
    const data = form.getFieldsValue();
    await mutation.trigger(data);
    processStore.next();
  };

  useEffect(() => {
    form.resetFields();

    if (!data?.item?.formData) return;
    const { birthday, ...rest } = data.item.formData;

    const fieldValues = {
      ...rest,
      birthday: birthday ? dayjs(birthday).toDate() : undefined,
    };

    for (const [key, value] of Object.entries(fieldValues)) {
      if (value == null) {
        fieldValues[key] = undefined;
        continue;
      }

      if (
        [
          'type',
          'noTaxIdentityNumberCause',
          'organizationType',
          'organizationTaxType',
          'organizationNoTaxIdentityNumberCause',
        ].includes(key) &&
        !Array.isArray(value)
      ) {
        fieldValues[key] = [value];
      }
    }

    form.setFieldsValue(fieldValues);
  }, [data]);

  return (
    <Card title="税收声明">
      <Space block direction="vertical" justify="center">
        <Form
          form={form}
          mode="card"
          onFinish={onFinish}
          footer={
            <div className="flex gap-6">
              <Button block shape="rounded" onClick={processStore.prev}>
                上一步
              </Button>
              <Button block color="primary" shape="rounded" type="submit">
                下一步
              </Button>
            </div>
          }
        >
          {user?.type === '个人' && <FormItemsIndividual />}
          {user?.type === '机构' && <FormItemsOrganization />}
        </Form>
      </Space>
    </Card>
  );
};
