import DatePickerFormItem from '@/components/date-picker-form-item';
import { Form, Input, Selector } from 'antd-mobile';
import first from 'lodash/first';
import React from 'react';

export default () => (
  <React.Fragment>
    <Form.Item name="type" label="税收居民身份" rules={[{required:true,message:"请选择税收居民身份"}]}>
      <Selector
        columns={1}
        options={[
          { label: '仅为中国税收居民', value: 'chineseTaxResident' },
          { label: '仅为非居民', value: 'nonChineseTaxResident' },
          {
            label: '即是中国税收居民又是其他国家（地区）税收居民',
            value: 'both',
          },
        ]}
      />
    </Form.Item>
    <Form.Subscribe to={['type']}>
      {({ type }) => {
        const value = first(type);

        if (!value || value === 'chineseTaxResident') {
          return <React.Fragment />;
        }

        return <ExtraFormItems />;
      }}
    </Form.Subscribe>
  </React.Fragment>
);

const ExtraFormItems = () => (
  <React.Fragment>
    <Form.Item label="姓（英文或拼音）" name="familyName">
      <Input />
    </Form.Item>
    <Form.Item label="名（英文或拼音）" name="givenName">
      <Input />
    </Form.Item>
    <DatePickerFormItem label="出生日期" name="birthday" />
    <Form.Item label="现居地址（中文）" name="addressCN">
      <Input />
    </Form.Item>
    <Form.Item label="现居地址（英文或拼音）" name="addressEN">
      <Input />
    </Form.Item>
    <Form.Item label="出生地（中文）" name="placeOfBirthCN">
      <Input />
    </Form.Item>
    <Form.Item label="出生地（英文或拼音）" name="placeOfBirthEN">
      <Input />
    </Form.Item>
    <Form.Item
      label="税收居民国（地区）及纳税人识别号"
      name="taxIdentityNumber"
    >
      <Input />
    </Form.Item>
    <Form.Item label="2（如有）" name="taxIdentityNumber2">
      <Input />
    </Form.Item>
    <Form.Item label="3（如有）" name="taxIdentityNumber3">
      <Input />
    </Form.Item>

    <Form.Item
      label="如不能提供税收居民国（地区）及纳税人识别号，请选择原因"
      name="noTaxIdentityNumberCause"
    >
      <Selector
        columns={1}
        options={[
          {
            label: '居民国（地区）不发放纳税人识别号',
            value: 'distribution',
          },
          { label: '账户持有人未取得纳税人识别号', value: 'obtain' },
        ]}
      />
    </Form.Item>
    <Form.Subscribe to={['noTaxIdentityNumberCause']}>
      {({ noTaxIdentityNumberCause }) => {
        const value = first(noTaxIdentityNumberCause);

        return value === 'obtain' ? (
          <div className="pl-6">
            <Form.Item
              name="noTaxIdentityNumberCauseReason"
              label="如未取得，请解释具体原因"
            >
              <Input />
            </Form.Item>
          </div>
        ) : (
          <React.Fragment />
        );
      }}
    </Form.Subscribe>
  </React.Fragment>
);
