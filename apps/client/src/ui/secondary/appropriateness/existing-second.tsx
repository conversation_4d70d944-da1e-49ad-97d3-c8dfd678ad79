import DocumentViewButton from '@/components/document-view-button';
import * as query from '@/queries/appropriatenessProcess';
import { DocumentType } from '@portfolio-service/schema/document';
import { Card, List, Space } from 'antd-mobile';
import { FileOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';

const documentTypes: Set<DocumentType> = new Set([
  '投资者基本信息表',
  '税收声明',
  '风险测评问卷',
  '风险承受能力评估结果',
  '合格投资者承诺',
]);

export default () => {
  const own = query.own().data;
  const { data } = query.documentsById(own?.item?.id);

  const documents = useMemo(
    () => data?.filter(({ type }) => !!type && documentTypes.has(type)),
    [data],
  );

  if (!data?.length) {
    return <React.Fragment />;
  }

  return (
    <Space block direction="vertical">
      <List mode="card">
        {documents?.map((document) => {
          const { createTime } = document;
          const time = createTime
            ? dayjs(createTime).format('YYYY-MM-DD')
            : '-年-月-日';

          return (
            <List.Item key={document.id}>
              <Card
                title={
                  <Space align="center">
                    <FileOutline />
                    <span>{document.type}</span>
                  </Space>
                }
              >
                <Space block justify="between" align="center">
                  <span>{time}</span>
                  <DocumentViewButton
                    size="small"
                    color="primary"
                    document={document}
                  >
                    查看
                  </DocumentViewButton>
                </Space>
              </Card>
            </List.Item>
          );
        })}
      </List>
    </Space>
  );
};
