import { Button, Modal, Space } from 'antd-mobile';

export type FaceRecognitionProps = {
  link: () => Promise<string | undefined>;
  loading: boolean;
  showOtherOptions: boolean;
  onOtherOptions: () => void;
};

export default (props: FaceRecognitionProps) => {
  const { link, loading, showOtherOptions, onOtherOptions } = props;

  const onClick = async () => {
    // if (isProdEnv()) {
    //   if (!isWechatEnv()) {
    //     toastError('请在微信公众号操作');
    //     return;
    //   }
    // }

    location.href = (await link())!;
  };

  return (
    <div className="bg-white shadow-custom p-8 rounded-4xl">
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div className="text-lg font-medium">人脸识别</div>
        <Button
          block
          color="primary"
          shape="rounded"
          loading={loading}
          disabled={!link}
          onClick={onClick}
        >
          前往识别
        </Button>
        {showOtherOptions && (
          <div className="flex justify-center">
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={() =>
                Modal.show({
                  bodyStyle: {
                    borderRadius: '32px',
                    padding: '1.5rem 1.5rem 1rem 1.5rem',
                  },
                  closeOnAction: true,
                  actions: [
                    {
                      key: 'confirm',
                      text: '确定',
                      primary: true,
                      style: { borderRadius: '9999px' },
                      onClick: onOtherOptions,
                    },
                    {
                      key: 'cancel',
                      text: '取消',
                    },
                  ],
                  title:
                    '如不同意我司采集个人生物识别信息，请采用银行账户进行认证。',
                })
              }
            >
              其他选项
            </a>
          </div>
        )}
      </Space>
    </div>
  );
};
