import { IdentityVerificationType } from '@portfolio-service/schema/identityValidation';
import { Space } from 'antd-mobile';
import { useState } from 'react';
import FaceRecognition, { FaceRecognitionProps } from './face-recognition';
import Quartlet, { QuartletProps } from './quartlet';

type Props = {
  title?: React.ReactNode;
  disableQuartlet?: boolean;
  faceRecognitionProps: Pick<FaceRecognitionProps, 'link' | 'loading'>;
  quartletProps?: QuartletProps;
};

export default (props: Props) => {
  const { title, disableQuartlet, faceRecognitionProps, quartletProps } = props;

  const [type, setType] = useState<IdentityVerificationType>('人脸识别');

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {!!title && <div className="text-lg font-medium">{title}</div>}
      <FaceRecognition
        {...faceRecognitionProps}
        onOtherOptions={() => setType('四要素')}
        showOtherOptions={!disableQuartlet && type === '人脸识别'}
      />
      {!disableQuartlet && type === '四要素' && (
        <Quartlet {...(quartletProps || {})} />
      )}
    </Space>
  );
};
