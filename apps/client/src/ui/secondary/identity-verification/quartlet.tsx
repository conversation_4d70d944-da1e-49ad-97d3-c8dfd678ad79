import * as account from '@/queries/account';
import { phone } from '@portfolio-service/schema/utils';
import { Button, Form, FormProps, Input, Selector, Space } from 'antd-mobile';
import { useEffect } from 'react';

export type QuartletProps = FormProps & {
  loading?: boolean;
};

export default ({ loading, ...props }: QuartletProps) => {
  const { data: user } = account.user();
  const form = props.form || Form.useForm()[0];

  useEffect(() => {
    if (!user) return;
    if (!!form.getFieldValue('name')) return;

    form.setFieldValue('name', user.name);
  }, [user]);

  return (
    <div className="bg-white shadow-custom p-4 rounded-4xl">
      <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
        <div className="text-lg font-medium px-4 pt-4">银行账户认证</div>
        <Form
          {...props}
          initialValues={{ bindBankCard: [1] }}
          form={form}
          className="w-full"
          mode="card"
          footer={
            <Button
              block
              color="primary"
              fill="solid"
              shape="rounded"
              type="submit"
              loading={loading}
            >
              提交
            </Button>
          }
        >
          <Form.Item
            className="bg-white"
            name="name"
            label="姓名"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="identityNumber"
            label="证件号码"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="bankNumber"
            label="银行卡号码"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="accountName"
            label="银行账户名称"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="phone"
            label="银行预留手机"
            required
            rules={[
              {
                validator: async (_, value) => {
                  if (!value) {
                    throw new Error('请输入银行预留手机');
                  }

                  if (!phone.safeParse(value).success) {
                    throw new Error('格式不正确');
                  }
                },
              },
            ]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="branch"
            label="银行开户行全称"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item
            className="bg-white"
            name="bindBankCard"
            label="绑定银行卡"
            rules={[{ required: true }]}
          >
            <Selector
              columns={2}
              options={[
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ]}
            />
          </Form.Item>
        </Form>
      </Space>
    </div>
  );
};
