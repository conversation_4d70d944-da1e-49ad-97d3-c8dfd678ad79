import { DownOutline } from 'antd-mobile-icons';
import { filterAtom, filterPicker } from './filter-picker';

export default () => {
  const value = filterAtom.use();

  return (
    <div
      className="bg-white rounded-full px-4 py-[0.375rem] flex items-center"
      onClick={() => filterPicker.show()}
    >
      <span className="mr-2">{value || '筛选'}</span>
      <DownOutline />
    </div>
  );
};
