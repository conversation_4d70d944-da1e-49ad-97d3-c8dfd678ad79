import { createAtom, createPopup } from '@/utils/zustand';
import {
  BusinessType,
  businessTypeEnum,
  businessTypeSchema,
} from '@portfolio-service/schema/trade';
import { Picker, PickerProps } from 'antd-mobile';

export const filterPicker = createPopup();
export const filterAtom = createAtom<BusinessType | undefined>(undefined);

const columns: PickerProps['columns'] = [
  [{ label: '全部', value: '' }].concat(
    businessTypeEnum.map((it) => ({ label: it, value: it })),
  ),
];

export default () => {
  const open = filterPicker.use((state) => state.open);

  const onConfirm = (value: string | number | null) => {
    const valueParsed = businessTypeSchema.safeParse(value?.toString());
    filterAtom.store.setState(
      valueParsed.success ? valueParsed.data : undefined,
    );

    filterPicker.hide();
  };

  return (
    <Picker
      defaultValue={[filterAtom.store.getState() || '']}
      visible={open}
      columns={columns}
      onConfirm={([value]) => onConfirm(value)}
      onCancel={() => filterPicker.hide()}
    />
  );
};
