import { ReactComponent as DownArrow } from '@/assets/down-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import { toCN, toCNY } from '@/utils/number';
import { createPopup } from '@/utils/zustand';
import { stringToDate } from '@portfolio-service/schema/utils';
import { Divider, Popup, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import { RouterOutput } from 'server/typings';

export const tradeDetailDialog =
  createPopup<RouterOutput['trade']['list']['items'][number]>();

export default () => {
  const { open, data } = tradeDetailDialog.use();

  const applyDateParsed = stringToDate.safeParse(data?.applyDate);
  const applyDate = applyDateParsed.success
    ? dayjs(applyDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  const confDateParsed = stringToDate.safeParse(data?.confDate);
  const confDate = confDateParsed.success
    ? dayjs(confDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  return (
    <Popup
      visible={open}
      onClose={() => tradeDetailDialog.hide()}
      closeOnMaskClick
      closeOnSwipe
      bodyStyle={{
        height: 'calc(50vh + env(safe-area-inset-bottom))',
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        overflowY: 'auto',
        padding: '0 2rem 2rem 2rem',
      }}
    >
      <div
        onClick={() => tradeDetailDialog.hide()}
        className="sticky z-50 top-0 left-0 right-0 h-8 flex justify-center items-center"
        style={{ backgroundColor: 'var(--adm-color-background)' }}
      >
        <SvgWrapper component={DownArrow} />
      </div>
      <div className="text-lg font-medium">交易详情</div>
      <Divider className="my-2" />
      <Space
        block
        direction="vertical"
        style={{ '--gap-vertical': '1.5rem' }}
        className="mt-6"
      >
        <div className="bg-white rounded-4xl shadow-custom p-6">
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '1.5rem' }}
          >
            <span
              className="font-medium text-sm"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              交易申请
            </span>
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '0.75rem' }}
            >
              <Item label="产品名称" value={data?.portfolioName} />
              <Item label="产品代码" value={data?.portfolioNo} />
              <Item label="交易类型" value={data?.businessType} />
              <Item label="申请日期" value={applyDate} />
              <Item label="申请金额" value={toCNY(data?.applyBalance)} />
              <Item label="申请份额" value={toCN(data?.applyShare)} />
            </Space>
          </Space>
        </div>
        <div className="bg-white rounded-4xl shadow-custom p-6">
          <Space
            block
            direction="vertical"
            style={{ '--gap-vertical': '1.5rem' }}
          >
            <span
              className="font-medium text-sm"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              交易确认
            </span>
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '0.75rem' }}
            >
              <Item label="确认日期" value={confDate} />
              <Item label="确认金额" value={toCNY(data?.confBalance)} />
              <Item label="交易价格" value={toCN(data?.transactionPrice, 4)} />
              <Item label="确认份额" value={toCN(data?.confShare)} />
              <Item label="手续费" value={toCNY(data?.transactionFee)} />
              {['赎回', '强制赎回', '基金清盘'].includes(
                data?.businessType as string,
              ) && <Item label="业绩报酬" value={toCNY(data?.transferFee)} />}
            </Space>
          </Space>
        </div>
      </Space>
    </Popup>
  );
};

type ItemProps = {
  label: React.ReactNode;
  value: React.ReactNode;
};

const Item = (props: ItemProps) => {
  return (
    <div className="flex justify-between text-xs">
      <span
        className="mr-6 break-keep"
        style={{ color: 'var(--adm-color-weak)' }}
      >
        {props.label}
      </span>
      <span
        className="break-all text-right"
        style={{ color: 'var(--adm-color-text-secondary)' }}
      >
        {props.value || '-'}
      </span>
    </div>
  );
};
