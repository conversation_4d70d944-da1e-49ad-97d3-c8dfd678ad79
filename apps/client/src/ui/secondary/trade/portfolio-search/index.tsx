import { ReactComponent as Search } from '@/assets/search.svg';
import SvgWrapper from '@/components/svgWrapper';
import { createAtom } from '@/utils/zustand';
import { Input } from 'antd-mobile';
import { debounce } from 'lodash';
import FilterButton from '../filter-button';

export const searchAtom = createAtom<string | undefined>(undefined);
const setSearchDebounced = debounce(
  (value: string) => searchAtom.store.setState(value),
  500,
);

export default () => (
  <div className="flex gap-4">
    <div className="flex-1 bg-white rounded-full px-4 py-[0.375rem] flex items-center">
      <span className="mr-2">
        <SvgWrapper component={Search} />
      </span>
      <Input
        clearable
        placeholder="产品名称/代码"
        style={{ '--font-size': '0.875rem' }}
        onChange={(value) => setSearchDebounced(value)}
      />
    </div>
    <FilterButton />
  </div>
);
