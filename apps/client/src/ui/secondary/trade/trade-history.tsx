import { ReactComponent as RightArrow } from '@/assets/right-arrow.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/ta/trade';
import { toCN, toCNY } from '@/utils/number';
import { usePagination } from '@/utils/pagination';
import { stringToDate } from '@portfolio-service/schema/utils';
import { InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React from 'react';
import { RouterOutput } from 'server/typings';
import { filterAtom } from './filter-picker';
import { searchAtom } from './portfolio-search';
import { tradeDetailDialog } from './trade-detail';

const pageSize = 10;

export default () => {
  const search = searchAtom.use();
  const businessType = filterAtom.use();

  const { page, nextPage, prevPages, useHelpers } = usePagination({
    pageSize,
    resetDeps: [search, businessType],
  });

  const listQuery = query.list({
    page,
    pageSize,
    portfolio: search,
    businessType,
  });
  const { data: trades } = listQuery;
  const { hasMore } = useHelpers(trades);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <React.Fragment>
      <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
        {prevPages.map((page) => (
          <Page
            key={page}
            params={{ page, pageSize, portfolio: search, businessType }}
          />
        ))}
        {trades?.items?.map((it, index) => (
          <ListItem key={index + prevPages.length} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['trade']['list']['items'][number];
}) => {
  const applyDateParsed = stringToDate.safeParse(data.applyDate);
  const applyDate = applyDateParsed.success
    ? dayjs(applyDateParsed.data).format('YYYY-MM-DD')
    : undefined;

  let amount: string | undefined = undefined;
  if (data.applyBalance) amount = toCNY(data.applyBalance);
  else if (data.applyShare) amount = toCN(data.applyShare);
  else amount = '-';

  return (
    <div
      className="bg-white shadow-custom pl-6 py-6 rounded-4xl flex"
      onClick={() => tradeDetailDialog.show({ data })}
    >
      <div className="flex-1 flex mr-6">
        <div
          className="flex flex-col justify-center text-lg mr-6"
          style={{ color: 'var(--adm-color-primary)' }}
        >
          {data.businessType?.split('')?.map((char, index) => (
            <span key={index} className="leading-tight">
              {char}
            </span>
          ))}
        </div>
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '0.75rem' }}
          className="flex-1"
        >
          <span className="font-medium text-lg">
            {data.portfolioName || '-'}
          </span>
          <Space block justify="between">
            <span
              className="text-sm font-medium"
              style={{ color: 'var(--adm-color-primary)' }}
            >
              {amount}
            </span>
            <span
              className="text-xs"
              style={{ color: 'var(--adm-color-weak)' }}
            >
              {applyDate || '-'}
            </span>
          </Space>
        </Space>
      </div>
      <div className="basis-6 flex items-center">
        <SvgWrapper component={RightArrow} />
      </div>
    </div>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data: trades } = query.list(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {trades?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </Space>
  );
};
