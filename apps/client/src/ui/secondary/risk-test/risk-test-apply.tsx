import Loading from '@/components/loading';
import * as query from '@/queries/riskTestProcess';
import RiskTest, { RiskTestProps } from '@/ui/secondary/risk-test';
import ApplyDocumentSign from './apply-document-sign';

export default () => {
  const ownQuery = query.own();
  const { data, isLoading } = ownQuery;

  const mutation = query.create();
  const riskTestMutation = query.riskTest();

  if (isLoading) {
    return <Loading />;
  }

  switch (data?.item?.state) {
    case 'riskTest':
      const onFinish: RiskTestProps['onFinish'] = async ({
        answers,
        questionaireId,
      }) => {
        await riskTestMutation.trigger({
          id: data.item!.id,
          questionaireId,
          answers,
        });
      };
      return <RiskTest onFinish={onFinish} />;

    case 'documentsSign':
      return <ApplyDocumentSign title="签署风险测评问卷" />;
  }

  const onFinish: RiskTestProps['onFinish'] = async (data) => {
    await mutation.trigger(data);
  };

  return <RiskTest onFinish={onFinish} />;
};
