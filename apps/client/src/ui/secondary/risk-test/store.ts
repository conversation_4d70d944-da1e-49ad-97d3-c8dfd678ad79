import { $produce } from '@/utils/immer';
import { createAtom } from '@/utils/zustand';
import { Answer } from '@portfolio-service/schema/riskTestQuestionaire';

type RiskTestProcess = {
  questionId: number;
  answers: Record<string | number, Answer>;
};

const defaultValue: RiskTestProcess = { questionId: 1, answers: {} };

const createRiskTestStore = () => {
  const atom = createAtom<RiskTestProcess>(defaultValue);

  const reset = () => {
    atom.store.setState(defaultValue);
  };

  const next = () => {
    atom.store.setState($produce((draft) => (draft.questionId += 1)));
  };

  const prev = () => {
    atom.store.setState(
      $produce((draft) => {
        if (draft.questionId > 1) {
          draft.questionId -= 1;
        }
      }),
    );
  };

  const goto = (id: number) => {
    atom.store.setState($produce((draft) => (draft.questionId = id)));
  };

  const useQuestionId = () => {
    return atom.use((state) => state.questionId);
  };

  const useAnswer = (): Answer | undefined => {
    return atom.use(({ answers, questionId }) => answers[questionId]);
  };

  const setAnswer = (optionId: string) => {
    atom.store.setState(
      $produce(
        (draft) =>
          (draft.answers[draft.questionId] = {
            questionId: draft.questionId,
            optionId,
          }),
      ),
    );
  };

  const getAnswer = () => {
    const { answers, questionId } = atom.store.getState();
    return answers[questionId];
  };

  return {
    ...atom,
    reset,
    next,
    prev,
    goto,
    useQuestionId,
    useAnswer,
    setAnswer,
    getAnswer,
  };
};

export default createRiskTestStore();
