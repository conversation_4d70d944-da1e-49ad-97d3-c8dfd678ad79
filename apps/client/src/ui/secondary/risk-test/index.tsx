import Loading from '@/components/loading';
import * as questionaire from '@/queries/riskTest/questionaire';
import { voidKey } from '@/utils/swr';
import { answersSchema } from '@portfolio-service/schema/riskTestQuestionaire';
import { Button, Card, Form, Selector } from 'antd-mobile';
import React, { useEffect, useMemo } from 'react';
import useSWRMutation from 'swr/mutation';
import { z } from 'zod';
import store from './store';

export type RiskTestProps = {
  onFinish?: (data: {
    questionaireId: number;
    answers: z.infer<typeof answersSchema>;
  }) => void | Promise<void>;
};

export default (props: RiskTestProps) => {
  const [form] = Form.useForm();
  const questionId = store.useQuestionId();
  const answer = store.useAnswer();

  const latestQuery = questionaire.latest();
  const { data, isLoading } = latestQuery;
  const questionaireId = data?.id;

  const question = data?.config?.find((it) => it.id === questionId);

  const { isFirstQuestion, isLastQuestion } = useMemo(() => {
    const isFirstQuestion = questionId <= 1;
    const isLastQuestion =
      !!data && questionId === Math.max(...data.config.map((it) => it.id));

    return { isFirstQuestion, isLastQuestion };
  }, [questionId, data]);

  const mutation = useSWRMutation(
    voidKey,
    async (
      _,
      {
        arg,
      }: {
        arg: { questionaireId: number; onFinish: RiskTestProps['onFinish'] };
      },
    ) => {
      const { questionaireId, onFinish } = arg;
      const answers = Object.values(store.store.getState().answers);
      await onFinish?.({ questionaireId, answers });
    },
  );

  useEffect(() => {
    return () => {
      store.reset();
    };
  }, []);

  useEffect(() => {
    const value = answer?.optionId;
    form.setFieldValue('answer', value ? [value] : []);
  }, [questionId]);

  if (isLoading) {
    return <Loading />;
  }

  if (!question) {
    return <React.Fragment />;
  }

  return (
    <Card title={question.question}>
      {question.options.map((option) => (
        <p key={option.id}>
          <span>（{option.id}）</span>
          <span>{option.text}</span>
        </p>
      ))}
      <Form
        form={form}
        mode="card"
        footer={
          <div className="w-full flex gap-6">
            {!isFirstQuestion && (
              <Button className="flex-1" onClick={store.prev}>
                上一步
              </Button>
            )}
            {isLastQuestion ? (
              <Button
                type="submit"
                className="flex-1"
                color="primary"
                onClick={() =>
                  mutation.trigger({
                    questionaireId: questionaireId!,
                    onFinish: props.onFinish,
                  })
                }
                loading={mutation.isMutating}
                disabled={!answer || !questionaireId}
              >
                提交
              </Button>
            ) : (
              <Button
                className="flex-1"
                color="primary"
                onClick={store.next}
                disabled={!answer}
              >
                下一步
              </Button>
            )}
          </div>
        }
      >
        <Form.Item name="answer" label="请回答">
          <Selector
            columns={2}
            onChange={([value]) => store.setAnswer(value)}
            options={question.options.map((option) => ({
              label: option.id,
              value: option.id,
            }))}
          />
        </Form.Item>
      </Form>
    </Card>
  );
};
