import * as query from '@/queries/user';
import { toastError, toastSuccess } from '@/utils/antd';
import { useTimer } from '@/utils/timer';
import { phone } from '@portfolio-service/schema/utils';
import { Button, DotLoading, Form, Input, Space } from 'antd-mobile';
import React, { useState } from 'react';
import { useNavigate } from 'umi';
import { z } from 'zod';
import TypeSelect from './type-select';

export default () => {
  const navigate = useNavigate();

  const [sent, setSent] = useState(false);
  const [form] = Form.useForm();

  const { trigger, isMutating } = query.updateLogin();

  const onFinish = async () => {
    const { code, ...rest } = form.getFieldsValue();
    let data: Parameters<typeof trigger>[0] | undefined = undefined;
    if (z.string().email().safeParse(rest.email).success) {
      data = { type: 'email', code, email: rest.email };
    } else if (phone.safeParse(rest.phone).success) {
      data = { type: 'phone', code, phone: rest.phone };
    }

    if (!data) {
      toastError('格式不正确');
      return;
    }

    await trigger(data);
    navigate('/main/account');
  };

  const type = Form.useWatch(['type'], form);

  return (
    <div className="bg-white shadow-custom p-4 rounded-4xl">
      <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
        <div className="text-lg font-medium px-4 pt-4">修改登录方式</div>

        <Form
          form={form}
          className="w-full"
          mode="card"
          onFinish={onFinish}
          footer={
            !!type && (
              <Button
                block
                color="primary"
                fill="solid"
                shape="rounded"
                type="submit"
                loading={isMutating}
              >
                提交
              </Button>
            )
          }
        >
          <Form.Item
            className="bg-white"
            label="请选择需要修改的登录方式"
            name="type"
            rules={[{ required: true }]}
          >
            <TypeSelect
              disabled={sent}
              buttonText={{ email: '修改电子邮箱', phone: '修改手机号码' }}
            />
          </Form.Item>
          <Form.Subscribe to={['type']}>
            {({ type }) => {
              if (!type) return <React.Fragment />;

              const label = type === 'email' ? '邮箱' : '手机号码';

              return (
                <React.Fragment>
                  <Form.Item
                    className="bg-white"
                    label={label}
                    name={type}
                    rules={[{ required: true }]}
                  >
                    <Input placeholder="请输入" clearable />
                  </Form.Item>
                  <Form.Item
                    className="bg-white"
                    label="验证码"
                    name="code"
                    rules={[{ required: true }]}
                    extra={
                      <SendCodeButton
                        form={form}
                        onSend={() => setSent(true)}
                      />
                    }
                  >
                    <Input placeholder="请输入" clearable maxLength={6} />
                  </Form.Item>
                </React.Fragment>
              );
            }}
          </Form.Subscribe>
        </Form>
      </Space>
    </div>
  );
};

const SendCodeButton = ({
  form,
  onSend,
}: {
  form: ReturnType<typeof Form.useForm>[0];
  onSend?: () => void;
}) => {
  const { trigger, isMutating } = query.updateLoginSendCode();
  const timer = useTimer();

  const sendCode = async () => {
    await form.validateFields(['name']);
    const formData = form.getFieldsValue() as any;
    let data: Parameters<typeof trigger>[0] | undefined = undefined;

    if (z.string().email().safeParse(formData.email).success) {
      data = { type: 'email', email: formData.email };
    } else if (phone.safeParse(formData.phone).success) {
      data = { type: 'phone', phone: formData.phone };
    }

    if (!data) {
      toastError('格式不正确');
      return;
    }

    await trigger(data);
    toastSuccess('发送成功');
    timer.start(60);
    onSend?.();
  };

  if (isMutating) {
    return <DotLoading />;
  }

  if (timer.status === 'active') {
    return <span>再次发送（{timer.countDown}）</span>;
  }

  return (
    <a style={{ color: 'var(--adm-color-primary)' }} onClick={sendCode}>
      <span>发送验证码</span>
    </a>
  );
};
