import * as account from '@/queries/account';
import * as query from '@/queries/user';
import { useTimer } from '@/utils/timer';
import { Button, DotLoading, Form, Input, Space } from 'antd-mobile';
import React, { useState } from 'react';
import { useNavigate } from 'umi';
import TypeSelect, { TypeSelectProps } from './type-select';

export default () => {
  const navigate = useNavigate();

  const [form] = Form.useForm();
  const userQuery = account.user();
  const { data: user } = userQuery;

  const [sent, setSent] = useState(false);

  const mutation = query.verifyCode();

  const onFinish = async () => {
    const code = form.getFieldValue('code');
    await mutation.trigger({ code });
    navigate('/secondary/update-login/update');
  };

  const type = Form.useWatch(['type'], form);

  const typeSelectOptions: TypeSelectProps['options'] = [];
  if (!!user?.email) typeSelectOptions.push('email');
  if (!!user?.phone) typeSelectOptions.push('phone');

  return (
    <div className="bg-white shadow-custom p-4 rounded-4xl">
      <Space block direction="vertical" style={{ '--gap-vertical': '0.75rem' }}>
        <div className="text-lg font-medium px-4 pt-4">
          验证您当前的登录方式
        </div>

        <Form
          form={form}
          className="w-full"
          mode="card"
          onFinish={onFinish}
          footer={
            !!type && (
              <Button
                block
                color="primary"
                fill="solid"
                shape="rounded"
                type="submit"
                loading={mutation.isMutating}
              >
                提交
              </Button>
            )
          }
        >
          <Form.Item
            className="bg-white"
            label="请选择验证方式"
            name="type"
            rules={[{ required: true }]}
          >
            <TypeSelect disabled={sent} options={typeSelectOptions} />
          </Form.Item>
          <Form.Subscribe to={['type']}>
            {({ type }) => {
              if (!type) return <React.Fragment />;

              const label = type === 'email' ? '邮箱' : '手机号码';
              const value = type === 'email' ? user?.email : user?.phone;

              return (
                <React.Fragment>
                  <Form.Item className="bg-white" label={label}>
                    <span>{value}</span>
                  </Form.Item>
                  <Form.Item
                    className="bg-white"
                    label="验证码"
                    name="code"
                    rules={[{ required: true }]}
                    extra={
                      <SendCodeButton
                        form={form}
                        onSend={() => setSent(true)}
                      />
                    }
                  >
                    <Input placeholder="请输入" clearable maxLength={6} />
                  </Form.Item>
                </React.Fragment>
              );
            }}
          </Form.Subscribe>
        </Form>
      </Space>
    </div>
  );
};

const SendCodeButton = ({
  form,
  onSend,
}: {
  form: ReturnType<typeof Form.useForm>[0];
  onSend?: () => void;
}) => {
  const { trigger, isMutating } = query.sendCode();
  const timer = useTimer();

  const sendCode = async () => {
    await form.validateFields(['type']);
    const type = form.getFieldValue('type');

    await trigger({ type });
    timer.start(60);
    onSend?.();
  };

  if (isMutating) {
    return <DotLoading />;
  }

  if (timer.status === 'active') {
    return <span>再次发送（{timer.countDown}）</span>;
  }

  return (
    <a style={{ color: 'var(--adm-color-primary)' }} onClick={sendCode}>
      <span>发送验证码</span>
    </a>
  );
};
