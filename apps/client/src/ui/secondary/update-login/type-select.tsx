import { Button, Space } from 'antd-mobile';
import { MailOutline, PhonebookOutline } from 'antd-mobile-icons';

type LoginType = 'email' | 'phone';

export type TypeSelectProps = {
  disabled?: boolean;
  value?: LoginType;
  onChange?: (value: LoginType) => void;
  buttonText?: Record<LoginType, string>;
  options?: LoginType[];
};

export default (props: TypeSelectProps) => {
  const { disabled, value, onChange, buttonText, options } = props;

  return (
    <Space
      block
      direction="vertical"
      style={{ '--gap-vertical': '1rem' }}
      className="my-2"
    >
      {(!options || options.includes('phone')) && (
        <Button
          block
          shape="rounded"
          disabled={disabled}
          className="py-4 px-6"
          color={value === 'phone' ? 'primary' : undefined}
          onClick={disabled ? undefined : () => onChange?.('phone')}
        >
          <Space block align="center">
            <PhonebookOutline className="text-3xl" />
            <span className="text-[1rem]">
              {buttonText?.['phone'] || '手机号码验证'}
            </span>
          </Space>
        </Button>
      )}
      {(!options || options.includes('email')) && (
        <Button
          block
          shape="rounded"
          disabled={disabled}
          className="py-4 px-6"
          color={value === 'email' ? 'primary' : undefined}
          onClick={disabled ? undefined : () => onChange?.('email')}
        >
          <Space block align="center">
            <MailOutline className="text-3xl" />
            <span className="text-[1rem]">
              {buttonText?.['email'] || '电子邮箱验证'}
            </span>
          </Space>
        </Button>
      )}
    </Space>
  );
};
