import * as query from '@/queries/notification';
import { usePagination } from '@/utils/pagination';
import { Badge, InfiniteScroll, Space } from 'antd-mobile';
import dayjs from 'dayjs';
import React from 'react';
import { RouterOutput } from 'server/typings';
import { tabsAtom } from './tabs';

const pageSize = 10;

export default () => {
  const tab = tabsAtom.use();
  const type = tab === 'all' ? undefined : tab;

  const { page, prevPages, useHelpers, nextPage } = usePagination({
    pageSize,
    resetDeps: [],
  });

  const listQuery = query.list({ page, pageSize, type });
  const { data } = listQuery;

  const { hasMore } = useHelpers(data);

  const loadMore = async () => {
    if (listQuery.isLoading || !!listQuery.error) {
      return;
    }

    nextPage();
  };

  return (
    <React.Fragment>
      <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
        {prevPages.map((page) => (
          <Page key={page} params={{ page, pageSize, type }} />
        ))}
        {data?.items?.map((it, index) => (
          <ListItem key={index + prevPages.length} data={it} />
        ))}
      </Space>
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </React.Fragment>
  );
};

const ListItem = ({
  data,
}: {
  data: RouterOutput['notification']['ownList']['items'][number];
}) => {
  const mutation = query.read();

  return (
    <div
      className="bg-white shadow-custom py-6 px-6 rounded-4xl"
      style={{ color: data.read ? 'var(--adm-color-weak)' : undefined }}
      onClick={() => mutation.trigger({ id: data.id })}
    >
      <Space block direction="vertical" style={{ '--gap-vertical': '1.5rem' }}>
        <div className="flex justify-between">
          <span className="text-lg font-medium mr-6">{data.title}</span>
          {!data.read && <Badge content={Badge.dot} className="mt-[0.5625rem]" />}
        </div>
        <div>{data.content}</div>
        <Space block justify="between" align="center">
          <span>{data.key}</span>
          <span>{dayjs(data.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
        </Space>
      </Space>
    </div>
  );
};

const Page = ({ params }: { params: query.ListParams }) => {
  const { data } = query.list(params);

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      {data?.items?.map((it, index) => (
        <ListItem key={index} data={it} />
      ))}
    </Space>
  );
};
