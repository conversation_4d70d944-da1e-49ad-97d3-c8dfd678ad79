import { ReactComponent as AllActive } from '@/assets/all-active.svg';
import { ReactComponent as AllStale } from '@/assets/all-stale.svg';
import { ReactComponent as NewsActive } from '@/assets/news-active.svg';
import { ReactComponent as NewsStale } from '@/assets/news-stale.svg';
import { ReactComponent as ReminderActive } from '@/assets/reminder-active.svg';
import { ReactComponent as ReminderStale } from '@/assets/reminder-stale.svg';
import { ReactComponent as ReportActive } from '@/assets/report-active.svg';
import { ReactComponent as ReportStale } from '@/assets/report-stale.svg';
import SvgWrapper from '@/components/svgWrapper';
import * as query from '@/queries/notification';
import { createAtom } from '@/utils/zustand';
import { Type } from '@portfolio-service/schema/notification';
import { Badge, Space } from 'antd-mobile';
import { HTMLAttributes } from 'react';

type Tab = Type | 'all';

export const tabsAtom = createAtom<Tab>('all');

export default () => {
  const activeKey = tabsAtom.use();

  const allSVG = activeKey === 'all' ? AllActive : AllStale;
  const reminderSVG = activeKey === 'reminder' ? ReminderActive : ReminderStale;
  const reportSVG = activeKey === 'report' ? ReportActive : ReportStale;
  const newsSVG = activeKey === 'news' ? NewsActive : NewsStale;

  const all = <SvgWrapper component={allSVG} />;
  const reminder = <SvgWrapper component={reminderSVG} />;
  const report = <SvgWrapper component={reportSVG} />;
  const news = <SvgWrapper component={newsSVG} />;

  return (
    <Space block justify="between" className="mx-6 my-3">
      <TabItem
        tabKey="all"
        icon={all}
        label="全部"
        onClick={() => tabsAtom.store.setState('all')}
      />
      <TabItem
        tabKey="reminder"
        icon={reminder}
        label="提醒通知"
        onClick={() => tabsAtom.store.setState('reminder')}
      />
      <TabItem
        tabKey="report"
        icon={report}
        label="公告/报告"
        onClick={() => tabsAtom.store.setState('report')}
      />
      <TabItem
        tabKey="news"
        icon={news}
        label="咨询回复"
        onClick={() => tabsAtom.store.setState('news')}
      />
    </Space>
  );
};

type TabProps = HTMLAttributes<HTMLDivElement> & {
  tabKey: Tab;
  icon: React.ReactNode;
  label: React.ReactNode;
};

const TabItem = ({ tabKey, icon, label, ...props }: TabProps) => {
  const activeKey = tabsAtom.use();
  const active = tabKey === activeKey;

  const { data } = query.unreadAmount({
    type: tabKey === 'all' ? undefined : tabKey,
  });

  return (
    <Space direction="vertical">
      <Badge content={data?.amount || undefined}>
        <div
          {...props}
          className="rounded-3xl h-16 w-16 flex justify-center items-center"
          style={{
            backgroundColor: active
              ? 'var(--adm-color-primary)'
              : 'var(--adm-color-box)',
          }}
        >
          {icon}
        </div>
      </Badge>
      <div
        className="text-center font-medium text-xs"
        style={{
          color: active ? 'var(--adm-color-primary)' : 'var(--adm-color-weak)',
        }}
      >
        {label}
      </div>
    </Space>
  );
};
