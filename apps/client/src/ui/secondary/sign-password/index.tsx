import Checkbox from '@/components/checkbox';
import Loading from '@/components/loading';
import * as account from '@/queries/account';
import * as signPasswordQuery from '@/queries/signPassword';
import { cfcaTos } from '@/ui/tos/cfca';
import { popupQueueAtom } from '@/utils/account';
import { Button, Form, Input, Modal, Space } from 'antd-mobile';
import { z } from 'zod';

type Props = {
  title?: string;
  onFinish: (password: string) => unknown | Promise<unknown>;
  loading: boolean;
};

export default (props: Props) => {
  const [form] = Form.useForm();

  const userQuery = account.user();
  const user = userQuery.data;

  const existsQuery = signPasswordQuery.exists();
  const existsParsed = z.boolean().safeParse(existsQuery.data?.exists);

  if (!user || existsQuery.isLoading || !existsParsed.success) {
    return <Loading />;
  }

  const exists = existsParsed.data;

  const checkCheckbox = () => {
    return new Promise<boolean>((resolve) => {
      if (form.getFieldValue('agreeTos')) {
        resolve(true);
        return;
      }

      Modal.show({
        title: (
          <span>
            <span>请阅读并同意</span>
            <a
              style={{ color: 'var(--adm-color-primary)' }}
              onClick={(e) => {
                e.preventDefault();
                cfcaTos.show({ data: user });
              }}
            >
              《CFCA 数字证书服务合同》
            </a>
          </span>
        ),
        bodyStyle: {
          borderRadius: '32px',
          padding: '1.5rem 1.5rem 1rem 1.5rem',
        },
        closeOnAction: true,
        actions: [
          {
            key: 'confirm',
            text: '同意',
            primary: true,
            style: { borderRadius: '9999px' },
            onClick: () => {
              form.setFieldValue('agreeTos', true);
              resolve(true);
            },
          },
          {
            key: 'cancel',
            text: '不同意',
            onClick: () => resolve(false),
          },
        ],
      });
    });
  };

  const onFinish = async () => {
    if (!exists && !(await checkCheckbox())) return;

    const { password } = form.getFieldsValue();
    await props.onFinish(password);
    form.resetFields();
    popupQueueAtom.complete('setSignPassword');
  };

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-4 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '0.75rem' }}
        >
          <div className="text-lg font-medium px-4 pt-4">
            {props.title || (exists ? '修改签署密码' : '设置签署密码')}
          </div>

          <Form
            form={form}
            className="w-full"
            mode="card"
            onFinish={onFinish}
            initialValues={{ agreeTos: true }}
            disabled={props.loading}
            footer={
              <Button
                block
                color="primary"
                fill="solid"
                shape="rounded"
                type="submit"
                loading={props.loading}
              >
                提交
              </Button>
            }
          >
            <Form.Item
              className="bg-white"
              label="签署密码"
              name="password"
              rules={[{ required: true }]}
            >
              <Input
                placeholder="请输入 6 位签署密码"
                type="password"
                clearable
                maxLength={6}
              />
            </Form.Item>
            <Form.Item
              className="bg-white"
              label="再次输入签署密码"
              name="passwordCheck"
              required
              rules={[
                {
                  validator: async (_, value) => {
                    if (!value) {
                      throw new Error('请再次输入签署密码');
                    }

                    if (value !== form.getFieldValue('password')) {
                      throw new Error('与签署密码不一致');
                    }
                  },
                },
              ]}
            >
              <Input
                placeholder="请输入"
                type="password"
                clearable
                maxLength={6}
              />
            </Form.Item>
            {!exists && (
              <Form.Item name="agreeTos" className="bg-white">
                <Checkbox>
                  <span
                    className="text-xs"
                    style={{ color: 'var(--adm-color-weak)' }}
                  >
                    <span>我已阅读并同意</span>
                    <a
                      style={{ color: 'var(--adm-color-primary)' }}
                      onClick={(e) => {
                        e.preventDefault();
                        cfcaTos.show({ data: user });
                      }}
                    >
                      《CFCA 数字证书服务合同》
                    </a>
                  </span>
                </Checkbox>
              </Form.Item>
            )}
          </Form>
        </Space>
      </div>
    </Space>
  );
};
