import Loading from '@/components/loading';
import * as account from '@/queries/account';
import * as signPasswordQuery from '@/queries/signPassword';
import { Button, Space } from 'antd-mobile';
import { useNavigate } from 'umi';
import { z } from 'zod';

export default () => {
  const navigate = useNavigate();

  const userQuery = account.user();
  const user = userQuery.data;

  const existsQuery = signPasswordQuery.exists();
  const existsParsed = z.boolean().safeParse(existsQuery.data?.exists);

  if (!user || existsQuery.isLoading || !existsParsed.success) {
    return <Loading />;
  }

  const exists = existsParsed.data;

  return (
    <Space block direction="vertical" style={{ '--gap-vertical': '1.25rem' }}>
      <div className="bg-white shadow-custom p-8 rounded-4xl">
        <Space
          block
          direction="vertical"
          style={{ '--gap-vertical': '1.5rem' }}
        >
          <div className="text-lg font-medium">数字证书</div>
          <Space block direction="vertical" style={{ '--gap': '0.75rem' }}>
            <Item label="申请人" value={user?.name || '-'} />
            <Item label="证件号码" value={user?.identityNumber || '-'} />
          </Space>
          <Button
            color="primary"
            shape="rounded"
            block
            onClick={() => navigate('/secondary/sign-password')}
          >
            {exists ? '修改签署密码' : '设置签署密码'}
          </Button>
        </Space>
      </div>
    </Space>
  );
};

type ItemProps = {
  label: React.ReactNode;
  value: React.ReactNode;
};

const Item = (props: ItemProps) => {
  return (
    <div className="flex justify-between">
      <span
        className="break-keep mr-6"
        style={{ color: 'var(--adm-color-weak)' }}
      >
        {props.label}
      </span>
      <span
        className="break-all text-right"
        style={{ color: 'var(--adm-color-text-secondary)' }}
      >
        {props.value || '-'}
      </span>
    </div>
  );
};
