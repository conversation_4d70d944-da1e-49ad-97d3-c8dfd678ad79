import { NavBar } from 'antd-mobile';
import { useMemo } from 'react';
import { history, useLocation, useNavigate } from 'umi';

export default () => {
  const navigate = useNavigate();
  const pathname = useLocation().pathname;

  const isTrade = pathname === '/secondary/ta/trade';
  const isDividend = pathname === '/secondary/ta/dividend';

  const className = isTrade || isDividend ? undefined : 'shadow-nav';
  const bg = isTrade || isDividend ? 'var(--adm-color-primary)' : '#fafbfc';
  const color = isTrade || isDividend ? 'white' : undefined;

  const title = useMemo(() => {
    switch (pathname) {
      case '/secondary/notification':
        return '通知';
      case '/secondary/appropriateness':
        return '适当性';
      case '/secondary/contract':
        return '电子签';
    }
  }, [pathname]);

  const onBack = () => {
    if (
      pathname === '/secondary/notification' ||
      pathname === '/secondary/appropriateness' ||
      pathname === '/secondary/contract'
    ) {
      navigate('/main/home');
    } else if (
      pathname.startsWith('/secondary/sign-password') ||
      pathname === '/secondary/risk-test/landing'
    ) {
      navigate('/main/account');
    } else {
      history.back();
    }
  };

  return (
    <NavBar
      className={className}
      style={{ backgroundColor: bg, color, '--height': '2.8125rem' }}
      back="返回"
      onBack={onBack}
    >
      {title}
    </NavBar>
  );
};
