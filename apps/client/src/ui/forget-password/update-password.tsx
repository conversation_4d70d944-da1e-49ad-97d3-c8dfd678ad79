import Loading from '@/components/loading';
import * as query from '@/queries/forgetPassword';
import { Button, Form, Input } from 'antd-mobile';
import { useEffect } from 'react';
import { useNavigate } from 'umi';
import ForgetPasswordStep from './forget-password-step';

const UpdatePassword = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const mutation = query.forgetPassword();

  const onFinish = async () => {
    const { password } = form.getFieldsValue();
    await mutation.trigger({ password });
    navigate('/auth');
  };

  return (
    <div className="h-full py-10">
      <div>
        <ForgetPasswordStep step="重置密码" />
      </div>
      <div
        className="flex justify-center items-center px-6"
        style={{ height: 'calc(100% - 3.84375rem)' }}
      >
        <Form
          className="w-full"
          mode="card"
          form={form}
          onFinish={onFinish}
          footer={
            <Button
              block
              color="primary"
              fill="solid"
              type="submit"
              size="large"
              shape="rounded"
              loading={mutation.isMutating}
            >
              提交
            </Button>
          }
        >
          <Form.Header>重置密码</Form.Header>
          <Form.Item
            label="新密码"
            name="password"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" type="password" clearable />
          </Form.Item>
          <Form.Item
            label="再次输入新密码"
            name="passwordCheck"
            required
            rules={[
              {
                validator: async (_, value) => {
                  if (!value) {
                    throw new Error('请再次输入新密码');
                  }

                  if (value !== form.getFieldValue('password')) {
                    throw new Error('与新密码不一致');
                  }
                },
              },
            ]}
          >
            <Input placeholder="请输入" type="password" clearable />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default () => {
  const nagivate = useNavigate();

  const statusQuery = query.validationPassed();
  const loading = statusQuery.isLoading;
  const status = statusQuery.data?.status;

  useEffect(() => {
    if (loading || !!status) return;
    nagivate('/forget-password');
  }, [loading, status]);

  if (loading || !status) {
    return <Loading />;
  }

  return <UpdatePassword />;
};
