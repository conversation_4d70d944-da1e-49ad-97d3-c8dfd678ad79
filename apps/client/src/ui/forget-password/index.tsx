import Checkbox from '@/components/checkbox';
import TOSButtons from '@/ui/tos/tos-buttons';
import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, Form, Input, Modal, Space } from 'antd-mobile';
import { RouterInput } from 'server/typings';
import useSWRMutation from 'swr/mutation';
import { useNavigate } from 'umi';
import ForgetPasswordStep from './forget-password-step';

const redirectUrl = `${location.origin}/forget-password/identity-validation/redirect`;

export default () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const linkQuery = useSWRMutation(
    voidKey,
    async (
      _,
      { arg }: { arg: RouterInput['auth']['forgetPasswordFaceRecognitionUrl'] },
    ) => trpc.auth.forgetPasswordFaceRecognitionUrl.mutate(arg),
    { onSuccess: ({ url }) => (location.href = url) },
  );

  const checkCheckbox = () => {
    return new Promise<boolean>((resolve) => {
      if (form.getFieldValue('agreeTos')) {
        resolve(true);
        return;
      }

      Modal.show({
        title: (
          <span>
            <span>请阅读并同意</span>
            <TOSButtons />
          </span>
        ),
        bodyStyle: {
          borderRadius: '32px',
          padding: '1.5rem 1.5rem 1rem 1.5rem',
        },
        closeOnAction: true,
        actions: [
          {
            key: 'confirm',
            text: '同意',
            primary: true,
            style: { borderRadius: '9999px' },
            onClick: () => {
              form.setFieldValue('agreeTos', true);
              resolve(true);
            },
          },
          {
            key: 'cancel',
            text: '不同意',
            onClick: () => resolve(false),
          },
        ],
      });
    });
  };

  const onFinish = async () => {
    if (!(await checkCheckbox())) return;

    const { identityNumber } = form.getFieldsValue();
    await linkQuery.trigger({ identityNumber, redirectUrl });
  };

  return (
    <div className="h-full py-10">
      <div>
        <ForgetPasswordStep step="身份认证" />
      </div>
      <div
        className="flex justify-center items-center px-6"
        style={{ height: 'calc(100% - 3.84375rem)' }}
      >
        <Form
          className="w-full"
          mode="card"
          form={form}
          onFinish={onFinish}
          initialValues={{ agreeTos: true }}
          footer={
            <Space
              block
              direction="vertical"
              style={{ '--gap-vertical': '1rem' }}
            >
              <Button
                block
                color="primary"
                fill="solid"
                type="submit"
                size="large"
                shape="rounded"
                loading={linkQuery.isMutating}
              >
                下一步
              </Button>
              <Space block justify="center">
                <a
                  style={{ color: 'var(--adm-color-primary)' }}
                  onClick={() => navigate('/auth')}
                >
                  登录
                </a>
              </Space>
            </Space>
          }
        >
          <Form.Header>身份认证</Form.Header>
          <Form.Item
            label="证件号码"
            name="identityNumber"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入" clearable />
          </Form.Item>
          <Form.Item name="agreeTos">
            <Checkbox>
              <span
                className="text-xs"
                style={{ color: 'var(--adm-color-weak)' }}
              >
                <span>我已阅读并同意</span>
                <TOSButtons />
              </span>
            </Checkbox>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};
