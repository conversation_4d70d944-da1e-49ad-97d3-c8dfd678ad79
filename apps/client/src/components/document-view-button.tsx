import { voidKey } from '@/utils/swr';
import { trpc } from '@/utils/trpc';
import { Button, ButtonProps } from 'antd-mobile';
import type { RouterOutput } from 'server/typings';
import useSWRMutation from 'swr/mutation';
import { history } from 'umi';

type Props = ButtonProps & {
  document: RouterOutput['document']['byId'];
};

export default ({ document, ...props }: Props) => {
  const linkQuery = useSWRMutation(
    voidKey,
    async (_, { arg }: { arg: { document: Props['document'] } }) => {
      const { id, fileUploadId } = arg.document;
      if (!!document.fileUploadId) {
        return `/file-viewer?id=${fileUploadId}`;
      }

      const response = await trpc.document.ttdLinkById.query({ id });
      return response.url;
    },
    { onSuccess: (url) => history.push(url) },
  );

  return (
    <Button
      loading={linkQuery.isMutating}
      onClick={() => linkQuery.trigger({ document })}
      {...props}
    />
  );
};
