import { DatePicker, DatePickerProps, Form, FormItemProps } from 'antd-mobile';
import dayjs from 'dayjs';

type Props = FormItemProps & {
  pickerProps?: DatePickerProps;
  placeHolder?: string;
};

export default ({ pickerProps, placeHolder, ...props }: Props) => {
  return (
    <Form.Item
      {...props}
      onClick={(_, ref) => ref.current?.open()}
      trigger="onConfirm"
    >
      <DatePicker {...(pickerProps || {})}>
        {(value) => (value ? dayjs(value).format('YYYY-MM-DD') : '请选择')}
      </DatePicker>
    </Form.Item>
  );
};
