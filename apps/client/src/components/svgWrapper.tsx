import { scaleAtom } from '@/store';
import { FunctionComponent, SVGProps } from 'react';

type Props = SVGProps<SVGSVGElement> & { title?: string };

type WrapperProps = Props & {
  component: FunctionComponent<Props>;
};

export default ({ component, ...rest }: WrapperProps) => {
  const Component = component;

  const scale = scaleAtom.use();
  const transform = scale == null ? undefined : `scale(${scale})`;

  return <Component {...rest} transform={transform} />;
};
