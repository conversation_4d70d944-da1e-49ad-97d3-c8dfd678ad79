import React from 'react';
import { useLocation } from 'umi';

export default ({ style, ...rest }: React.HTMLAttributes<HTMLDivElement>) => {
  const { pathname } = useLocation();
  const isAuthPage = pathname.startsWith('/auth');
  const isRegisterPage = pathname.startsWith('/register');
  const showTabs = /^\/main\/(home|portfolio|holding|account)$/.test(pathname);
  const showNav = !isAuthPage && !isRegisterPage && !showTabs;

  const height = showNav ? 2.8125 : 0;

  return <div {...rest} style={{ height: `${height}rem`, ...(style || {}) }} />;
};
