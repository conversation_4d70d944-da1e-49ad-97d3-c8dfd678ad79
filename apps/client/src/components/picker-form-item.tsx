import { Form, FormItemProps, Picker, PickerProps } from 'antd-mobile';

type Props = FormItemProps & {
  pickerProps: PickerProps;
  placeHolder?: string;
};

export default ({ pickerProps, placeHolder, ...props }: Props) => {
  return (
    <Form.Item
      {...props}
      onClick={(_, ref) => ref.current?.open()}
      trigger="onConfirm"
    >
      <Picker {...pickerProps}>
        {(values) => {
          const valid = values.filter((it) => !!it);
          if (!valid?.length) {
            return <span style={{ color: '#cccccc' }}>请选择</span>;
          }

          return valid.map((it) => it?.label).join(' - ');
        }}
      </Picker>
    </Form.Item>
  );
};
