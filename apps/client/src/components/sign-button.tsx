import * as signPasswordQuery from '@/queries/signPassword';
import { Button, ButtonProps, Form, Input, Modal } from 'antd-mobile';
import { useNavigate } from 'umi';
import { z } from 'zod';

type SignButtonProps = ButtonProps & {
  from?: 'appropriateness' | 'contract';
  onSubmit: (password: string) => unknown | Promise<unknown>;
};

export const SignButton = ({
  onSubmit,
  loading,
  className,
  ...props
}: SignButtonProps) => {
  const navigate = useNavigate();

  const [form] = Form.useForm();

  const existsQuery = signPasswordQuery.exists();
  const existsParsed = z.boolean().safeParse(existsQuery.data?.exists);

  const sign = () => {
    const handler = Modal.show({
      bodyStyle: {
        borderRadius: '32px',
        padding: '1.5rem 0.75rem 1rem 0.75rem',
      },
      afterClose: () => form.resetFields(),
      actions: [
        {
          key: 'submit',
          text: '提交',
          primary: true,
          onClick: async () => {
            await form.validateFields();

            const password = form.getFieldValue('password');
            await onSubmit(password);
          },
          style: {
            borderRadius: '9999px',
            width: 'calc(100% - 1.5rem)',
            marginLeft: 12,
          },
        },
        {
          key: 'cancel',
          text: '取消',
          onClick: () => handler.close(),
        },
      ],
      title: <div className="px-3">请输入 6 位签署密码</div>,
      content: (
        <Form form={form} mode="card" className="w-full">
          <Form.Item
            className="bg-white"
            label="签署密码"
            name="password"
            required
            rules={[
              {
                validator: async (_, value) => {
                  if (!value) {
                    throw new Error('请输入签署密码');
                  }

                  if (
                    !z.string().trim().min(6).max(6).safeParse(value).success
                  ) {
                    throw new Error('格式不正确');
                  }
                },
              },
            ]}
          >
            <Input
              placeholder="请输入"
              type="password"
              maxLength={6}
              clearable
            />
          </Form.Item>
          <div className="text-sm text-slate-400 text-center mt-4">
            签署状态更新可能会有 5 ~ 10 分钟延迟。如已签署，请稍等片刻。
          </div>
        </Form>
      ),
    });
  };

  const redirect = () => {
    Modal.show({
      bodyStyle: {
        borderRadius: '32px',
        padding: '1.5rem 1.5rem 1rem 1.5rem',
      },
      closeOnAction: true,
      actions: [
        {
          key: 'confirm',
          text: '前往设置',
          primary: true,
          style: { borderRadius: '9999px' },
          onClick: () => {
            const searchString = props.from ? `?from=${props.from}` : '';
            navigate(`/secondary/sign-password${searchString}`);
          },
        },
      ],
      title: '签署前，请先设置签署密码',
    });
  };

  const onClick = () => {
    if (!existsParsed.success) return;
    const exists = existsParsed.data;

    if (exists) {
      return sign();
    } else {
      return redirect();
    }
  };

  return (
    <Button
      {...props}
      className={`break-keep ${className || ''}`}
      loading={existsQuery.isLoading || !existsParsed.success}
      onClick={onClick}
    />
  );
};
